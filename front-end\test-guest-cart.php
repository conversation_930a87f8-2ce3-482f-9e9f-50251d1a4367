<?php
session_start();
require_once '../functions/server.php';

// Test script to verify guest cart functionality
echo "<h1>Guest Cart Test</h1>";

// Clear any existing guest cart
unset($_SESSION['guest_cart']);

echo "<h2>1. Testing Guest Cart Item Addition</h2>";

// Simulate adding STARTER XS (ticket_id: 1001, price: $20)
$_POST['ticket_id'] = 1001;
$_POST['quantity'] = 1;

// Get ticket data
$ticket_result = mysqli_query($conn, "SELECT * FROM tickets WHERE ticketid = '1001'");
$ticket_xs = mysqli_fetch_assoc($ticket_result);

if ($ticket_xs) {
    $_SESSION['guest_cart'][] = [
        'ticket_id' => 1001,
        'quantity' => 1,
        'ticket_type' => $ticket_xs['ticket_type'],
        'package_size' => $ticket_xs['package_size'],
        'numbers_per_package' => $ticket_xs['numbers_per_package'],
        'dollar_price_per_package' => $ticket_xs['dollar_price_per_package'],
        'cart_item_id' => 'guest_' . time() . '_1001'
    ];
    echo "✓ Added STARTER XS: $" . $ticket_xs['dollar_price_per_package'] . "<br>";
}

// Simulate adding STARTER S (ticket_id: 1002, price: $150)
$ticket_result = mysqli_query($conn, "SELECT * FROM tickets WHERE ticketid = '1002'");
$ticket_s = mysqli_fetch_assoc($ticket_result);

if ($ticket_s) {
    $_SESSION['guest_cart'][] = [
        'ticket_id' => 1002,
        'quantity' => 1,
        'ticket_type' => $ticket_s['ticket_type'],
        'package_size' => $ticket_s['package_size'],
        'numbers_per_package' => $ticket_s['numbers_per_package'],
        'dollar_price_per_package' => $ticket_s['dollar_price_per_package'],
        'cart_item_id' => 'guest_' . time() . '_1002'
    ];
    echo "✓ Added STARTER S: $" . $ticket_s['dollar_price_per_package'] . "<br>";
}

echo "<h2>2. Current Guest Cart Contents</h2>";
echo "<pre>";
print_r($_SESSION['guest_cart']);
echo "</pre>";

echo "<h2>3. Testing Line Items Creation (Simulated)</h2>";

// Simulate the line items creation logic from create-checkout-session.php
$cart_items = $_SESSION['guest_cart'];
$line_items = [];

foreach ($cart_items as $item) {
    // Create descriptive product name that includes package size
    $product_name = $item['ticket_type'];
    if (!empty($item['package_size'])) {
        $product_name .= ' ' . $item['package_size'];
    }
    
    $product_data = [
        'name' => $product_name
    ];
    if (!empty($item['package_size'])) {
        $product_data['description'] = 'Package Size: ' . $item['package_size'] . ' (' . ($item['numbers_per_package'] ?? 1) . ' tickets)';
    }
    
    $line_items[] = [
        'price_data' => [
            'currency' => 'usd',
            'product_data' => $product_data,
            'unit_amount' => $item['dollar_price_per_package'] * 100,
        ],
        'quantity' => $item['quantity'],
    ];
}

echo "<h3>Generated Line Items for Stripe:</h3>";
foreach ($line_items as $index => $line_item) {
    echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 5px;'>";
    echo "<strong>Line Item " . ($index + 1) . ":</strong><br>";
    echo "Product Name: " . $line_item['price_data']['product_data']['name'] . "<br>";
    if (isset($line_item['price_data']['product_data']['description'])) {
        echo "Description: " . $line_item['price_data']['product_data']['description'] . "<br>";
    }
    echo "Price: $" . ($line_item['price_data']['unit_amount'] / 100) . "<br>";
    echo "Quantity: " . $line_item['quantity'] . "<br>";
    echo "Total: $" . (($line_item['price_data']['unit_amount'] / 100) * $line_item['quantity']) . "<br>";
    echo "</div>";
}

$total_amount = 0;
foreach ($line_items as $line_item) {
    $total_amount += ($line_item['price_data']['unit_amount'] / 100) * $line_item['quantity'];
}

echo "<h3>Expected Results:</h3>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<strong>✓ Should show 2 separate line items in Stripe checkout:</strong><br>";
echo "1. STARTER XS - $20.00 x 1 = $20.00<br>";
echo "2. STARTER S - $150.00 x 1 = $150.00<br>";
echo "<strong>Total: $" . number_format($total_amount, 2) . "</strong><br>";
echo "</div>";

echo "<h3>Previous Issue (Fixed):</h3>";
echo "<div style='background: #ffe8e8; padding: 15px; border-radius: 5px;'>";
echo "<strong>✗ Previously would show:</strong><br>";
echo "STARTER xs x2 - $40.00 (incorrectly grouped)<br>";
echo "</div>";

echo "<h2>4. Cart Display Test</h2>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Product</th><th>Price</th><th>Quantity</th><th>Total</th></tr>";

foreach ($cart_items as $item) {
    $product_name = $item['ticket_type'];
    if (!empty($item['package_size'])) {
        $product_name .= ' ' . $item['package_size'];
    }
    
    echo "<tr>";
    echo "<td>" . htmlspecialchars($product_name) . "</td>";
    echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
    echo "<td>" . $item['quantity'] . "</td>";
    echo "<td>$" . number_format($item['quantity'] * $item['dollar_price_per_package'], 2) . "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>5. Test Summary</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<strong>✓ Guest cart now stores package_size and numbers_per_package</strong><br>";
echo "<strong>✓ Line items are created separately for different package sizes</strong><br>";
echo "<strong>✓ Product names include package size (e.g., 'STARTER XS', 'STARTER S')</strong><br>";
echo "<strong>✓ Cart display shows package sizes correctly</strong><br>";
echo "<strong>✓ No more incorrect grouping of different ticket types</strong><br>";
echo "</div>";

// Clean up test data
unset($_SESSION['guest_cart']);
echo "<br><em>Test completed. Guest cart cleared.</em>";
?>
