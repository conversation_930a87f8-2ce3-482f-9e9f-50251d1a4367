<?php
/**
 * Test Address2 Field Fix
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Address2 Field Fix</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Issue Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ The Problem:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Missing address2 extraction:</strong> address->line2 not being extracted</li>";
echo "<li>✅ <strong>Missing address2 in INSERT:</strong> address2 field not included in user creation</li>";
echo "<li>✅ <strong>Inconsistent with webhook:</strong> Webhook saves address2, fallback doesn't</li>";
echo "</ul>";

echo "<h4>✅ The Solution:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Extract address2:</strong> Get address->line2 from Stripe customer details</li>";
echo "<li>✅ <strong>Include in INSERT:</strong> Add address2 field to user creation query</li>";
echo "<li>✅ <strong>Match webhook behavior:</strong> Same address handling as webhook</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Code Changes:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Before (Missing address2):</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "\$address = \$customer_details->address ?? null;\n";
echo "\$address1 = \$address->line1 ?? '';\n";
echo "// ❌ Missing: \$address2 = \$address->line2 ?? '';\n";
echo "\$city = \$address->city ?? '';\n\n";
echo "// ❌ Missing address2 in INSERT\n";
echo "INSERT INTO user (username, email, password, address, city, ...)\n";
echo "VALUES (?, ?, ?, ?, ?, ...)";
echo "</pre>";

echo "<h4>🔧 After (Complete address handling):</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "\$address = \$customer_details->address ?? null;\n";
echo "\$address1 = \$address->line1 ?? '';\n";
echo "\$address2 = \$address->line2 ?? ''; // ✅ Added\n";
echo "\$city = \$address->city ?? '';\n\n";
echo "// ✅ Complete INSERT with address2\n";
echo "INSERT INTO user (username, email, password, address, address2, city, ...)\n";
echo "VALUES (?, ?, ?, ?, ?, ?, ...)";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Address Field Mapping:</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 Stripe to Database Mapping:</h4>";
echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Stripe Field</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Database Field</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Description</th>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address->line1</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Primary address line</td>";
echo "</tr>";
echo "<tr style='background: #e8f5e8;'>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>address->line2</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>address2</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>Secondary address line (FIXED)</strong></td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address->city</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>city</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>City</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address->state</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>state</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>State/Province</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address->postal_code</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>postal_code</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>ZIP/Postal Code</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address->country</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>country</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>Country</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h3>Consistency Check:</h3>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 Webhook vs Fallback Comparison:</h4>";
echo "<table style='width: 100%; border-collapse: collapse; margin: 10px 0;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Field</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Webhook (stripe-webhook.php)</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Fallback (payment-success.php)</th>";
echo "<th style='border: 1px solid #ddd; padding: 8px; text-align: left;'>Status</th>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>address</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$address1</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$address1</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Consistent</td>";
echo "</tr>";
echo "<tr style='background: #e8f5e8;'>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'><strong>address2</strong></td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$address2</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$address2 (FIXED)</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ <strong>Now Consistent</strong></td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>city</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$city</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$city</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Consistent</td>";
echo "</tr>";
echo "<tr>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>state</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$state</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ \$state</td>";
echo "<td style='border: 1px solid #ddd; padding: 8px;'>✅ Consistent</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

if ($is_localhost) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test Address2 on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Add items to cart</strong> and proceed to checkout</li>";
    echo "<li><strong>Fill billing address</strong> with both address lines:";
    echo "<ul>";
    echo "<li>Address line 1: e.g., '123 Main Street'</li>";
    echo "<li>Address line 2: e.g., 'Apt 4B' or 'Suite 200'</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Complete purchase</strong> → Should process without errors</li>";
    echo "<li><strong>Check database</strong> → user table should have both address and address2 populated</li>";
    echo "<li><strong>Verify data</strong> → Both address lines should be saved correctly</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>Production uses webhook processing which already handles address2 correctly.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Database Verification:</h3>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 Check User Table:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "SELECT username, address, address2, city, state, postal_code, country\n";
echo "FROM user\n";
echo "WHERE username LIKE 'user%'\n";
echo "ORDER BY id DESC\n";
echo "LIMIT 5;";
echo "</pre>";

echo "<h4>📝 Expected Results:</h4>";
echo "<ul>";
echo "<li><strong>address:</strong> Contains line1 (e.g., '123 Main Street')</li>";
echo "<li><strong>address2:</strong> Contains line2 (e.g., 'Apt 4B') - NOT empty anymore</li>";
echo "<li><strong>Other fields:</strong> city, state, postal_code, country all populated</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>address2 field populated:</strong> No longer empty for non-login purchases</li>";
echo "<li>✅ <strong>Complete address data:</strong> Both address lines saved correctly</li>";
echo "<li>✅ <strong>Consistent behavior:</strong> Webhook and fallback now handle addresses the same way</li>";
echo "<li>✅ <strong>Better user data:</strong> More complete address information for customer records</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Summary:</h3>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Address2 field is now properly saved!</strong></p>";
echo "<ul>";
echo "<li><strong>Root cause:</strong> Missing address2 extraction and database insertion</li>";
echo "<li><strong>Solution:</strong> Extract address->line2 and include address2 in INSERT statement</li>";
echo "<li><strong>Result:</strong> Complete address data saved for all non-login purchases</li>";
echo "</ul>";
echo "<p><strong>Your non-login user addresses will now be complete with both address lines!</strong></p>";
echo "</div>";
?>
