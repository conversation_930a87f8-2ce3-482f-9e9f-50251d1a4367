<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database connection
include('../functions/server.php');

// Load centralized API configuration for Appika GraphQL
require_once '../config/api-config.php';
require_once '../vendor/autoload.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

// Create a Guzzle HTTP client for GraphQL
$client = new \GuzzleHttp\Client([
    'base_uri' => $graphqlEndpoint,
    'timeout' => 30,
    'http_errors' => false,
]);

// Function to make GraphQL requests
function makeGraphQLRequest($client, $query, $variables = []) {
    global $apiKey;

    try {
        $requestBody = [
            'query' => $query
        ];

        if (!empty($variables)) {
            $requestBody['variables'] = $variables;
        }

        $response = $client->request('POST', '', [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => $requestBody
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        return [
            'status' => $statusCode,
            'data' => $data
        ];
    } catch (\Exception $e) {
        return [
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Log the request for debugging
$log_file = fopen("ticket_status_update.log", "a");
fwrite($log_file, "Request received at " . date('Y-m-d H:i:s') . "\n");
fwrite($log_file, "POST data: " . print_r($_POST, true) . "\n");

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    echo json_encode(['status' => 'error', 'message' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];
fwrite($log_file, "Admin: $admin_username (ID: $admin_id)\n");

// Check if it's a GET request with redirect parameter
$redirect_mode = false;
if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['redirect'])) {
    fwrite($log_file, "GET request with redirect parameter detected\n");
    $ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;
    $status = isset($_GET['status']) ? $_GET['status'] : null;
    $redirect_mode = true;
}
// Check if it's a POST request
else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    fwrite($log_file, "POST request detected\n");
    $ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : null;
    $status = isset($_POST['status']) ? $_POST['status'] : null;
}
// Invalid request method
else {
    fwrite($log_file, "Error: Invalid request method\n\n");
    echo json_encode(['success' => false, 'error' => 'Invalid request method']);
    fclose($log_file);
    exit();
}

fwrite($log_file, "Ticket ID: $ticket_id, Status: $status, Redirect Mode: " . ($redirect_mode ? "Yes" : "No") . "\n");

// Validate data
if (!$ticket_id || !$status) {
    fwrite($log_file, "Error: Missing required data\n\n");
    echo json_encode(['success' => false, 'error' => 'Missing required data']);
    fclose($log_file);
    exit();
}

// Validate status
$allowed_statuses = ['open', 'in_progress', 'resolved', 'closed'];
if (!in_array($status, $allowed_statuses)) {
    fwrite($log_file, "Error: Invalid status\n\n");
    echo json_encode(['success' => false, 'error' => 'Invalid status']);
    fclose($log_file);
    exit();
}

// Check if ticket exists and get current status, user_id, and appika_id
$check_query = "SELECT st.status, st.user_id, st.subject, st.ticket_type, st.appika_id, u.email
                FROM support_tickets st
                JOIN user u ON st.user_id = u.id
                WHERE st.id = ?";
$stmt = mysqli_prepare($conn, $check_query);
mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);
$ticket_info = mysqli_fetch_assoc($result);

fwrite($log_file, "Checking if ticket exists...\n");
if (!$ticket_info) {
    fwrite($log_file, "Error: Ticket not found\n\n");
    mysqli_stmt_close($stmt);
    echo json_encode(['success' => false, 'error' => 'Ticket not found']);
    fclose($log_file);
    exit();
}

$user_id = $ticket_info['user_id'];
$current_status = $ticket_info['status'];
fwrite($log_file, "Ticket found. User ID: $user_id, Current Status: $current_status\n");

mysqli_stmt_close($stmt);
fwrite($log_file, "Ticket found. Proceeding with update...\n");

// Update ticket status
$update_query = "UPDATE support_tickets SET status = ? WHERE id = ?";
$stmt = mysqli_prepare($conn, $update_query);
mysqli_stmt_bind_param($stmt, 'si', $status, $ticket_id);
$success = mysqli_stmt_execute($stmt);
fwrite($log_file, "Update query executed. Success: " . ($success ? "Yes" : "No") . "\n");
if (!$success) {
    fwrite($log_file, "MySQL Error: " . mysqli_error($conn) . "\n");
}
mysqli_stmt_close($stmt);

if ($success) {
    // Log the status change with UTC time
    $created_at_utc = getCurrentUTC(); // Get UTC time for consistent storage
    $log_query = "INSERT INTO ticket_logs (ticket_id, user_id, action, description, performed_by_admin_id, created_at)
                 VALUES (?, ?, 'status_change', ?, ?, ?)";
    $description = "Ticket status changed to " . ucfirst($status) . " by admin";
    $stmt = mysqli_prepare($conn, $log_query);
    mysqli_stmt_bind_param($stmt, 'iisis', $ticket_id, $user_id, $description, $admin_id, $created_at_utc);
    $log_success = mysqli_stmt_execute($stmt);

    if (!$log_success) {
        fwrite($log_file, "Error logging status change: " . mysqli_error($conn) . "\n");
        // Continue even if logging fails - the status update was successful
        fwrite($log_file, "Continuing despite log error - status update was successful\n");
    } else {
        fwrite($log_file, "Logging status change successful\n");
    }

    mysqli_stmt_close($stmt);

    // Update Appika API if ticket has appika_id
    $appika_update_success = true;
    $appika_error = '';

    if (!empty($ticket_info['appika_id'])) {
        fwrite($log_file, "Updating Appika API for ticket with appika_id: " . $ticket_info['appika_id'] . "\n");

        try {
            // Map local status to Appika API status
            $statusMapping = [
                'open' => 'OPEN',
                'in_progress' => 'WIP',
                'resolved' => 'CLOSED',
                'closed' => 'CLOSED'
            ];
            $apiStatus = $statusMapping[$status] ?? 'OPEN';

            // Map ticket type to API type
            $typeMapping = [
                'starter' => 1,
                'premium' => 2,
                'ultimate' => 3
            ];
            $apiType = $typeMapping[$ticket_info['ticket_type']] ?? 1;

            // Extract numeric ID from appika_id (e.g., HT076 -> 76)
            $numericId = (int)filter_var($ticket_info['appika_id'], FILTER_SANITIZE_NUMBER_INT);

            if ($numericId > 0) {
                fwrite($log_file, "Extracted numeric ID: $numericId from appika_id\n");

                // GraphQL mutation to update ticket in Appika
                $mutation = '
                mutation UpdateTicket(
                  $id: Int!,
                  $contact_id: Int,
                  $agent_id: Int,
                  $subject: String!,
                  $type: Int!,
                  $type_name: String,
                  $priority: String!,
                  $status: String!,
                  $req_email: String,
                  $time_track: String!,
                  $reply_msg: String,
                  $tags: String
                ) {
                  updateTicket(
                    id: $id,
                    contact_id: $contact_id,
                    agent_id: $agent_id,
                    subject: $subject,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    req_email: $req_email,
                    time_track: $time_track,
                    reply_msg: $reply_msg,
                    tags: $tags
                  ) {
                    id
                    ticket_no
                    status
                    priority
                    updated
                  }
                }';

                $variables = [
                    'id' => $numericId,
                    'contact_id' => null,
                    'agent_id' => $admin_id,
                    'subject' => $ticket_info['subject'],
                    'type' => $apiType,
                    'type_name' => $ticket_info['ticket_type'],
                    'priority' => 'LOW', // Default priority for status-only updates
                    'status' => $apiStatus,
                    'req_email' => $ticket_info['email'],
                    'time_track' => '00:00:00',
                    'reply_msg' => "Status updated to {$status} by admin {$admin_username}",
                    'tags' => ''
                ];

                fwrite($log_file, "Sending GraphQL mutation with variables: " . json_encode($variables) . "\n");

                $result = makeGraphQLRequest($client, $mutation, $variables);

                fwrite($log_file, "GraphQL response status: " . $result['status'] . "\n");
                fwrite($log_file, "GraphQL response data: " . json_encode($result['data']) . "\n");

                if ($result['status'] < 200 || $result['status'] >= 300) {
                    $appika_update_success = false;
                    $appika_error = $result['error'] ?? 'Unknown GraphQL error';
                    if (isset($result['data']['errors'])) {
                        $appika_error = $result['data']['errors'][0]['message'] ?? $appika_error;
                    }
                    fwrite($log_file, "Appika API update failed: " . $appika_error . "\n");
                } else {
                    fwrite($log_file, "Appika API update successful\n");
                }
            } else {
                $appika_update_success = false;
                $appika_error = "Invalid appika_id format: " . $ticket_info['appika_id'];
                fwrite($log_file, $appika_error . "\n");
            }
        } catch (Exception $e) {
            $appika_update_success = false;
            $appika_error = $e->getMessage();
            fwrite($log_file, "Exception during Appika API update: " . $appika_error . "\n");
        }
    } else {
        fwrite($log_file, "No appika_id found, skipping Appika API update\n");
    }

    // Get updated ticket info
    $ticket_query = "SELECT id, status FROM support_tickets WHERE id = ?";
    $stmt = mysqli_prepare($conn, $ticket_query);
    mysqli_stmt_bind_param($stmt, 'i', $ticket_id);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    $ticket_data = mysqli_fetch_assoc($result);
    mysqli_stmt_close($stmt);

    // Handle success response
    if ($redirect_mode) {
        // If in redirect mode, redirect back to the chat page with success message
        $success_message = $appika_update_success
            ? "Ticket status updated to " . ucfirst($status) . " in both local database and Appika API"
            : "Ticket status updated to " . ucfirst($status) . " in local database, but failed to update Appika API: " . $appika_error;

        $redirect_url = "admin-chat.php?user_id=" . (isset($_GET['user_id']) ? intval($_GET['user_id']) : '') .
                        "&ticket_id=$ticket_id&success=1&message=" . urlencode($success_message);
        fwrite($log_file, "Success! Redirecting to: $redirect_url\n\n");
        fclose($log_file);
        header("Location: $redirect_url");
        exit();
    } else {
        // If in AJAX mode, return JSON
        $response = [
            'success' => true,
            'message' => $appika_update_success
                ? 'Ticket status updated successfully in both local database and Appika API'
                : 'Ticket status updated in local database, but failed to update Appika API: ' . $appika_error,
            'ticket' => $ticket_data,
            'appika_success' => $appika_update_success,
            'appika_error' => $appika_error
        ];

        fwrite($log_file, "Success response: " . json_encode($response) . "\n\n");
        echo json_encode($response);
    }
} else {
    // Handle error response
    $error_message = 'Failed to update ticket status: ' . mysqli_error($conn);

    if ($redirect_mode) {
        // If in redirect mode, redirect back with error parameter
        $redirect_url = "admin-chat.php?user_id=" . (isset($_GET['user_id']) ? intval($_GET['user_id']) : '') . "&ticket_id=$ticket_id&error=" . urlencode($error_message);
        fwrite($log_file, "Error! Redirecting to: $redirect_url\n\n");
        fclose($log_file);
        header("Location: $redirect_url");
        exit();
    } else {
        // If in AJAX mode, return JSON error
        $error_response = [
            'success' => false,
            'error' => $error_message
        ];

        fwrite($log_file, "Error response: " . json_encode($error_response) . "\n\n");
        echo json_encode($error_response);
    }
}

fclose($log_file);
?>
