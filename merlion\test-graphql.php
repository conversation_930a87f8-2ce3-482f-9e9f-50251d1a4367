<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

// Include GraphQL functions
include('../functions/graphql_functions.php');

$admin_username = $_SESSION['admin_username'];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Connection Test</title>
    <link rel="stylesheet" href="../css/bootstrap.css">
    <style>
        body { padding: 20px; background-color: #f8f9fa; }
        .container { max-width: 800px; }
        .test-result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>GraphQL API Connection Test</h1>
        <p>Welcome, <?php echo htmlspecialchars($admin_username); ?>!</p>
        
        <div class="test-result info">
            <h3>Testing GraphQL Connection...</h3>
            <p>Attempting to connect to Appika GraphQL API and fetch tickets.</p>
        </div>

        <?php
        // Test 1: Simple connection test
        echo "<div class='test-result'>";
        echo "<h4>Test 1: Basic GraphQL Query</h4>";
        
        $query = '
        query GetTickets {
            getTickets {
                id
                ticket_no
                subject
                status
                priority
                created
            }
        }';
        
        $result = makeGraphQLRequest($query, []);
        
        if ($result['success']) {
            echo "<div class='success'>";
            echo "<strong>✅ Success!</strong> GraphQL API connection is working.";
            echo "<br><strong>Status Code:</strong> " . $result['status'];
            echo "</div>";
            
            echo "<h5>API Response:</h5>";
            echo "<pre>" . htmlspecialchars(json_encode($result['data'], JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Failed!</strong> " . htmlspecialchars($result['error']);
            echo "<br><strong>Status Code:</strong> " . $result['status'];
            echo "</div>";
            
            if ($result['raw_response']) {
                echo "<h5>Raw Response:</h5>";
                echo "<pre>" . htmlspecialchars($result['raw_response']) . "</pre>";
            }
        }
        echo "</div>";

        // Test 2: Alternative query structure
        echo "<div class='test-result'>";
        echo "<h4>Test 2: Alternative Query Structure</h4>";
        
        $query2 = '
        query GetTickets {
            getTickets {
                data {
                    id
                    ticket_no
                    subject
                    status
                    priority
                    created
                }
            }
        }';
        
        $result2 = makeGraphQLRequest($query2, []);
        
        if ($result2['success']) {
            echo "<div class='success'>";
            echo "<strong>✅ Success!</strong> Alternative query structure works.";
            echo "</div>";
            
            echo "<h5>API Response:</h5>";
            echo "<pre>" . htmlspecialchars(json_encode($result2['data'], JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Failed!</strong> " . htmlspecialchars($result2['error']);
            echo "</div>";
        }
        echo "</div>";

        // Test 3: Individual ticket query
        echo "<div class='test-result'>";
        echo "<h4>Test 3: Individual Ticket Query</h4>";
        
        $query3 = '
        query GetTicket {
            getTicket(id: 1) {
                id
                ticket_no
                subject
                status
                priority
                created
            }
        }';
        
        $result3 = makeGraphQLRequest($query3, []);
        
        if ($result3['success']) {
            echo "<div class='success'>";
            echo "<strong>✅ Success!</strong> Individual ticket query works.";
            echo "</div>";
            
            echo "<h5>API Response:</h5>";
            echo "<pre>" . htmlspecialchars(json_encode($result3['data'], JSON_PRETTY_PRINT)) . "</pre>";
        } else {
            echo "<div class='error'>";
            echo "<strong>❌ Failed!</strong> " . htmlspecialchars($result3['error']);
            echo "</div>";
        }
        echo "</div>";
        ?>

        <div class="test-result info">
            <h4>Next Steps:</h4>
            <ul>
                <li>If all tests pass, go to <a href="appika-tickets.php">Appika Tickets</a> to view the table</li>
                <li>If tests fail, check the API key and endpoint configuration</li>
                <li>Check the GraphQL schema at: <a href="https://dev-sgsg-tktapi.appika.com/graphiql" target="_blank">GraphiQL Interface</a></li>
            </ul>
        </div>

        <div class="mt-3">
            <a href="appika-tickets.php" class="btn btn-primary">Go to Appika Tickets</a>
            <a href="admin-tickets.php" class="btn btn-secondary">Go to Local Tickets</a>
            <a href="index.php" class="btn btn-outline-primary">Back to Dashboard</a>
        </div>
    </div>
</body>
</html>
