<?php
// Test success page without Stripe dependency
ini_set('display_errors', 1);
error_reporting(E_ALL);

include('../functions/server.php');

$session_id = $_GET['session_id'] ?? 'test_session_' . time();

// Simulate successful payment processing
$username = 'user' . rand(10000,99999);
$email = '<EMAIL>';
$password = bin2hex(random_bytes(4));
$show_creds = true;

// Try to save to payment_temp
try {
    $stmt = $conn->prepare("INSERT INTO payment_temp (session_id, username, email, password) VALUES (?, ?, ?, ?)");
    $stmt->bind_param("ssss", $session_id, $username, $email, $password);
    $stmt->execute();
    echo "<div style='color: green; padding: 10px; margin: 10px; border: 1px solid green;'>✓ Successfully saved to payment_temp</div>";
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; margin: 10px; border: 1px solid red;'>✗ Error saving to payment_temp: " . $e->getMessage() . "</div>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Payment Success</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #f5f5f5; margin: 0; padding: 20px; }
        .copy-btn { margin-left: 10px; padding: 5px 10px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        .copy-btn:hover { background: #005a87; }
        .container { max-width: 500px; margin: 50px auto; padding: 30px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #28a745; font-size: 1.5em; margin-bottom: 20px; text-align: center; }
        .field { margin-bottom: 15px; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        .field label { font-weight: bold; color: #333; }
        .field span { color: #666; }
        .btn { display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin-top: 20px; }
        .btn:hover { background: #218838; }
        .success-icon { font-size: 4em; color: #28a745; text-align: center; margin-bottom: 20px; }
    </style>
    <script>
        function copyToClipboard(id) {
            var copyText = document.getElementById(id);
            navigator.clipboard.writeText(copyText.textContent);
            alert("Copied: " + copyText.textContent);
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <div class="success">Payment Completed Successfully!</div>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            Your account has been created and tickets have been added. Please save your login credentials:
        </p>
        
        <div class="field">
            <label>Username:</label><br>
            <span id="username"><?php echo htmlspecialchars($username); ?></span>
            <button class="copy-btn" onclick="copyToClipboard('username')">Copy</button>
        </div>
        <div class="field">
            <label>Email:</label><br>
            <span id="email"><?php echo htmlspecialchars($email); ?></span>
            <button class="copy-btn" onclick="copyToClipboard('email')">Copy</button>
        </div>
        <div class="field">
            <label>Password:</label><br>
            <span id="password"><?php echo htmlspecialchars($password); ?></span>
            <button class="copy-btn" onclick="copyToClipboard('password')">Copy</button>
        </div>
        
        <div style="text-align: center;">
            <a href="sign-in.php" class="btn">Go to Login Page</a>
        </div>
    </div>
</body>
</html>
