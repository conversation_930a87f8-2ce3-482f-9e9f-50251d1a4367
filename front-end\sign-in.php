<?php
session_start();
include('../functions/server.php');

// Auto-detect environment for URL paths
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';



// Check for preserved cart transfer parameters from validation errors
if (isset($_SESSION['preserve_cart_transfer']) && !isset($_GET['cart_transfer'])) {
    $preserved = $_SESSION['preserve_cart_transfer'];
    unset($_SESSION['preserve_cart_transfer']);

    // Redirect to self with cart transfer parameters
    $redirect_url = $_SERVER['REQUEST_URI'];
    $redirect_url .= (strpos($redirect_url, '?') !== false ? '&' : '?');
    $redirect_url .= 'cart_transfer=' . urlencode($preserved['cart_transfer']);
    $redirect_url .= '&return_to=' . urlencode($preserved['return_to']);

    header("location: " . $redirect_url);
    exit();
}

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // User is already logged in, redirect to my-ticket page
    header("location: " . $url_base . "/front-end/my-ticket.php");
    exit();
}

// Define cart transfer variable for use in the page
$cart_transfer = isset($_GET['cart_transfer']) && $_GET['cart_transfer'] == '1';
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Sign In</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <style>
    /* Password toggle eye icon styling */
    .toggle-password {
        cursor: pointer;
        border-left: none;
    }

    .toggle-password:hover {
        color: #007bff;
    }

    .input-group-text {
        background-color: #fff;
        border-left: 0;
    }

    .input-group .form-control:focus+.input-group-append .input-group-text {
        border-color: #80bdff;
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <div class="sign-in bg-default-2" style="padding-top: 0px;">
            <header>
                <?php
        include('../header-footer/header.php');
        ?>
            </header>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-7 col-lg-5 col-xl-4">
                        <div class="main-block py-25">
                            <div class="form-title text-center">
                                <h2 class="title gr-text-2 mb-9">Login</h2>
                                <?php if ($cart_transfer): ?>
                                <div class="alert alert-info mb-3"
                                    style="background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; padding: 15px; border-radius: 5px;">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    <strong>Cart Transfer:</strong>
                                    <?php echo isset($_GET['message']) ? htmlspecialchars(urldecode($_GET['message'])) : 'Please login to continue with your purchase. Your cart items will be preserved.'; ?>
                                </div>
                                <?php else: ?>
                                <p class="gr-text-8 mb-13">To get started, you need to sign in here.</p>
                                <?php endif; ?>
                            </div>
                            <div class="login-form bg-white border rounded-10 px-8 py-8 shadow-1 mb-11">
                                <form action="../functions/sign-in-db.php" method="POST">
                                    <!-- notification message-->

                                    <?php if (isset($_SESSION['error'])) : ?>
                                    <div class="gr-text-8 mb-13">

                                        <?php
                      echo $_SESSION['error'];
                      unset($_SESSION['error']);
                      ?>
                                    </div>
                                    <?php endif ?>
                                    <div class="form-group">
                                        <label for="username"
                                            class="gr-text-11 font-weight-bold text-blackish-blue">Username or
                                            Email</label>
                                        <input class="form-control gr-text-11 border" type="text" id="username"
                                            name="username" placeholder="Enter your username or email"
                                            value="<?php echo isset($_GET['email']) ? htmlspecialchars($_GET['email']) : ''; ?>"
                                            required>
                                    </div>
                                    <div class="form-group forget-block">
                                        <div class="d-flex justify-content-between">
                                            <label for="password"
                                                class="gr-text-11 font-weight-bold text-blackish-blue">Password</label>
                                        </div>
                                        <div class="input-group">
                                            <input id="password" class="form-control gr-text-11 border" type="password"
                                                name="password" placeholder="********" required>
                                            <div class="input-group-append">
                                                <span class="input-group-text toggle-password" toggle="#password">
                                                    <i class="fa fa-eye-slash" aria-hidden="true"></i>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Hidden fields for cart transfer -->
                                    <?php if ($cart_transfer): ?>
                                    <input type="hidden" name="cart_transfer" value="1">
                                    <input type="hidden" name="return_to"
                                        value="<?php echo isset($_GET['return_to']) ? htmlspecialchars($_GET['return_to']) : 'cart'; ?>">
                                    <?php endif; ?>

                                    <div class="form-group button-block mb-2">
                                        <button type="submit" name="signin_user"
                                            class="form-btn btn btn-primary gr-hover-y w-100">
                                            <?php if ($cart_transfer): ?>
                                            <i class="fas fa-shopping-cart mr-2"></i>Login & Continue Purchase
                                            <?php else: ?>
                                            Sign In
                                            <?php endif; ?>
                                        </button>
                                    </div>
                                </form>
                            </div>
                            <div class="form-bottom excerpt text-center">
                                <p class="sign-up-text gr-text-9 gr-text-color" style="color: black !important;">Forget
                                    password?
                                    <br><a href="#" class="text-primary" data-toggle="modal"
                                        data-target="#resetPasswordModal">Reset password now</a>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- footer-->
        <?php
    include '../header-footer/footer.php';
    ?>
    </div>

    <!-- Reset Password Modal -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1" role="dialog"
        aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="resetPasswordModalLabel">
                        <i class="fas fa-key mr-2"></i>Reset Password
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="resetPasswordForm">
                        <div class="form-group">
                            <label for="resetEmail">Email Address</label>
                            <input type="email" class="form-control" id="resetEmail" name="email"
                                placeholder="Enter your email address" required>
                            <small class="form-text text-muted">Enter the email address associated with your
                                account.</small>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="newPassword" name="new_password"
                                    placeholder="Enter new password" required minlength="6">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">Confirm New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password"
                                    placeholder="Confirm new password" required>
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div id="resetMessage" class="alert" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <!-- <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button> -->
                    <button type="button" class="btn btn-primary" id="resetPasswordBtn">
                        <i class="fas fa-sync-alt mr-2"></i>Reset Password
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <!-- Registration Success Modal -->
    <div class="modal fade" id="registrationSuccessModal" tabindex="-1" role="dialog"
        aria-labelledby="registrationSuccessModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header bg-primary">
                    <h5 class="modal-title text-white" id="registrationSuccessModalLabel" style="font-size: 22px;">
                        Registration Successful</h5>
                    <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close"
                        id="closeRegModal">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body text-center py-4">
                    <div class="mb-4">
                        <i class="fas fa-user-plus text-success" style="font-size: 60px;"></i>
                    </div>
                    <h4>Account Created!</h4>
                    <p class="mb-4">Your account has been successfully created. Please log in with your credentials.</p>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-primary" id="continueRegBtn">Continue to Login</button>
                </div>
            </div>
        </div>
    </div>

    <script>
    $(document).ready(function() {
        // Check for registration success message
        <?php if (isset($_SESSION['registration_success']) && $_SESSION['registration_success'] === true) : ?>
        // Show the modal
        $('#registrationSuccessModal').modal({
            backdrop: true,
            keyboard: true,
            focus: true,
            show: true
        });
        <?php unset($_SESSION['registration_success']); ?>

        // Add event handlers for closing the modal
        $('#closeRegModal, #continueRegBtn').on('click', function() {
            $('#registrationSuccessModal').modal('hide');
        });
        <?php endif; ?>

        // Toggle password visibility
        $('.toggle-password').on('click', function() {
            // Toggle the eye icon
            $(this).find('i').toggleClass('fa-eye-slash fa-eye');

            // Toggle the password field type
            var input = $($(this).attr('toggle'));
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
            } else {
                input.attr('type', 'password');
            }
        });

        // Add hover effect to the eye icon
        $('.toggle-password').hover(
            function() {
                $(this).css('cursor', 'pointer');
            }
        );
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Function to get URL parameters
        function getUrlParameter(name) {
            name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
            var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
            var results = regex.exec(location.search);
            return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
        }

        // Check for URL parameters first (from payment success page)
        var urlUsername = getUrlParameter('username');
        var urlPassword = getUrlParameter('password');
        var urlEmail = getUrlParameter('email');
        var fromPayment = getUrlParameter('from_payment');

        // Check localStorage (from other sources)
        var storageEmail = localStorage.getItem('autoLoginEmail');
        var storagePassword = localStorage.getItem('autoLoginPassword');

        // Priority: URL parameters > localStorage
        var finalUsername = urlUsername || urlEmail || storageEmail || '';
        var finalPassword = urlPassword || storagePassword || '';

        // Fill the form fields
        if (finalUsername) {
            document.getElementById('username').value = finalUsername;
            // Add visual indication that field was auto-filled
            document.getElementById('username').style.backgroundColor = '#e8f5e8';
            document.getElementById('username').style.borderColor = '#28a745';
        }

        if (finalPassword) {
            document.getElementById('password').value = finalPassword;
            // Add visual indication that field was auto-filled
            document.getElementById('password').style.backgroundColor = '#e8f5e8';
            document.getElementById('password').style.borderColor = '#28a745';
        }

        // Clear localStorage after use
        if (storageEmail) localStorage.removeItem('autoLoginEmail');
        if (storagePassword) localStorage.removeItem('autoLoginPassword');

        // Show success message if credentials were auto-filled
        if (finalUsername && finalPassword) {
            showAutoFillMessage(fromPayment);
        }

        // Clear the URL parameters for security (optional)
        if (urlUsername || urlPassword || urlEmail) {
            // Replace current URL without parameters
            var newUrl = window.location.protocol + "//" + window.location.host + window.location.pathname;
            window.history.replaceState({
                path: newUrl
            }, '', newUrl);
        }
    });

    // Show auto-fill success message
    function showAutoFillMessage(fromPayment) {
        // Create and show a temporary success message
        var messageDiv = document.createElement('div');
        messageDiv.className = 'alert alert-success';
        messageDiv.style.marginBottom = '15px';

        if (fromPayment) {
            messageDiv.innerHTML =
                '<i class="fas fa-check-circle mr-2"></i> &nbsp; <strong>Credentials Saved & Auto-Filled!</strong> Your updated credentials have been saved and filled below. Simply click "Sign In" to access your account.';
        } else {
            messageDiv.innerHTML =
                '<i class="fas fa-check-circle mr-2"></i> &nbsp; <strong>Credentials Auto-Filled!</strong> Your login information has been automatically filled from your recent purchase.';
        }

        // Insert before the form
        var form = document.querySelector('.login-form');
        form.parentNode.insertBefore(messageDiv, form);

        // Auto-remove message after 7 seconds (longer for payment context)
        setTimeout(function() {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, fromPayment ? 7000 : 5000);
    }

    // Reset Password Modal Functions
    document.getElementById('toggleNewPassword').addEventListener('click', function() {
        var passwordField = document.getElementById('newPassword');
        var icon = this.querySelector('i');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
        var passwordField = document.getElementById('confirmPassword');
        var icon = this.querySelector('i');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    document.getElementById('resetPasswordBtn').addEventListener('click', function() {
        var email = document.getElementById('resetEmail').value;
        var newPassword = document.getElementById('newPassword').value;
        var confirmPassword = document.getElementById('confirmPassword').value;
        var messageDiv = document.getElementById('resetMessage');
        var resetBtn = this;

        // Validation
        if (!email || !newPassword || !confirmPassword) {
            showResetMessage('Please fill in all fields.', 'danger');
            return;
        }

        if (newPassword !== confirmPassword) {
            showResetMessage('Passwords do not match.', 'danger');
            return;
        }

        if (newPassword.length < 6) {
            showResetMessage('Password must be at least 6 characters long.', 'danger');
            return;
        }

        // Show loading state
        resetBtn.disabled = true;
        resetBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Resetting...';

        // Send AJAX request
        var xhr = new XMLHttpRequest();
        xhr.open('POST', '../functions/reset-password.php', true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');

        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                resetBtn.disabled = false;
                resetBtn.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Reset Password';

                if (xhr.status === 200) {
                    try {
                        var response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            showResetMessage(response.message, 'success');
                            // Clear form
                            document.getElementById('resetPasswordForm').reset();
                            // Auto-close modal after 3 seconds
                            setTimeout(function() {
                                $('#resetPasswordModal').modal('hide');
                            }, 3000);
                        } else {
                            showResetMessage(response.message, 'danger');
                        }
                    } catch (e) {
                        showResetMessage('An error occurred. Please try again.', 'danger');
                    }
                } else {
                    showResetMessage('Network error. Please try again.', 'danger');
                }
            }
        };

        var params = 'email=' + encodeURIComponent(email) + '&new_password=' + encodeURIComponent(newPassword);
        xhr.send(params);
    });

    function showResetMessage(message, type) {
        var messageDiv = document.getElementById('resetMessage');
        messageDiv.className = 'alert alert-' + type;
        messageDiv.innerHTML = message;
        messageDiv.style.display = 'block';

        // Auto-hide success messages
        if (type === 'success') {
            setTimeout(function() {
                messageDiv.style.display = 'none';
            }, 5000);
        }
    }
    </script>
</body>

</html>