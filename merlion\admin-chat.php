<?php
session_start();
include('../functions/server.php');

// Include timezone helper for proper time handling
include('../functions/timezone-helper.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];
$admin_role = $_SESSION['admin_role'];

// Get ticket ID if provided
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;
$ticket_info = null;
$user_id = null;

// If ticket ID is provided, get ticket information
if ($ticket_id) {
    $ticket_query = "SELECT st.*, u.username, u.email, u.tell
                    FROM support_tickets st
                    JOIN user u ON st.user_id = u.id
                    WHERE st.id = $ticket_id";
    $ticket_result = mysqli_query($conn, $ticket_query);

    if ($ticket_result && mysqli_num_rows($ticket_result) > 0) {
        $ticket_info = mysqli_fetch_assoc($ticket_result);
        $user_id = $ticket_info['user_id'];
    }
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Get all users with tickets for the chat list
$users_query = "SELECT DISTINCT u.id, u.username, u.email,
                (SELECT MAX(cm.created_at) FROM chat_messages cm WHERE
                    (cm.sender_id = u.id AND cm.sender_type = 'user') OR
                    (cm.sender_id = $admin_id AND cm.sender_type = 'admin' AND cm.ticket_id IN
                        (SELECT id FROM support_tickets WHERE user_id = u.id))
                ) as last_message_time,
                (SELECT COUNT(*) FROM chat_messages cm WHERE cm.sender_id = u.id AND cm.sender_type = 'user' AND cm.is_read = 0) as unread_count
                FROM user u
                JOIN support_tickets st ON u.id = st.user_id
                ORDER BY last_message_time DESC, unread_count DESC, u.username ASC";
$users_result = mysqli_query($conn, $users_query);

// Handle sending new messages
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message']) && !empty($_POST['message'])) {
    $message = mysqli_real_escape_string($conn, $_POST['message']);
    $chat_user_id = isset($_POST['user_id']) ? intval($_POST['user_id']) : $user_id;
    $chat_ticket_id = isset($_POST['ticket_id']) ? intval($_POST['ticket_id']) : $ticket_id;

    if ($chat_user_id) {
        // Make sure ticket_id is not null
        if (!$chat_ticket_id) {
            // Get the first ticket for this user
            $first_ticket_query = "SELECT id FROM support_tickets WHERE user_id = $chat_user_id LIMIT 1";
            $first_ticket_result = mysqli_query($conn, $first_ticket_query);
            if ($first_ticket_result && mysqli_num_rows($first_ticket_result) > 0) {
                $first_ticket = mysqli_fetch_assoc($first_ticket_result);
                $chat_ticket_id = $first_ticket['id'];
            } else {
                // If no ticket exists, create a default value
                $chat_ticket_id = 0;
            }
        }

        // Get current UTC time for consistent storage
        $utc_time = UTCTimeHelper::getCurrentUTC();

        $insert_query = "INSERT INTO chat_messages (ticket_id, sender_id, sender_type, message, created_at)
                        VALUES (?, ?, 'admin', ?, ?)";
        $stmt = mysqli_prepare($conn, $insert_query);
        mysqli_stmt_bind_param($stmt, 'iiss', $chat_ticket_id, $admin_id, $message, $utc_time);
        $success = mysqli_stmt_execute($stmt);
        $message_id = mysqli_insert_id($conn);
        mysqli_stmt_close($stmt);

        // Check if this is an AJAX request
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

        if ($isAjax) {
            // For AJAX requests, return JSON response
            $response = array(
                'success' => $success,
                'message' => array(
                    'id' => $message_id,
                    'ticket_id' => $chat_ticket_id,
                    'sender_id' => $admin_id,
                    'sender_type' => 'admin',
                    'message' => $message,
                    'is_read' => 0,
                    'created_at' => $utc_time,
                    'sender_name' => $admin_username,
                    'formatted_time' => date('M d, H:i', strtotime($utc_time))
                )
            );

            header('Content-Type: application/json');
            echo json_encode($response);
            exit();
        } else {
            // For traditional form submissions, redirect
            header("Location: admin-chat.php" . ($chat_user_id ? "?user_id=$chat_user_id" : "") . ($chat_ticket_id ? "&ticket_id=$chat_ticket_id" : ""));
            exit();
        }
    }
}

// Get user ID from URL if provided
$chat_user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : $user_id;

// Get chat messages for the selected user
$messages = [];
if ($chat_user_id) {
    // Mark messages as read when admin views them
    $update_query = "UPDATE chat_messages
                    SET is_read = 1
                    WHERE sender_id = $chat_user_id
                    AND sender_type = 'user'
                    AND is_read = 0";
    mysqli_query($conn, $update_query);

    // admin_notifications table removed - using chat_messages.is_read system

    // Get messages
    $messages_query = "SELECT cm.*,
                      CASE
                          WHEN cm.sender_type = 'admin' THEN a.username
                          WHEN cm.sender_type = 'user' THEN u.username
                      END as sender_name
                      FROM chat_messages cm
                      LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                      LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                      WHERE ";

    // If a specific ticket is selected, only show messages for that ticket
    if ($ticket_id) {
        $messages_query .= "cm.ticket_id = $ticket_id";
    } else {
        // Otherwise show all messages between this admin and user
        $messages_query .= "(cm.sender_id = $chat_user_id AND cm.sender_type = 'user')
                          OR (cm.sender_id = $admin_id AND cm.sender_type = 'admin' AND cm.ticket_id IN
                              (SELECT id FROM support_tickets WHERE user_id = $chat_user_id))";
    }

    $messages_query .= " ORDER BY cm.created_at ASC";
    $messages_result = mysqli_query($conn, $messages_query);

    if ($messages_result) {
        while ($message = mysqli_fetch_assoc($messages_result)) {
            $messages[] = $message;
        }
    }

    // Get user information
    $user_query = "SELECT * FROM user WHERE id = $chat_user_id";
    $user_result = mysqli_query($conn, $user_query);
    $user_info = mysqli_fetch_assoc($user_result);

    // Get user's tickets with unread message counts
    $user_tickets_query = "SELECT st.*,
                          (SELECT COUNT(*) FROM chat_messages cm
                           WHERE cm.ticket_id = st.id
                           AND cm.sender_type = 'user'
                           AND cm.is_read = 0) as unread_count
                          FROM support_tickets st
                          WHERE st.user_id = $chat_user_id
                          ORDER BY st.created_at DESC";
    $user_tickets_result = mysqli_query($conn, $user_tickets_query);
}

// --- AJAX endpoint for admin notification count ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_admin_notifications'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    // Count unread messages directly from chat_messages table
    $notif_sql = "SELECT COUNT(*) as unread_count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
    $notif_result = mysqli_query($conn, $notif_sql);
    $notif_row = mysqli_fetch_assoc($notif_result);
    $unread_count = intval($notif_row['unread_count']);
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'unread_count' => $unread_count]);
    exit();
}

// --- AJAX endpoint for chat user list (for sidebar auto-update) ---
if (
    isset($_GET['action']) && $_GET['action'] === 'get_chat_user_list'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    $admin_id = $_SESSION['admin_id'];
    $users_query = "SELECT DISTINCT u.id, u.username, u.email,
        (SELECT MAX(cm.created_at) FROM chat_messages cm WHERE
            (cm.sender_id = u.id AND cm.sender_type = 'user') OR
            (cm.sender_id = $admin_id AND cm.sender_type = 'admin' AND cm.ticket_id IN
                (SELECT id FROM support_tickets WHERE user_id = u.id))
        ) as last_message_time,
        (SELECT COUNT(*) FROM chat_messages cm WHERE cm.sender_id = u.id AND cm.sender_type = 'user' AND cm.is_read = 0) as unread_count
        FROM user u
        JOIN support_tickets st ON u.id = st.user_id
        ORDER BY last_message_time DESC, unread_count DESC, u.username ASC";
    $users_result = mysqli_query($conn, $users_query);
    $users = [];
    while ($user = mysqli_fetch_assoc($users_result)) {
        $users[] = $user;
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'users' => $users]);
    exit();
}

if (
    isset($_GET['action']) && $_GET['action'] === 'get_ticket_notifications'
    && isset($_SESSION['admin_id'])
    && !empty($_SERVER['HTTP_X_REQUESTED_WITH'])
    && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'
) {
    $user_id = intval($_GET['user_id'] ?? 0);
    $tickets = [];
    if ($user_id) {
        $tickets_query = "SELECT st.id,
            (SELECT COUNT(*) FROM chat_messages cm
             WHERE cm.ticket_id = st.id
             AND cm.sender_type = 'user'
             AND cm.is_read = 0) as unread_count
            FROM support_tickets st
            WHERE st.user_id = $user_id";
        $tickets_result = mysqli_query($conn, $tickets_query);
        while ($ticket = mysqli_fetch_assoc($tickets_result)) {
            $tickets[] = [
                'id' => $ticket['id'],
                'unread_count' => (int)$ticket['unread_count']
            ];
        }
    }
    header('Content-Type: application/json');
    echo json_encode(['success' => true, 'tickets' => $tickets]);
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Live Chat</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    /* Notification styling */
    .notification {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 4px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        max-width: 350px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .notification.show {
        opacity: 1;
        transform: translateY(0);
    }

    .notification.success {
        background-color: #28a745;
    }

    .notification.danger {
        background-color: #dc3545;
    }

    .notification.info {
        background-color: #17a2b8;
    }

    .notification.warning {
        background-color: #ffc107;
        color: #212529;
    }

    @media (max-width: 767px) {
        .notification {
            top: 10px;
            right: 10px;
            left: 10px;
            max-width: none;
        }
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
    }

    /* Responsive container */
    @media (max-width: 991px) {
        .admin-container {
            padding: 15px;
        }

        .admin-header {
            padding: 12px 15px;
        }
    }

    @media (max-width: 767px) {
        .admin-container {
            padding: 10px;
        }

        .admin-header {
            padding: 10px;
        }
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .admin-user span {
        margin-right: 10px;
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .chat-container {
        display: flex;
        height: 100%;
        width: 100%;
        position: relative;
    }

    @media (max-width: 991px) {
        .admin-content {
            padding: 15px;
            height: calc(100vh - 90px);
        }
    }

    @media (max-width: 767px) {
        .admin-content {
            padding: 10px;
            height: calc(100vh - 80px);
        }

        .chat-container {
            flex-direction: column;
        }
    }

    .chat-sidebar {
        width: 300px;
        border-right: 1px solid #e5e5e5;
        height: 100%;
        overflow-y: auto;
        transition: all 0.3s ease;
    }

    .chat-sidebar-header {
        padding: 15px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chat-sidebar-header h3 {
        margin: 0;
        font-size: 18px;
    }

    .mobile-toggle-chat-sidebar {
        display: none;
        background: none;
        border: none;
        color: #473BF0;
        font-size: 18px;
        cursor: pointer;
    }

    @media (max-width: 991px) {
        .chat-sidebar {
            width: 250px;
        }
    }

    @media (max-width: 767px) {
        .chat-sidebar {
            width: 100%;
            height: 300px;
            border-right: none;
            border-bottom: 1px solid #e5e5e5;
        }

        .mobile-toggle-chat-sidebar {
            display: block;
        }

        .chat-sidebar.collapsed {
            height: 60px;
            overflow: hidden;
        }
    }

    @media (max-width: 480px) {
        .chat-sidebar {
            height: 250px;
        }
    }

    .chat-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .chat-list-item {
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        transition: background-color 0.2s;
        position: relative;
    }

    .chat-list-item:hover {
        background-color: #f8f9fa;
    }

    .chat-list-item.active {
        background-color: #e9ecef;
    }

    .chat-list-item-name {
        font-weight: 600;
        margin-bottom: 5px;
        display: flex;
        justify-content: space-between;
    }

    .chat-list-item-email {
        font-size: 12px;
        color: #6c757d;
    }

    .unread-badge {
        background-color: #473BF0;
        color: white;
        border-radius: 50%;
        min-width: 20px;
        height: 20px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        padding: 0 5px;
    }

    .ticket-notification-badge {
        background-color: #dc3545;
        color: white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 11px;
        font-weight: bold;
        position: absolute;
        top: 5px;
        right: 10px;
        z-index: 10;
    }

    .ticket-item {
        position: relative;
    }

    .chat-main {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 0;
        /* Important for flex child to respect parent height */
    }

    .chat-header {
        padding: 15px;
        border-bottom: 1px solid #e5e5e5;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        position: relative;
    }

    /* Ensure buttons have proper spacing */
    .chat-header-actions .btn {
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    /* Profile action styling */
    .profile-action {
        margin-left: auto;
        margin-right: 10px;
    }

    @media (max-width: 767px) {
        .chat-main {
            height: calc(100% - 300px);
        }

        .chat-header {
            padding: 10px;
        }
    }

    @media (max-width: 480px) {
        .chat-main {
            height: calc(100% - 250px);
        }
    }

    .chat-header-user {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }

    .chat-header-user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: #473BF0;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        margin-right: 10px;
        flex-shrink: 0;
    }

    .chat-header-user-info {
        min-width: 0;
        overflow: hidden;
    }

    .chat-header-user-info h4 {
        margin: 0;
        font-size: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-header-user-info p {
        margin: 0;
        font-size: 12px;
        color: #6c757d;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .chat-header-actions {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: flex-start;
        width: 100%;
        margin-top: 10px;
    }

    .ticket-actions {
        display: flex;
        align-items: center;
        padding-left: 15px;
    }

    .chat-header-actions a {
        margin-left: 10px;
        color: #6c757d;
        text-decoration: none;
    }

    .chat-header-actions .btn-group {
        display: flex;
        flex-wrap: nowrap;
        margin-right: 10px;
    }

    .chat-header-actions .btn-group .btn {
        border-radius: 0;
        transition: all 0.2s ease;
    }

    .chat-header-actions .btn-group .btn:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .chat-header-actions .btn-group .btn:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    /* Specific hover styles for Resolve and Close buttons */
    .btn-outline-success:hover {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-outline-secondary:hover {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .chat-header-actions .btn {
        white-space: nowrap;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    @media (max-width: 767px) {
        .chat-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .chat-header-user {
            width: 100%;
            margin-bottom: 10px;
        }

        .profile-action {
            position: absolute;
            top: 15px;
            right: 15px;
            margin-right: 0;
        }

        .chat-header-actions {
            margin-top: 10px;
            width: 100%;
            justify-content: center;
            /* Center on mobile */
        }

        .ticket-actions {
            width: 100%;
            justify-content: center;
            padding-left: 0;
        }

        .chat-header-actions .btn-group {
            margin-right: 0 !important;
            margin-bottom: 5px;
        }

        .chat-header-actions .btn {
            padding: 5px 10px;
            font-size: 13px;
            height: auto;
        }

        .chat-header-actions .btn i {
            margin-right: 5px;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {

        /* Keep text visible on mobile */
        .chat-header-actions .btn span {
            display: inline-block;
            font-size: 12px;
        }

        .chat-header-actions .btn i {
            margin-right: 3px;
            font-size: 12px;
        }

        .chat-header-actions .btn {
            min-width: auto;
            padding: 4px 8px;
            margin-bottom: 5px;
        }

        .profile-action {
            top: 10px;
            right: 10px;
        }

        .profile-action .btn {
            padding: 4px 8px;
            font-size: 12px;
        }

        .profile-action .btn span {
            display: none;
        }

        .profile-action .btn i {
            margin-right: 0;
        }
    }

    @media (max-width: 767px) {
        .chat-header-user {
            margin-right: 10px;
        }

        .chat-header-actions {
            margin-top: 5px;
        }

        .chat-header-actions .btn {
            padding: 4px 8px;
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .chat-header-user-avatar {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }

        .chat-header-user-info h4 {
            font-size: 14px;
        }

        .chat-header-user-info p {
            font-size: 11px;
        }
    }

    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        background-color: #f8f9fa;
        min-height: 0;
        /* Important for flex child to respect parent height */
    }

    .chat-message {
        margin-bottom: 15px;
        display: flex;
        flex-direction: column;
    }

    .chat-message-content {
        max-width: 80%;
        padding: 10px 15px;
        border-radius: 18px;
        position: relative;
        word-wrap: break-word;
    }

    .chat-message-time {
        font-size: 11px;
        color: #6c757d;
        margin-top: 5px;
    }

    .chat-message.outgoing {
        align-items: flex-end;
    }

    .chat-message.incoming {
        align-items: flex-start;
    }

    .chat-message.outgoing .chat-message-content {
        background-color: #473BF0;
        color: white;
        border-bottom-right-radius: 5px;
    }

    .chat-message.incoming .chat-message-content {
        background-color: #e9ecef;
        color: #212529;
        border-bottom-left-radius: 5px;
    }

    .chat-input {
        padding: 15px;
        border-top: 1px solid #e5e5e5;
        display: flex;
        align-items: center;
    }

    .chat-input form {
        display: flex;
        width: 100%;
        align-items: center;
    }

    .chat-input input {
        flex: 1;
        padding: 10px 15px;
        border: 1px solid #ced4da;
        border-radius: 30px;
        outline: none;
        font-size: 14px;
    }

    .chat-input button {
        margin-left: 10px;
        border: none;
        background-color: #473BF0;
        color: white;
        border-radius: 30px;
        padding: 10px 20px;
        cursor: pointer;
        transition: background-color 0.2s;
        white-space: nowrap;
        display: flex;
        align-items: center;
    }

    .chat-input button:hover {
        background-color: #3730c0;
    }

    .chat-input button i {
        margin-right: 5px;
    }

    @media (max-width: 991px) {
        .chat-messages {
            padding: 12px;
        }

        .chat-message-content {
            max-width: 85%;
            padding: 8px 12px;
        }
    }

    @media (max-width: 767px) {
        .chat-messages {
            padding: 10px;
        }

        .chat-input {
            padding: 10px;
        }

        .chat-input input {
            padding: 8px 12px;
            font-size: 14px;
        }

        .chat-input button {
            padding: 8px 12px;
            font-size: 14px;
        }

        .chat-input button span {
            display: none;
        }

        .chat-input button i {
            margin-right: 0;
        }
    }

    @media (max-width: 480px) {
        .chat-message-content {
            max-width: 90%;
            padding: 8px 10px;
            font-size: 14px;
        }

        .chat-message-time {
            font-size: 10px;
        }
    }

    .chat-empty {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        color: #6c757d;
    }

    .chat-empty i {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dee2e6;
    }

    .ticket-info {
        padding: 15px;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e5e5e5;
    }

    .ticket-header {
        cursor: pointer;
    }

    .ticket-info h5 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        color: #6c757d;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .ticket-toggle-btn {
        background: none;
        border: none;
        color: #473BF0;
        font-size: 14px;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        transition: transform 0.3s ease;
    }

    .ticket-toggle-btn.collapsed i {
        transform: rotate(-90deg);
    }

    .ticket-list {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: 150px;
        overflow-y: auto;
        transition: max-height 0.3s ease, opacity 0.3s ease;
        -webkit-overflow-scrolling: touch;
        /* Smooth scrolling on iOS */
        scrollbar-width: thin;
        /* Firefox */
        scrollbar-color: #473BF0 #f0f0f0;
        /* Firefox */
    }

    /* Webkit scrollbar styling */
    .ticket-list::-webkit-scrollbar {
        width: 6px;
    }

    .ticket-list::-webkit-scrollbar-track {
        background: #f0f0f0;
        border-radius: 10px;
    }

    .ticket-list::-webkit-scrollbar-thumb {
        background: #473BF0;
        border-radius: 10px;
    }

    .ticket-list.collapsed {
        max-height: 0;
        overflow: hidden;
        opacity: 0;
        margin: 0;
    }

    .ticket-item {
        padding: 8px 10px;
        border-radius: 5px;
        margin-bottom: 8px;
        background-color: #fff;
        border: 1px solid #e5e5e5;
        font-size: 13px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .ticket-list .ticket-item:last-child {
        margin-bottom: 5px;
    }

    .ticket-item-content {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        width: 100%;
    }

    .ticket-id {
        font-weight: 600;
        margin-right: 5px;
        white-space: nowrap;
    }

    .ticket-subject {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 5px;
    }

    .ticket-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 3px;
    }

    .ticket-item:hover {
        background-color: #e9ecef;
    }

    .ticket-item.active {
        background-color: #473BF0;
        color: white;
        border-color: #473BF0;
    }

    .ticket-item .badge {
        margin-left: 0;
    }

    @media (max-width: 991px) {
        .ticket-info {
            padding: 12px;
        }

        .ticket-list {
            max-height: 120px;
        }

        .ticket-toggle-btn {
            width: 22px;
            height: 22px;
        }
    }

    @media (max-width: 767px) {
        .ticket-info {
            padding: 10px;
        }

        .ticket-item {
            padding: 6px 8px;
            font-size: 12px;
        }

        .ticket-info h5 {
            font-size: 13px;
        }

        .ticket-toggle-btn {
            width: 20px;
            height: 20px;
            font-size: 12px;
        }

        /* Default collapsed on mobile */
        .ticket-list {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            margin: 0;
        }

        .ticket-list.expanded {
            max-height: 200px;
            opacity: 1;
            margin-top: 10px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
    }

    @media (max-width: 480px) {
        .ticket-list.expanded {
            max-height: 150px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .ticket-item {
            margin-bottom: 3px;
        }

        .ticket-badges {
            width: 100%;
            margin-top: 5px;
            justify-content: flex-start;
        }
    }

    .badge {
        font-size: 14px;
        padding: 5px 8px;
        border-radius: 4px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    @media (max-width: 767px) {
        .badge {
            font-size: 12px;
            padding: 4px 6px;
        }
    }

    @media (max-width: 480px) {
        .badge {
            font-size: 11px;
            padding: 3px 5px;
        }
    }

    .badge-starter {
        background-color: #6c757d;
        color: #fff !important;
    }

    .badge-premium,
    .badge-business {
        background-color: #fd7e14;
        color: #fff !important;
    }

    .badge-ultimate {
        background-color: #007BFF;
        color: #fff !important;
    }

    .badge-open {
        background-color: #fd7e14;
        color: #fff;
    }

    .badge-in_progress {
        background-color: #0d6efd;
        color: #fff;
    }

    .badge-resolved {
        background-color: #28a745;
        color: #fff;
    }

    .badge-closed {
        background-color: #6c757d;
        color: #fff;
    }

    .btn {
        font-size: 14px;
        font-weight: 500;
        padding: 8px 16px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    .btn-primary {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .btn-primary:hover {
        background-color: #3730c0;
        border-color: #3730c0;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-primary {
        color: #473BF0;
        border-color: #473BF0;
    }

    /* Button hover styles */
    .btn-outline-primary:hover,
    .btn-outline-success:hover,
    .btn-outline-secondary:hover {
        color: #fff !important;
    }

    .btn-outline-primary:hover {
        background-color: #473BF0;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* User dropdown styles */
    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .admin-header h1 {
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
        }
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Live Chat Support</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>


        <div class="row">
            <!-- User sidebar -->
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
                <style>
                .admin-sidebar {
                    background-color: #fff;
                    border-radius: 5px;
                    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
                    padding: 20px;
                    height: calc(100vh - 100px);
                    overflow-y: auto;
                }

                .admin-sidebar ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }

                .admin-sidebar ul li {
                    margin-bottom: 5px;
                }

                .admin-sidebar ul li a {
                    display: block;
                    padding: 10px 15px;
                    color: #333;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: all 0.3s;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .admin-sidebar ul li a:hover,
                .admin-sidebar ul li a.active {
                    background-color: #473BF0;
                    color: #fff;
                }

                .admin-sidebar ul li a i {
                    margin-right: 10px;
                    width: 20px;
                    text-align: center;
                }

                @media (max-width: 991px) {
                    .admin-sidebar {
                        padding: 15px;
                        height: calc(100vh - 90px);
                    }

                    .admin-sidebar ul li a {
                        padding: 8px 12px;
                        font-size: 14px;
                    }
                }

                @media (max-width: 767px) {
                    .admin-sidebar {
                        height: auto;
                        margin-bottom: 15px;
                    }

                    .admin-sidebar ul {
                        display: flex;
                        flex-wrap: wrap;
                    }

                    .admin-sidebar ul li {
                        margin-right: 5px;
                        margin-bottom: 5px;
                        width: calc(50% - 5px);
                    }

                    .admin-sidebar ul li a {
                        padding: 8px 10px;
                        font-size: 13px;
                        text-align: center;
                    }

                    .admin-sidebar ul li a i {
                        margin-right: 5px;
                        width: auto;
                    }
                }

                @media (max-width: 480px) {
                    .admin-sidebar ul li {
                        width: 100%;
                        margin-right: 0;
                    }
                }
                </style>
            </div>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="chat-container">
                        <!-- Chat Sidebar - User List -->
                        <div class="chat-sidebar">
                            <div class="chat-sidebar-header">
                                <h3>Conversations</h3>
                                <button type="button" class="mobile-toggle-chat-sidebar" id="toggleChatSidebar">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <ul class="chat-list">
                                <?php if (mysqli_num_rows($users_result) > 0): ?>
                                <?php while ($user = mysqli_fetch_assoc($users_result)): ?>
                                <li class="chat-list-item <?php echo $chat_user_id == $user['id'] ? 'active' : ''; ?>"
                                    onclick="selectUser(<?php echo $user['id']; ?>)">
                                    <div class="chat-list-item-name">
                                        <span><?php echo htmlspecialchars($user['username']); ?></span>
                                        <?php if ($user['unread_count'] > 0): ?>
                                        <span class="unread-badge"><?php echo $user['unread_count']; ?></span>
                                        <?php endif; ?>
                                    </div>
                                    <div class="chat-list-item-email"><?php echo htmlspecialchars($user['email']); ?>
                                    </div>
                                </li>
                                <?php endwhile; ?>
                                <?php else: ?>
                                <li class="chat-list-item">No users found</li>
                                <?php endif; ?>
                            </ul>
                        </div>

                        <!-- Chat Main Area -->
                        <div class="chat-main">
                            <?php if (isset($_GET['error'])): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                Error: <?php echo htmlspecialchars($_GET['error']); ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <?php if (isset($_GET['success']) && isset($_GET['message'])): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo htmlspecialchars($_GET['message']); ?>
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <?php endif; ?>

                            <?php if ($chat_user_id && isset($user_info)): ?>
                            <!-- Chat Header -->
                            <div class="chat-header">
                                <div class="chat-header-user">
                                    <div class="chat-header-user-avatar">
                                        <?php echo strtoupper(substr($user_info['username'], 0, 1)); ?>
                                    </div>
                                    <div class="chat-header-user-info">
                                        <h4><?php echo htmlspecialchars($user_info['username']); ?></h4>
                                        <p><?php echo htmlspecialchars($user_info['email']); ?></p>
                                    </div>
                                </div>

                                <div class="profile-action">
                                    <a href="admin-user-detail.php?id=<?php echo $chat_user_id; ?>"
                                        title="View User Profile" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-user"></i> <span> &nbsp; View Profile</span>
                                    </a>
                                </div>

                                <div class="chat-header-actions">
                                    <div class="ticket-actions">
                                        <?php if ($ticket_id): ?>
                                        <?php
                                                // Get current ticket status
                                                $status_query = "SELECT status FROM support_tickets WHERE id = $ticket_id";
                                                $status_result = mysqli_query($conn, $status_query);
                                                $current_ticket = mysqli_fetch_assoc($status_result);
                                                $current_status = $current_ticket['status'];
                                                ?>
                                        <div class="btn-group">
                                            <a href="update-ticket-status.php?ticket_id=<?php echo $ticket_id; ?>&status=resolved&redirect=1"
                                                class="btn btn-sm <?php echo $current_status === 'resolved' ? 'btn-success' : 'btn-outline-success'; ?>"
                                                <?php echo $current_status === 'closed' ? 'disabled' : ''; ?>>
                                                <i class="fas fa-check"></i> <span>&nbsp; Resolve</span>
                                            </a>
                                            <a href="update-ticket-status.php?ticket_id=<?php echo $ticket_id; ?>&status=closed&redirect=1"
                                                class="btn btn-sm <?php echo $current_status === 'closed' ? 'btn-secondary' : 'btn-outline-secondary'; ?>"
                                                <?php echo $current_status === 'closed' ? 'disabled' : ''; ?>>
                                                <i class="fas fa-lock"></i> <span>&nbsp; Close</span>
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Ticket Information -->
                            <?php if (mysqli_num_rows($user_tickets_result) > 0): ?>
                            <div class="ticket-info">
                                <div class="ticket-header" id="ticketHeader">
                                    <h5>
                                        <span>User's Tickets</span>
                                        <button type="button" class="ticket-toggle-btn" id="toggleTicketList">
                                            <i class="fas fa-chevron-down"></i>
                                        </button>
                                    </h5>
                                </div>
                                <ul class="ticket-list" id="ticketList">
                                    <?php while ($user_ticket = mysqli_fetch_assoc($user_tickets_result)): ?>
                                    <?php
                                                // Display 'Business' for premium tickets
                                                $ticketType = $user_ticket['ticket_type'];
                                                $badgeClass = $ticketType;
                                                $displayText = ucfirst($ticketType);

                                                if (strtolower($ticketType) == 'premium') {
                                                    $displayText = 'Business';
                                                }
                                                ?>
                                    <li class="ticket-item <?php echo $ticket_id == $user_ticket['id'] ? 'active' : ''; ?>"
                                        onclick="window.location.href='admin-chat.php?user_id=<?php echo $chat_user_id; ?>&ticket_id=<?php echo $user_ticket['id']; ?>'">
                                        <div class="ticket-item-content">
                                            <span class="ticket-id">#<?php echo $user_ticket['id']; ?>:</span>
                                            <span
                                                class="ticket-subject"><?php echo htmlspecialchars(substr($user_ticket['subject'], 0, 30)); ?></span>
                                            <div class="ticket-badges">
                                                <span
                                                    class="badge badge-<?php echo $user_ticket['status']; ?>"><?php echo ucfirst($user_ticket['status']); ?></span>
                                                <span
                                                    class="badge badge-<?php echo $badgeClass; ?>"><?php echo $displayText; ?></span>
                                            </div>
                                            <?php if ($user_ticket['unread_count'] > 0): ?>
                                            <span id="ticket-notification-<?php echo $user_ticket['id']; ?>"
                                                class="ticket-notification-badge">
                                                <?php echo $user_ticket['unread_count'] >= 10 ? '9+' : $user_ticket['unread_count']; ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                    </li>
                                    <?php endwhile; ?>
                                </ul>
                            </div>
                            <?php endif; ?>

                            <!-- Chat Messages -->
                            <div class="chat-messages" id="chatMessages">
                                <?php foreach ($messages as $message): ?>
                                <div
                                    class="chat-message <?php echo $message['sender_type'] == 'admin' ? 'outgoing' : 'incoming'; ?>">
                                    <div class="chat-message-content">
                                        <?php echo nl2br(htmlspecialchars($message['message'])); ?>
                                    </div>
                                    <div class="chat-message-time">
                                        <?php echo date('M d, H:i', strtotime($message['created_at'])); ?> -
                                        <?php echo htmlspecialchars($message['sender_name']); ?>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>

                            <!-- Chat Input -->
                            <div class="chat-input">
                                <?php if ($ticket_id && ($current_status === 'closed' || $current_status === 'resolved')): ?>
                                <div class="alert alert-secondary mb-0 text-center">
                                    <i class="fas fa-lock mr-2"></i> This ticket is <?php echo $current_status; ?>. You
                                    cannot send new
                                    messages.
                                </div>
                                <?php else: ?>
                                <form id="chatForm" method="post" onsubmit="sendMessage(event)">
                                    <input type="hidden" id="user_id" name="user_id"
                                        value="<?php echo $chat_user_id; ?>">
                                    <input type="hidden" id="ticket_id" name="ticket_id"
                                        value="<?php echo $ticket_id ? $ticket_id : ''; ?>">
                                    <input type="hidden" id="ticket_status"
                                        value="<?php echo $ticket_id ? $current_status : ''; ?>">
                                    <input type="text" id="message" name="message" placeholder="Type a message..."
                                        required autocomplete="off"
                                        <?php echo ($ticket_id && $current_status === 'closed') ? 'disabled' : ''; ?>>
                                    <button type="submit"
                                        <?php echo ($ticket_id && $current_status === 'closed') ? 'disabled' : ''; ?>>
                                        <i class="fas fa-paper-plane"></i> &nbsp; Send
                                    </button>
                                </form>
                                <?php endif; ?>
                            </div>
                            <?php else: ?>
                            <!-- Empty State -->
                            <div class="chat-empty">
                                <i class="fas fa-comments"></i>
                                <p>Select a user to start chatting</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script src="../js/chat-notifications.js"></script>
    <script>
    // Scroll to bottom of chat messages
    function scrollToBottom() {
        const chatMessages = document.getElementById('chatMessages');
        if (chatMessages) {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
    }

    // Format admin message time
    function formatAdminMessageTime(utcTimeString) {
        try {
            // Parse UTC time and convert to local timezone
            const utcDate = new Date(utcTimeString + 'Z'); // Add Z to indicate UTC

            // Format in admin's local timezone
            return utcDate.toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        } catch (error) {
            console.error('Admin time formatting error:', error);
            return utcTimeString;
        }
    }

    // Format message HTML
    function formatMessage(message) {
        const messageType = message.sender_type === 'admin' ? 'outgoing' : 'incoming';
        const senderName = message.sender_name || (message.sender_type === 'admin' ? 'Admin' : 'User');
        const time = message.formatted_time || new Date(message.created_at).toLocaleString();

        return `
                <div class="chat-message ${messageType}">
                    <div class="chat-message-content">
                        ${message.message.replace(/\n/g, '<br>')}
                    </div>
                    <div class="chat-message-time">
                        ${time} - ${senderName}
                    </div>
                </div>
            `;
    }

    // Get current messages count
    let currentMessagesCount = <?php echo count($messages); ?>;
    console.log('Initial message count:', currentMessagesCount);

    // Function to fetch new messages
    function fetchMessages() {
        <?php if ($chat_user_id): ?>
        fetch('../merlion/get-messages?user_id=<?php echo $chat_user_id; ?>' + (
                <?php echo $ticket_id ? 'true' : 'false'; ?> ?
                '&ticket_id=<?php echo $ticket_id; ?>' : ''))
            .then(response => response.json())
            .then(data => {
                // Debug log to help troubleshoot
                console.log('Fetched messages:', data.messages.length, 'Current count:', currentMessagesCount);

                if (data.success && data.messages.length > currentMessagesCount) {
                    // Get chat container
                    const chatMessages = document.getElementById('chatMessages');

                    // Check if there are new messages from the user (not from admin)
                    let hasNewUserMessages = false;

                    // Add only new messages
                    for (let i = currentMessagesCount; i < data.messages.length; i++) {
                        const message = data.messages[i];
                        const messageHTML = formatMessage(message);
                        chatMessages.innerHTML += messageHTML;

                        // Check if this is a user message (not from admin)
                        if (message.sender_type === 'user') {
                            hasNewUserMessages = true;
                        }
                    }

                    // Play notification sound and show browser notification for new user messages
                    if (hasNewUserMessages && window.chatNotificationSystem) {
                        // Initialize notification system if not already done
                        if (!window.chatNotificationSystem.isInitialized) {
                            window.chatNotificationSystem.initialize();
                        }

                        const username = data.messages[currentMessagesCount].sender_name || 'User';
                        const message = data.messages[currentMessagesCount].message || 'New message';

                        // Notify with the first new message
                        window.chatNotificationSystem.notify(
                            message.substring(0, 100) + (message.length > 100 ? '...' : ''), {
                                title: `New message from ${username}`,
                                sender: username
                            }
                        );

                        // Update ticket notifications when new user messages arrive
                        setTimeout(updateTicketNotifications, 1000);
                    }

                    // Update count and scroll to bottom
                    currentMessagesCount = data.messages.length;
                    scrollToBottom();
                }
            })
            .catch(error => console.error('Error checking for new messages:', error));
        <?php endif; ?>
    }

    // Function to send message via AJAX
    function sendMessage(event) {
        event.preventDefault();

        const messageInput = document.getElementById('message');
        const message = messageInput.value.trim();
        const userId = document.getElementById('user_id').value;
        const ticketId = document.getElementById('ticket_id').value;
        const ticketStatus = document.getElementById('ticket_status').value;

        if (message === '') return;

        // Check if ticket is closed or resolved
        if (ticketStatus === 'closed' || ticketStatus === 'resolved') {
            showNotification(`Cannot send message. This ticket is ${ticketStatus}.`, 'warning');
            return;
        }

        // Create form data
        const formData = new FormData();
        formData.append('message', message);
        formData.append('user_id', userId);
        if (ticketId) {
            formData.append('ticket_id', ticketId);

            // Get current ticket status
            const ticketStatus = document.getElementById('ticket_status').value;

            // If ticket is not already in progress, set it to in_progress
            if (ticketStatus !== 'in_progress' && ticketStatus !== 'closed' && ticketStatus !== 'resolved') {
                formData.append('update_status', 'in_progress');
            }
        }

        // Send message via AJAX to send-message.php
        fetch('/helloit/merlion/send-message', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Always clear input field
                messageInput.value = '';

                if (data.success) {
                    // Add new message to chat if message data is available
                    if (data.message) {
                        const chatMessages = document.getElementById('chatMessages');
                        const messageHTML = formatMessage(data.message);
                        chatMessages.innerHTML += messageHTML;

                        // Increment message count and scroll to bottom
                        currentMessagesCount++;
                        scrollToBottom();
                    }

                    // If status was updated, update the status badge in the UI
                    if (data.status_updated) {
                        // Update the status badge in the chat header and ticket list
                        const statusBadges = document.querySelectorAll('.ticket-badges .badge');
                        statusBadges.forEach(badge => {
                            if (
                                badge.classList.contains('badge-open') ||
                                badge.textContent.trim().toLowerCase() === 'open'
                            ) {
                                badge.classList.remove('badge-open');
                                badge.classList.add('badge-in_progress');
                                badge.textContent = 'In_progress';
                            }
                        });
                        // Also update the hidden ticket_status input if present
                        const ticketStatusInput = document.getElementById('ticket_status');
                        if (ticketStatusInput) {
                            ticketStatusInput.value = 'in_progress';
                        }
                    }

                    // Fetch messages to ensure we have the latest count
                    setTimeout(fetchMessages, 500);
                } else {
                    alert(data.error || 'Error sending message. Please try again.');
                    // Still fetch messages to check if the message was actually sent
                    setTimeout(fetchMessages, 500);
                }
            })
            .catch(error => {
                // Always clear input field even on error
                messageInput.value = '';
                console.error('Error sending message:', error);
                alert('Error sending message. Please try again.');
            });
    }

    // Add confirmation to status change links
    document.addEventListener('DOMContentLoaded', function() {
        const statusLinks = document.querySelectorAll('a[href*="update-ticket-status.php"]');
        statusLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                const status = this.href.includes('status=resolved') ? 'Resolved' : 'Closed';
                if (!confirm(
                        `Are you sure you want to change the ticket status to ${status}?`)) {
                    e.preventDefault();
                }
            });
        });
    });

    // Function to show notification
    function showNotification(message, type, duration = 5000) {
        // Remove any existing notifications
        const existingNotifications = document.querySelectorAll('.notification');
        existingNotifications.forEach(notification => notification.remove());

        // Create notification element
        const notification = document.createElement('div');
        notification.className = 'notification ' + type;
        notification.textContent = message;

        // Add to body
        document.body.appendChild(notification);

        // Show with animation
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);

        // Hide after duration
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, duration);
    }

    // Function to select a user and find the first open ticket
    function selectUser(userId) {
        // First check if there are any open tickets
        fetch(`get-user-tickets.php?user_id=${userId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let openTicket = null;

                    // First try to find a ticket that's not closed or resolved
                    for (const ticket of data.tickets) {
                        if (ticket.status !== 'closed' && ticket.status !== 'resolved') {
                            openTicket = ticket;
                            break;
                        }
                    }

                    // If no open ticket found, use the first ticket
                    if (!openTicket && data.tickets.length > 0) {
                        openTicket = data.tickets[0];
                    }

                    // Redirect to the appropriate URL
                    if (openTicket) {
                        window.location.href = `admin-chat.php?user_id=${userId}&ticket_id=${openTicket.id}`;
                    } else {
                        // If no tickets at all, just go to the user
                        window.location.href = `admin-chat.php?user_id=${userId}`;
                    }
                } else {
                    // If API call fails, fall back to just selecting the user
                    window.location.href = `admin-chat.php?user_id=${userId}`;
                }
            })
            .catch(error => {
                console.error('Error fetching tickets:', error);
                // If API call fails, fall back to just selecting the user
                window.location.href = `admin-chat.php?user_id=${userId}`;
            });
    }

    // --- Auto-update admin notification badge ---
    function updateAdminNotificationBadge() {
        fetch('admin-chat.php?action=get_admin_notifications', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                const badge = document.getElementById('admin-notification-badge');
                if (data.success && badge) {
                    if (data.unread_count > 0) {
                        badge.textContent = data.unread_count;
                        badge.style.display = 'inline-block';
                    } else {
                        badge.style.display = 'none';
                    }
                }
            })
            .catch(() => {});
    }

    // --- Auto-update Conversations list (sidebar) ---
    function updateChatUserList() {
        fetch('admin-chat.php?action=get_chat_user_list', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const chatList = document.querySelector('.chat-list');
                    if (!chatList) return;
                    let html = '';
                    if (data.users.length > 0) {
                        data.users.forEach(user => {
                            html += `<li class="chat-list-item${user.id == <?php echo json_encode($chat_user_id); ?> ? ' active' : ''}" onclick="selectUser(${user.id})">
                            <div class="chat-list-item-name">
                                <span>${user.username.replace(/</g, "&lt;")}</span>
                                ${user.unread_count > 0 ? `<span class="unread-badge">${user.unread_count}</span>` : ''}
                            </div>
                            <div class="chat-list-item-email">${user.email.replace(/</g, "&lt;")}</div>
                        </li>`;
                        });
                    } else {
                        html = '<li class="chat-list-item">No users found</li>';
                    }
                    chatList.innerHTML = html;
                }
            });
    }

    // --- Auto-update User's Tickets notifications ---
    function updateTicketNotifications() {
        <?php if ($chat_user_id): ?>
        console.log('Updating ticket notifications for user <?php echo $chat_user_id; ?>');
        fetch('admin-chat.php?action=get_ticket_notifications&user_id=<?php echo $chat_user_id; ?>', {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('Ticket notifications response:', data);
                if (data.success) {
                    data.tickets.forEach(ticket => {
                        console.log(
                            `Processing ticket ${ticket.id} with ${ticket.unread_count} unread messages`
                        );
                        const badge = document.getElementById('ticket-notification-' + ticket.id);

                        if (ticket.unread_count > 0) {
                            if (badge) {
                                // Update existing badge
                                console.log(`Updating existing badge for ticket ${ticket.id}`);
                                badge.textContent = ticket.unread_count >= 10 ? '9+' : ticket.unread_count;
                            } else {
                                // Create new badge
                                console.log(`Creating new badge for ticket ${ticket.id}`);
                                const ticketItem = document.querySelector(
                                    `li[onclick*="ticket_id=${ticket.id}"] .ticket-item-content`);
                                if (ticketItem) {
                                    const newBadge = document.createElement('span');
                                    newBadge.id = 'ticket-notification-' + ticket.id;
                                    newBadge.className = 'ticket-notification-badge';
                                    newBadge.textContent = ticket.unread_count >= 10 ? '9+' : ticket
                                        .unread_count;
                                    ticketItem.appendChild(newBadge);
                                    console.log(`Badge created for ticket ${ticket.id}`);
                                } else {
                                    console.log(`Could not find ticket item for ticket ${ticket.id}`);
                                }
                            }
                        } else {
                            // Remove badge if no unread messages
                            if (badge) {
                                console.log(`Removing badge for ticket ${ticket.id}`);
                                badge.remove();
                            }
                        }
                    });
                } else {
                    console.error('Ticket notifications request failed:', data);
                }
            })
            .catch(error => console.error('Error updating ticket notifications:', error));
        <?php else: ?>
        console.log('No chat user ID available for ticket notifications');
        <?php endif; ?>
    }

    // Poll every 5 seconds
    setInterval(updateChatUserList, 5000);
    setInterval(updateTicketNotifications, 5000);
    // Also call once on page load
    updateChatUserList();
    updateTicketNotifications();

    // Call on page load
    window.onload = function() {
        scrollToBottom();

        // Initialize notification system
        if (window.chatNotificationSystem) {
            window.chatNotificationSystem.initialize();

            // Request notification permission when page loads
            setTimeout(() => {
                window.chatNotificationSystem.requestPermission();
            }, 2000);
        }

        // Set up auto-refresh for messages only if a user is selected
        <?php if ($chat_user_id): ?>
        setInterval(fetchMessages, 3000);
        <?php endif; ?>

        // Check if the current ticket is closed and disable the chat input if needed
        const ticketStatus = document.getElementById('ticket_status')?.value;
        const messageInput = document.getElementById('message');

        if (ticketStatus === 'closed' && messageInput) {
            messageInput.disabled = true;
            messageInput.placeholder = 'This ticket is closed. You cannot send new messages.';

            const submitButton = messageInput.nextElementSibling;
            if (submitButton) {
                submitButton.disabled = true;
            }
        }
    };

    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const chatSidebar = document.querySelector('.chat-sidebar');
        const toggleChatSidebar = document.getElementById('toggleChatSidebar');
        const ticketHeader = document.getElementById('ticketHeader');
        const toggleTicketList = document.getElementById('toggleTicketList');
        const ticketList = document.getElementById('ticketList');

        // Initialize ticket list state based on screen size
        function initTicketListState() {
            if (window.innerWidth <= 767) {
                // On mobile, default to collapsed
                ticketList?.classList.remove('expanded');
                toggleTicketList?.classList.add('collapsed');
                toggleTicketList?.querySelector('i')?.classList.add('fa-chevron-right');
                toggleTicketList?.querySelector('i')?.classList.remove('fa-chevron-down');
            } else {
                // On desktop, default to expanded
                ticketList?.classList.remove('collapsed');
                toggleTicketList?.classList.remove('collapsed');
                toggleTicketList?.querySelector('i')?.classList.remove('fa-chevron-right');
                toggleTicketList?.querySelector('i')?.classList.add('fa-chevron-down');
            }
        }

        // Initialize on page load
        initTicketListState();

        // Toggle ticket list functionality
        if (ticketHeader && toggleTicketList && ticketList) {
            ticketHeader.addEventListener('click', function(e) {
                toggleTicketList.click();
            });

            toggleTicketList.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const icon = this.querySelector('i');

                if (window.innerWidth <= 767) {
                    // On mobile, toggle expanded class
                    ticketList.classList.toggle('expanded');
                    this.classList.toggle('collapsed');

                    if (ticketList.classList.contains('expanded')) {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-down');

                        // Ensure scrolling works by forcing a reflow
                        setTimeout(function() {
                            ticketList.style.overflow = 'hidden';
                            ticketList.offsetHeight; // Force reflow
                            ticketList.style.overflow = 'auto';
                        }, 10);
                    } else {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-right');
                    }
                } else {
                    // On desktop, toggle collapsed class
                    ticketList.classList.toggle('collapsed');
                    this.classList.toggle('collapsed');

                    if (ticketList.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-down');

                        // Ensure scrolling works by forcing a reflow
                        setTimeout(function() {
                            ticketList.style.overflow = 'hidden';
                            ticketList.offsetHeight; // Force reflow
                            ticketList.style.overflow = 'auto';
                        }, 10);
                    }
                }
            });
        }

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });

            // Toggle chat sidebar on mobile
            if (toggleChatSidebar) {
                toggleChatSidebar.addEventListener('click', function() {
                    chatSidebar.classList.toggle('collapsed');
                    const icon = this.querySelector('i');
                    if (chatSidebar.classList.contains('collapsed')) {
                        icon.classList.remove('fa-chevron-down');
                        icon.classList.add('fa-chevron-up');
                    } else {
                        icon.classList.remove('fa-chevron-up');
                        icon.classList.add('fa-chevron-down');
                    }
                });

                // If a chat is selected, collapse the sidebar by default on mobile
                <?php if ($chat_user_id): ?>
                chatSidebar.classList.add('collapsed');
                toggleChatSidebar.querySelector('i').classList.remove('fa-chevron-down');
                toggleChatSidebar.querySelector('i').classList.add('fa-chevron-up');
                <?php endif; ?>
            }
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');

                // Remove collapsed class from chat sidebar when returning to desktop
                if (chatSidebar) {
                    chatSidebar.classList.remove('collapsed');
                    if (toggleChatSidebar) {
                        toggleChatSidebar.querySelector('i').classList.remove('fa-chevron-up');
                        toggleChatSidebar.querySelector('i').classList.add('fa-chevron-down');
                    }
                }

                // Reset ticket list state for desktop
                if (ticketList) {
                    ticketList.classList.remove('expanded');
                    if (toggleTicketList) {
                        if (ticketList.classList.contains('collapsed')) {
                            toggleTicketList.classList.add('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-down');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-right');
                        } else {
                            toggleTicketList.classList.remove('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-right');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-down');
                        }
                    }
                }
            } else {
                // Reset ticket list state for mobile
                if (ticketList) {
                    ticketList.classList.remove('collapsed');
                    if (toggleTicketList) {
                        if (ticketList.classList.contains('expanded')) {
                            toggleTicketList.classList.remove('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-right');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-down');
                        } else {
                            toggleTicketList.classList.add('collapsed');
                            toggleTicketList.querySelector('i').classList.remove('fa-chevron-down');
                            toggleTicketList.querySelector('i').classList.add('fa-chevron-right');
                        }
                    }
                }
            }
        });
    });
    </script>
</body>

</html>