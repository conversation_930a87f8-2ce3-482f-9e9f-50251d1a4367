<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clipboard Debug Test - HelloIT</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
        padding: 20px;
    }

    .test-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .test-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-success { background-color: #28a745; }
    .status-warning { background-color: #ffc107; }
    .status-danger { background-color: #dc3545; }

    .copy-btn {
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .btn-success {
        animation: successPulse 0.3s ease-in-out;
    }

    @keyframes successPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .log-output {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        font-family: monospace;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        white-space: pre-wrap;
    }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Clipboard Functionality Debug Test</h1>

        <div class="test-section">
            <h2>Environment Check</h2>
            <div id="environmentStatus">
                <p><span class="status-indicator" id="httpsStatus"></span><strong>HTTPS/Secure Context:</strong> <span id="httpsText">Checking...</span></p>
                <p><span class="status-indicator" id="clipboardApiStatus"></span><strong>Clipboard API Support:</strong> <span id="clipboardApiText">Checking...</span></p>
                <p><span class="status-indicator" id="execCommandStatus"></span><strong>execCommand Support:</strong> <span id="execCommandText">Checking...</span></p>
                <p><strong>User Agent:</strong> <span id="userAgent"></span></p>
                <p><strong>Current URL:</strong> <span id="currentUrl"></span></p>
            </div>
        </div>

        <div class="test-section">
            <h2>Clipboard Test</h2>
            <p>Test the clipboard functionality with different methods:</p>

            <div class="row g-3">
                <div class="col-12">
                    <label class="form-label fw-bold">Test Text:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="Hello World Test 123" readonly>
                        <button class="btn btn-outline-secondary copy-btn" type="button"
                            onclick="testClipboard('Hello World Test 123', this)">
                            <i class="fas fa-copy"></i> Test Copy
                        </button>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label fw-bold">Username:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="user94642" readonly>
                        <button class="btn btn-outline-secondary copy-btn" type="button"
                            onclick="testClipboard('user94642', this)">
                            <i class="fas fa-copy"></i> Copy Username
                        </button>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label fw-bold">Email:</label>
                    <div class="input-group">
                        <input type="text" class="form-control" value="<EMAIL>" readonly>
                        <button class="btn btn-outline-secondary copy-btn" type="button"
                            onclick="testClipboard('<EMAIL>', this)">
                            <i class="fas fa-copy"></i> Copy Email
                        </button>
                    </div>
                </div>

                <div class="col-12">
                    <label class="form-label fw-bold">Password:</label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="passwordField" value="a60530ba" readonly>
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                            <i class="fas fa-eye" id="eyeIcon"></i>
                        </button>
                        <button class="btn btn-outline-secondary copy-btn" type="button"
                            onclick="testClipboard('a60530ba', this)">
                            <i class="fas fa-copy"></i> Copy Password
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Test Methods</h2>
            <div class="row">
                <div class="col-md-4">
                    <button class="btn btn-primary w-100 mb-2" onclick="testMethod1()">
                        Method 1: Clipboard API
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-secondary w-100 mb-2" onclick="testMethod2()">
                        Method 2: execCommand
                    </button>
                </div>
                <div class="col-md-4">
                    <button class="btn btn-warning w-100 mb-2" onclick="testMethod3()">
                        Method 3: Manual Prompt
                    </button>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Debug Log</h2>
            <div class="d-flex justify-content-between align-items-center mb-2">
                <h5 class="mb-0">Console Output:</h5>
                <button class="btn btn-sm btn-outline-secondary" onclick="clearLog()">Clear Log</button>
            </div>
            <div id="debugLog" class="log-output">Starting debug session...\n</div>
        </div>

        <div class="test-section">
            <h2>Solutions</h2>
            <div class="alert alert-info">
                <h5>Common Issues & Solutions:</h5>
                <ul class="mb-0">
                    <li><strong>HTTPS Required:</strong> Clipboard API only works on HTTPS or localhost</li>
                    <li><strong>User Interaction:</strong> Copy must be triggered by user action (click)</li>
                    <li><strong>Browser Support:</strong> Older browsers need execCommand fallback</li>
                    <li><strong>Permissions:</strong> Some browsers require clipboard permission</li>
                    <li><strong>Focus Issues:</strong> Element must be focused for execCommand</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Debug logging
    function log(message) {
        const logElement = document.getElementById('debugLog');
        const timestamp = new Date().toLocaleTimeString();
        logElement.textContent += `[${timestamp}] ${message}\n`;
        logElement.scrollTop = logElement.scrollHeight;
        console.log(message);
    }

    function clearLog() {
        document.getElementById('debugLog').textContent = 'Log cleared...\n';
    }

    // Environment check
    function checkEnvironment() {
        // Check HTTPS/Secure Context
        const isSecure = window.isSecureContext;
        const httpsStatus = document.getElementById('httpsStatus');
        const httpsText = document.getElementById('httpsText');

        if (isSecure) {
            httpsStatus.className = 'status-indicator status-success';
            httpsText.textContent = 'Secure (HTTPS or localhost)';
        } else {
            httpsStatus.className = 'status-indicator status-danger';
            httpsText.textContent = 'Not secure (HTTP)';
        }

        // Check Clipboard API
        const hasClipboardApi = navigator.clipboard && navigator.clipboard.writeText;
        const clipboardApiStatus = document.getElementById('clipboardApiStatus');
        const clipboardApiText = document.getElementById('clipboardApiText');

        if (hasClipboardApi) {
            clipboardApiStatus.className = 'status-indicator status-success';
            clipboardApiText.textContent = 'Supported';
        } else {
            clipboardApiStatus.className = 'status-indicator status-danger';
            clipboardApiText.textContent = 'Not supported';
        }

        // Check execCommand
        const hasExecCommand = document.queryCommandSupported && document.queryCommandSupported('copy');
        const execCommandStatus = document.getElementById('execCommandStatus');
        const execCommandText = document.getElementById('execCommandText');

        if (hasExecCommand) {
            execCommandStatus.className = 'status-indicator status-success';
            execCommandText.textContent = 'Supported';
        } else {
            execCommandStatus.className = 'status-indicator status-warning';
            execCommandText.textContent = 'Limited support';
        }

        // Other info
        document.getElementById('userAgent').textContent = navigator.userAgent;
        document.getElementById('currentUrl').textContent = window.location.href;

        log(`Environment check complete - Secure: ${isSecure}, Clipboard API: ${hasClipboardApi}, execCommand: ${hasExecCommand}`);
    }

    // Test methods
    function testMethod1() {
        log('Testing Method 1: Clipboard API');
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText('Test from Clipboard API').then(() => {
                log('✅ Clipboard API: SUCCESS');
                alert('Clipboard API test successful!');
            }).catch(err => {
                log('❌ Clipboard API: FAILED - ' + err.message);
                alert('Clipboard API test failed: ' + err.message);
            });
        } else {
            log('❌ Clipboard API: Not available');
            alert('Clipboard API not available');
        }
    }

    function testMethod2() {
        log('Testing Method 2: execCommand');
        const textArea = document.createElement("textarea");
        textArea.value = 'Test from execCommand';
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                log('✅ execCommand: SUCCESS');
                alert('execCommand test successful!');
            } else {
                log('❌ execCommand: FAILED - returned false');
                alert('execCommand test failed');
            }
        } catch (err) {
            document.body.removeChild(textArea);
            log('❌ execCommand: ERROR - ' + err.message);
            alert('execCommand test error: ' + err.message);
        }
    }

    function testMethod3() {
        log('Testing Method 3: Manual prompt');
        const text = 'Test from manual prompt';
        prompt('Copy this text manually (Ctrl+C):', text);
        log('✅ Manual prompt: Displayed');
    }

    // Enhanced clipboard function with fallbacks (same as payment-success.php)
    function copyToClipboardAdvanced(text, button) {
        log(`Attempting to copy: "${text}"`);

        // Try modern clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            log('Using Clipboard API');
            return navigator.clipboard.writeText(text).then(function() {
                log('✅ Clipboard API: SUCCESS');
                showCopySuccess(button);
                return true;
            }).catch(function(err) {
                log('❌ Clipboard API failed: ' + err.message + ', trying fallback');
                return fallbackCopyTextToClipboard(text, button);
            });
        } else {
            log('Clipboard API not available, using fallback');
            return fallbackCopyTextToClipboard(text, button);
        }
    }

    // Fallback copy method
    function fallbackCopyTextToClipboard(text, button) {
        log('Using execCommand fallback');
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                log('✅ execCommand: SUCCESS');
                showCopySuccess(button);
                return Promise.resolve(true);
            } else {
                throw new Error('execCommand returned false');
            }
        } catch (err) {
            document.body.removeChild(textArea);
            log('❌ execCommand failed: ' + err.message + ', showing manual prompt');

            // Final fallback - show text in prompt for manual copy
            const userAgent = navigator.userAgent.toLowerCase();
            if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
                alert('Please copy this manually:\n\n' + text);
            } else {
                prompt('Copy this text manually (Ctrl+C):', text);
            }
            return Promise.resolve(false);
        }
    }

    // Show visual success feedback
    function showCopySuccess(button) {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i> Copied!';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }

    // Test clipboard function
    function testClipboard(text, button) {
        copyToClipboardAdvanced(text, button);
    }

    // Toggle password visibility
    function togglePassword() {
        const passwordField = document.getElementById('passwordField');
        const eyeIcon = document.getElementById('eyeIcon');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            eyeIcon.classList.remove('fa-eye');
            eyeIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            eyeIcon.classList.remove('fa-eye-slash');
            eyeIcon.classList.add('fa-eye');
        }
    }

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', function() {
        checkEnvironment();
        log('Page loaded and ready for testing');
    });
    </script>
</body>
</html>
