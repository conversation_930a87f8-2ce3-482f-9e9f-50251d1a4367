<?php
require_once '../vendor/autoload.php';
include('server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

/**
 * Enforce 2-card limit: remove oldest payment method if user has more than 2
 */
function enforcePaymentMethodLimit($stripe_customer_id) {
    try {
        // Get all payment methods for this customer
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        echo "<h4>Customer: $stripe_customer_id</h4>";
        echo "<p>Current payment methods: " . count($payment_methods->data) . "</p>";

        // If user has more than 2 payment methods, remove the oldest ones
        if (count($payment_methods->data) > 2) {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>⚠️ Limit exceeded! Removing oldest payment methods...</strong><br>";
            
            // Sort by created timestamp (oldest first)
            $methods_array = $payment_methods->data;
            usort($methods_array, function($a, $b) {
                return $a->created - $b->created;
            });

            // Remove oldest methods until we have exactly 2
            $methods_to_remove = count($methods_array) - 2;
            for ($i = 0; $i < $methods_to_remove; $i++) {
                $oldest_method = $methods_array[$i];
                echo "Removing: {$oldest_method->id} (Created: " . date('Y-m-d H:i:s', $oldest_method->created) . ")<br>";
                $oldest_method->detach();
                error_log("Enforced 2-card limit: Removed oldest payment method {$oldest_method->id} for customer {$stripe_customer_id}");
            }
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<strong>✅ Within limit</strong> - User has " . count($payment_methods->data) . " payment methods (limit: 2)";
            echo "</div>";
        }

        // Show remaining payment methods
        $remaining_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);

        echo "<h5>Remaining Payment Methods (" . count($remaining_methods->data) . "):</h5>";
        echo "<ul>";
        foreach ($remaining_methods->data as $method) {
            $card = $method->card;
            echo "<li>{$method->id} - {$card->brand} ****{$card->last4} (Created: " . date('Y-m-d H:i:s', $method->created) . ")</li>";
        }
        echo "</ul>";

    } catch (\Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>❌ Error:</strong> " . $e->getMessage();
        echo "</div>";
        error_log("Error enforcing payment method limit: " . $e->getMessage());
    }
}

echo "<h2>🔧 Payment Method Limit Test</h2>";

// Test with a specific customer ID (replace with actual customer ID)
$test_customer_id = $_GET['customer_id'] ?? '';

if (empty($test_customer_id)) {
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>📋 How to Test:</h3>";
    echo "<ol>";
    echo "<li><strong>Find a customer ID:</strong> Go to payment-methods.php and inspect a user's Stripe customer ID</li>";
    echo "<li><strong>Add customer ID to URL:</strong> ?customer_id=cus_XXXXXXXXXX</li>";
    echo "<li><strong>Run test:</strong> This will show current payment methods and enforce the 2-card limit</li>";
    echo "</ol>";
    echo "<p><strong>Example:</strong> test-payment-method-limit.php?customer_id=cus_ABC123</p>";
    echo "</div>";

    // Show some example customers from database
    echo "<h3>🔍 Available Test Customers:</h3>";
    $query = "SELECT id, username, email, stripe_customer_id FROM user WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != '' LIMIT 5";
    $result = mysqli_query($conn, $query);
    
    if ($result && mysqli_num_rows($result) > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>User ID</th><th>Username</th><th>Email</th><th>Stripe Customer ID</th><th>Test Link</th></tr>";
        while ($row = mysqli_fetch_assoc($result)) {
            $customer_id = htmlspecialchars($row['stripe_customer_id']);
            $test_link = "?customer_id=" . urlencode($customer_id);
            echo "<tr>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['username']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>{$customer_id}</td>";
            echo "<td><a href='$test_link' style='color: #007bff;'>Test Limit</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No customers with Stripe customer IDs found in database.</p>";
    }
} else {
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h3>🧪 Testing Payment Method Limit for Customer: $test_customer_id</h3>";
    
    try {
        // Verify customer exists
        $customer = \Stripe\Customer::retrieve($test_customer_id);
        echo "<p><strong>Customer Email:</strong> {$customer->email}</p>";
        echo "<p><strong>Customer Name:</strong> {$customer->name}</p>";
        
        // Test the limit enforcement
        enforcePaymentMethodLimit($test_customer_id);
        
    } catch (\Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px;'>";
        echo "<strong>❌ Error:</strong> " . $e->getMessage();
        echo "</div>";
    }
    echo "</div>";
}

echo "<hr>";
echo "<h3>📊 Feature Summary</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px;'>";
echo "<h4>✨ What was implemented:</h4>";
echo "<ul>";
echo "<li>✅ <strong>2-card limit enforcement:</strong> Automatically removes oldest payment methods when user exceeds 2 cards</li>";
echo "<li>✅ <strong>Integrated into all payment flows:</strong> Webhook, payment-success.php, and cart-payment-success.php</li>";
echo "<li>✅ <strong>Smart oldest-first removal:</strong> Sorts by creation timestamp and removes oldest cards first</li>";
echo "<li>✅ <strong>Comprehensive logging:</strong> All actions are logged for debugging</li>";
echo "<li>✅ <strong>Error handling:</strong> Graceful failure if limit enforcement fails</li>";
echo "</ul>";

echo "<h4>🔧 Integration Points:</h4>";
echo "<ul>";
echo "<li><strong>stripe-webhook.php:</strong> Enforces limit when non-login users complete purchases</li>";
echo "<li><strong>payment-success.php:</strong> Enforces limit for logged-in user purchases</li>";
echo "<li><strong>cart-payment-success.php:</strong> Enforces limit for cart-based purchases</li>";
echo "</ul>";

echo "<h4>📝 How it works:</h4>";
echo "<ol>";
echo "<li>User completes purchase with new payment method</li>";
echo "<li>System attaches payment method to Stripe customer</li>";
echo "<li><strong>enforcePaymentMethodLimit()</strong> function runs automatically</li>";
echo "<li>If user has >2 payment methods, oldest ones are removed</li>";
echo "<li>User ends up with exactly 2 payment methods maximum</li>";
echo "</ol>";
echo "</div>";
?>
