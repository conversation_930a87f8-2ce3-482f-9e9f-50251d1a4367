<?php
include('server.php');

echo "<h2>🧪 Test Payment Success Page Fix</h2>";

// Test the new logic with the actual email
$customer_email = '<EMAIL>';

echo "<h3>Testing with email: $customer_email</h3>";

// Step 1: Get the most recent user for this email (same logic as payment success page)
$recent_user_stmt = $conn->prepare("SELECT id, username, email, registration_time FROM user WHERE email = ? ORDER BY id DESC LIMIT 1");
$recent_user_stmt->bind_param("s", $customer_email);
$recent_user_stmt->execute();
$recent_user_result = $recent_user_stmt->get_result();

if ($recent_user_result->num_rows > 0) {
    $user_data = $recent_user_result->fetch_assoc();
    $username = $user_data['username'];
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>✅ Step 1: Found Recent User</h4>";
    echo "<p><strong>ID:</strong> " . $user_data['id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $username . "</p>";
    echo "<p><strong>Email:</strong> " . $user_data['email'] . "</p>";
    echo "<p><strong>Registration:</strong> " . $user_data['registration_time'] . "</p>";
    echo "</div>";
    
    // Step 2: Find the password that will work for this user
    $password_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1");
    $password_stmt->bind_param("s", $username);
    $password_stmt->execute();
    $password_result = $password_stmt->get_result();
    
    if ($password_result->num_rows > 0) {
        $password_data = $password_result->fetch_assoc();
        $password = $password_data['password'];
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Step 2: Found Password</h4>";
        echo "<p><strong>Password:</strong> " . $password . "</p>";
        echo "</div>";
        
        // Step 3: Test if these credentials would work for sign-in
        $signin_test_stmt = $conn->prepare("SELECT id, username, email, password FROM user WHERE username = ?");
        $signin_test_stmt->bind_param("s", $username);
        $signin_test_stmt->execute();
        $signin_test_result = $signin_test_stmt->get_result();
        
        if ($signin_test_result->num_rows > 0) {
            $signin_user = $signin_test_result->fetch_assoc();
            $stored_hash = $signin_user['password'];
            
            // Test password verification
            if (strpos($stored_hash, '$2y$') === 0) {
                $verify_result = password_verify($password, $stored_hash);
                $hash_type = 'bcrypt';
            } else {
                $verify_result = ($stored_hash === md5($password));
                $hash_type = 'md5';
            }
            
            if ($verify_result) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Step 3: Sign-In Test PASSED</h4>";
                echo "<p>These credentials will work for sign-in ($hash_type verification)</p>";
                echo "</div>";
                
                echo "<div style='background: #e3f2fd; border: 2px solid #2196f3; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
                echo "<h4>🎯 FINAL RESULT - Payment Success Page Will Show:</h4>";
                echo "<p><strong>Username:</strong> " . $username . "</p>";
                echo "<p><strong>Email:</strong> " . $customer_email . "</p>";
                echo "<p><strong>Password:</strong> " . $password . "</p>";
                echo "<p style='color: green;'><strong>✅ These credentials WILL WORK for sign-in!</strong></p>";
                echo "</div>";
                
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>❌ Step 3: Sign-In Test FAILED</h4>";
                echo "<p>Password verification failed ($hash_type)</p>";
                echo "<p><strong>Stored hash:</strong> " . substr($stored_hash, 0, 30) . "...</p>";
                echo "<p><strong>Test password:</strong> $password</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>⚠️ Step 3: User Not Found in Main Table</h4>";
            echo "<p>User exists in payment_temp but not in main user table (webhook pending)</p>";
            echo "</div>";
        }
        
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Step 2: No Password Found</h4>";
        echo "<p>Would generate new password for user: $username</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Step 1: No User Found</h4>";
    echo "<p>No user found for email: $customer_email</p>";
    echo "<p>Payment success page would show 'Account Being Set Up' message</p>";
    echo "</div>";
}

// Show comparison with old vs new approach
echo "<h3>📊 Comparison: Old vs New Approach</h3>";

echo "<div style='display: flex; gap: 20px;'>";

// Old approach (what was causing the problem)
echo "<div style='flex: 1; background: #f8d7da; padding: 15px; border-radius: 5px;'>";
echo "<h5>❌ Old Approach (BROKEN)</h5>";
echo "<ol>";
echo "<li>Find by session_id (fails)</li>";
echo "<li>Find recent user by email</li>";
echo "<li>Find password by username</li>";
echo "<li><strong>Problem:</strong> Finds old password for different user</li>";
echo "</ol>";
echo "<p><strong>Result:</strong> Shows user88933/7b19f462 (wrong!)</p>";
echo "</div>";

// New approach
echo "<div style='flex: 1; background: #d4edda; padding: 15px; border-radius: 5px;'>";
echo "<h5>✅ New Approach (FIXED)</h5>";
echo "<ol>";
echo "<li>Find most recent user by email</li>";
echo "<li>Find password for THAT SPECIFIC user</li>";
echo "<li>Verify credentials will work</li>";
echo "<li><strong>Solution:</strong> Always shows working credentials</li>";
echo "</ol>";
echo "<p><strong>Result:</strong> Shows user19599/c5b184c2 (correct!)</p>";
echo "</div>";

echo "</div>";
?>

<h3>🔧 Next Steps</h3>
<ol>
    <li><strong>Clear browser cache</strong> completely</li>
    <li><strong>Visit payment success page</strong> again</li>
    <li><strong>Should now show correct credentials</strong> (user19599/c5b184c2)</li>
    <li><strong>Test sign-in</strong> with those credentials</li>
</ol>

<h3>🔗 Test Links</h3>
<p><a href="../front-end/payment-success.php?session_id=cs_test_b1LT44RjjAD5R07TwNDhtxbHjBSAIqef6RQCkLvoM56gjQplXolRHIOxWB" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Test Payment Success Page</a></p>
<p><a href="../front-end/sign-in.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Sign-In</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
</style>
