<?php
include('server.php');

echo "<h2>Quick Database Debug</h2>";

// Get the most recent user
echo "<h3>Most Recent User in user table:</h3>";
$recent_user_query = "SELECT id, username, email, password, registration_time FROM user ORDER BY id DESC LIMIT 1";
$recent_user_result = mysqli_query($conn, $recent_user_query);

if ($recent_user_result && mysqli_num_rows($recent_user_result) > 0) {
    $user = mysqli_fetch_assoc($recent_user_result);
    echo "<p><strong>ID:</strong> " . $user['id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $user['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $user['email'] . "</p>";
    echo "<p><strong>Password Hash:</strong> " . $user['password'] . "</p>";
    echo "<p><strong>Hash Type:</strong> " . (strpos($user['password'], '$2y$') === 0 ? 'bcrypt' : 'md5') . "</p>";
    echo "<p><strong>Registration Time:</strong> " . $user['registration_time'] . "</p>";

    // Check payment_temp for this user
    echo "<h3>Payment Temp for this user:</h3>";
    $temp_query = "SELECT * FROM payment_temp WHERE username = '" . $user['username'] . "' OR email = '" . $user['email'] . "' ORDER BY id DESC LIMIT 1";
    $temp_result = mysqli_query($conn, $temp_query);

    if ($temp_result && mysqli_num_rows($temp_result) > 0) {
        $temp = mysqli_fetch_assoc($temp_result);
        echo "<p><strong>Session ID:</strong> " . $temp['session_id'] . "</p>";
        echo "<p><strong>Username:</strong> " . $temp['username'] . "</p>";
        echo "<p><strong>Email:</strong> " . $temp['email'] . "</p>";
        echo "<p><strong>Raw Password:</strong> " . $temp['password'] . "</p>";

        // Test password verification
        echo "<h3>Password Verification Test:</h3>";
        $raw_password = $temp['password'];
        $stored_hash = $user['password'];

        if (strpos($stored_hash, '$2y$') === 0) {
            $verify_result = password_verify($raw_password, $stored_hash);
            echo "<p><strong>bcrypt verify result:</strong> " . ($verify_result ? 'PASS ✅' : 'FAIL ❌') . "</p>";
        } else {
            $verify_result = ($stored_hash === md5($raw_password));
            echo "<p><strong>MD5 verify result:</strong> " . ($verify_result ? 'PASS ✅' : 'FAIL ❌') . "</p>";
        }

        echo "<h3>Test Credentials:</h3>";
        echo "<p><strong>Try signing in with:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Username:</strong> " . $user['username'] . "</li>";
        echo "<li><strong>Email:</strong> " . $user['email'] . "</li>";
        echo "<li><strong>Password:</strong> " . $temp['password'] . "</li>";
        echo "</ul>";

    } else {
        echo "<p>❌ No payment_temp record found for this user</p>";
    }
} else {
    echo "<p>❌ No users found in database</p>";
}

// Also check recent payment_temp entries
echo "<h3>Recent Payment Temp Entries:</h3>";
$recent_temp_query = "SELECT * FROM payment_temp ORDER BY id DESC LIMIT 5";
$recent_temp_result = mysqli_query($conn, $recent_temp_query);

if ($recent_temp_result && mysqli_num_rows($recent_temp_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Session ID</th><th>Username</th><th>Email</th><th>Password</th><th>User Exists?</th></tr>";
    while ($temp_row = mysqli_fetch_assoc($recent_temp_result)) {
        // Check if this user exists in main table
        $check_user_query = "SELECT id FROM user WHERE username = '" . $temp_row['username'] . "'";
        $check_user_result = mysqli_query($conn, $check_user_query);
        $user_exists = mysqli_num_rows($check_user_result) > 0;

        echo "<tr>";
        echo "<td>" . $temp_row['id'] . "</td>";
        echo "<td>" . substr($temp_row['session_id'], 0, 20) . "...</td>";
        echo "<td>" . $temp_row['username'] . "</td>";
        echo "<td>" . $temp_row['email'] . "</td>";
        echo "<td>" . $temp_row['password'] . "</td>";
        echo "<td>" . ($user_exists ? '✅ YES' : '❌ NO') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No payment_temp entries found</p>";
}

// Check specifically for user46281
echo "<h3>Check for user46281:</h3>";
$check_46281_user = "SELECT * FROM user WHERE username = 'user46281'";
$result_46281_user = mysqli_query($conn, $check_46281_user);

$check_46281_temp = "SELECT * FROM payment_temp WHERE username = 'user46281'";
$result_46281_temp = mysqli_query($conn, $check_46281_temp);

echo "<p><strong>user46281 in user table:</strong> " . (mysqli_num_rows($result_46281_user) > 0 ? '✅ YES' : '❌ NO') . "</p>";
echo "<p><strong>user46281 in payment_temp:</strong> " . (mysqli_num_rows($result_46281_temp) > 0 ? '✅ YES' : '❌ NO') . "</p>";

if (mysqli_num_rows($result_46281_temp) > 0) {
    $temp_46281 = mysqli_fetch_assoc($result_46281_temp);
    echo "<p><strong>Session ID:</strong> " . $temp_46281['session_id'] . "</p>";
    echo "<p><strong>Password:</strong> " . $temp_46281['password'] . "</p>";
}
?>

<h3>Manual Test</h3>
<p><a href="../front-end/sign-in.php">Go to Sign-In Page</a></p>
<p><a href="test-signin-step-by-step.php">Use Step-by-Step Test Tool</a></p>
