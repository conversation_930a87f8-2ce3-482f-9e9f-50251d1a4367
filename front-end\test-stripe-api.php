<?php
// Test Stripe API functionality in webhook context
require '../vendor/autoload.php';

// Set Stripe API key (same as webhook)
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

echo "<h1>Stripe API Test (Webhook Context)</h1>";

echo "<h2>1. Test Stripe Customer Creation</h2>";
try {
    $test_customer = \Stripe\Customer::create([
        'email' => 'test-webhook-' . time() . '@example.com',
        'name' => 'Test Webhook Customer',
        'address' => [
            'line1' => '123 Test Street',
            'city' => 'Bangkok',
            'state' => 'Bangkok',
            'postal_code' => '10110',
            'country' => 'TH',
        ],
        'metadata' => [
            'source' => 'webhook_test',
            'test_time' => date('Y-m-d H:i:s')
        ]
    ]);
    
    echo "<p style='color: green;'>✓ Stripe customer creation successful!</p>";
    echo "<p><strong>Customer ID:</strong> " . $test_customer->id . "</p>";
    echo "<p><strong>Email:</strong> " . $test_customer->email . "</p>";
    echo "<p><strong>Name:</strong> " . $test_customer->name . "</p>";
    
    // Clean up - delete the test customer
    $test_customer->delete();
    echo "<p style='color: green;'>✓ Test customer deleted successfully</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Stripe customer creation failed: " . $e->getMessage() . "</p>";
}

echo "<h2>2. Test Stripe Session Retrieval</h2>";
try {
    // Test retrieving a recent session (this will fail if no recent sessions exist)
    $sessions = \Stripe\Checkout\Session::all(['limit' => 1]);
    if (count($sessions->data) > 0) {
        $session = $sessions->data[0];
        echo "<p style='color: green;'>✓ Stripe session retrieval successful</p>";
        echo "<p><strong>Recent Session ID:</strong> " . $session->id . "</p>";
        echo "<p><strong>Status:</strong> " . $session->status . "</p>";
    } else {
        echo "<p style='color: orange;'>⚠ No recent sessions found (this is normal)</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Stripe session retrieval failed: " . $e->getMessage() . "</p>";
}

echo "<h2>3. Webhook Status</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>✓ Stripe API Key is properly set in webhook</strong></p>";
echo "<p><strong>✓ Customer creation functionality is working</strong></p>";
echo "<p><strong>Next:</strong> Test a real purchase to verify end-to-end functionality</p>";
echo "</div>";

echo "<h2>4. Recent Webhook Logs</h2>";
$log_file = __DIR__ . '/webhook.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -10); // Last 10 lines
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>No webhook log file found.</p>";
}
?>
