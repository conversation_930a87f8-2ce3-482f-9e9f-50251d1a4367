<?php
session_start();
include('../functions/server.php');

if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Open Ticket</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
        /* Additional CSS to ensure full page height */
        html,
        body {
            height: 100%;
            margin: 0;
        }

        .full-height {
            height: 100vh;
            /* Full viewport height */
        }
    </style>
</head>

<body data-theme="light">

    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>

            <?php  ?>
            <div class="inner-banner pt-29 pb-md-13 bg-default-2">
                <div class="container">

                </div>
            </div>
            <div class="bg-default-2 pb-17 pb-md-29 ">
                <div class="container">
                    <div class="row justify-content-md-between pt-9">
                        <div class="col-sm-6 col-md-5 col-lg-4 col-xl-3">
                            <div class="btn btn-white border">Details</div>
                        </div>
                    </div>
                    <div class="cart-details-main-block" id="dynamic-cart">
                        <!-- .cart_single-product-block starts -->
                        <div style="text-align: center;">
                            <a href="javascript:void(0);" class="btn btn-primary with-icon gr-hover-y px-xl-8 px-lg-4 px-sm-8 px-4" onclick="useTicket()">Open Now<i class="icon icon-tail-right font-weight-bold"></i></a>
                        </div>
                        <!-- .cart_single-product-block ends -->
                    </div>
                </div>
            </div>
            <div class="inner-banner pt-35 pb-md-20 bg-default-2">
                <div class="container">

                </div>
            </div>
        <?php else : ?>
            <div class="d-flex justify-content-center align-items-center full-height">
                <div class="text-center">
                    <?php echo "You are not logged in."; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
    <script>
        function useTicket() {

            var xhr = new XMLHttpRequest();
            xhr.open("POST", "/functions/use-ticket.php", true);
            xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");

            var username = '<?php echo $_SESSION['username']; ?>';
            xhr.send("username=" + username);

            xhr.onload = function() {
                if (xhr.status === 200) {
                    var response = JSON.parse(xhr.responseText);

                    if (response.status === "success") {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                } else {
                    alert("Unexpected error occurred.");
                }
            };
        }
    </script>
    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>
</body>

</html>