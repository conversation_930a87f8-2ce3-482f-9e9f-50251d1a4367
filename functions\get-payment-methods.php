<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$username = $_SESSION['username'];
$user_query = "SELECT id, stripe_customer_id FROM user WHERE username = '$username'";
$user_result = mysqli_query($conn, $user_query);

if (!$user_result || mysqli_num_rows($user_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

$user = mysqli_fetch_assoc($user_result);
$user_id = $user['id'];
$customer_id = $user['stripe_customer_id'];

// If no customer ID, return empty list
if (empty($customer_id)) {
    echo json_encode(['success' => true, 'payment_methods' => []]);
    exit;
}

try {
    // Get payment methods from database
    $payment_methods_query = "SELECT * FROM payment_methods WHERE user_id = $user_id ORDER BY is_default DESC, created_at DESC";
    $payment_methods_result = mysqli_query($conn, $payment_methods_query);
    
    $payment_methods = [];
    
    if ($payment_methods_result && mysqli_num_rows($payment_methods_result) > 0) {
        while ($method = mysqli_fetch_assoc($payment_methods_result)) {
            $payment_methods[] = [
                'id' => $method['id'],
                'payment_method_id' => $method['payment_method_id'],
                'card_last4' => $method['card_last4'],
                'card_brand' => $method['card_brand'],
                'card_exp_month' => $method['card_exp_month'],
                'card_exp_year' => $method['card_exp_year'],
                'is_default' => (bool)$method['is_default']
            ];
        }
    }
    
    echo json_encode(['success' => true, 'payment_methods' => $payment_methods]);
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}
?>
