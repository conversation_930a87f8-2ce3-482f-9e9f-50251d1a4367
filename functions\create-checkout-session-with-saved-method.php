<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

header('Content-Type: application/json');

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get data from POST request
$data = json_decode(file_get_contents('php://input'), true);
$packagesize = $data['packagesize'];
$tickettype = $data['tickettype'];
$payment_method_id = $data['payment_method_id'];

// Sanitize inputs to prevent SQL injection
$packagesize = mysqli_real_escape_string($conn, $packagesize);
$tickettype = mysqli_real_escape_string($conn, $tickettype);
$payment_method_id = mysqli_real_escape_string($conn, $payment_method_id);

// Get user information
$username = $_SESSION['username'];
$user_query = "SELECT id, email, stripe_customer_id FROM user WHERE username = '$username'";
$user_result = mysqli_query($conn, $user_query);

if (!$user_result || mysqli_num_rows($user_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'User not found']);
    exit;
}

$user = mysqli_fetch_assoc($user_result);
$user_id = $user['id'];
$user_email = $user['email'];
$customer_id = $user['stripe_customer_id'];

if (empty($customer_id)) {
    echo json_encode(['success' => false, 'message' => 'No Stripe customer ID found for this user']);
    exit;
}

// Verify that the payment method belongs to this user
$payment_method_query = "SELECT * FROM payment_methods WHERE user_id = $user_id AND payment_method_id = '$payment_method_id'";
$payment_method_result = mysqli_query($conn, $payment_method_query);

if (!$payment_method_result || mysqli_num_rows($payment_method_result) == 0) {
    echo json_encode(['success' => false, 'message' => 'Payment method not found or does not belong to this user']);
    exit;
}

// Calculate price based on package size and ticket type
$price = 0;
if ($packagesize == 'packageXS') {
    $price = 2000; // $20.00
} else if ($packagesize == 'packageS') {
    $price = 5000; // $50.00
} else if ($packagesize == 'packageM') {
    $price = 10000; // $100.00
} else if ($packagesize == 'packageL') {
    $price = 20000; // $200.00
}

try {
    // Create a PaymentIntent
    $payment_intent = \Stripe\PaymentIntent::create([
        'amount' => $price,
        'currency' => 'usd',
        'customer' => $customer_id,
        'payment_method' => $payment_method_id,
        'off_session' => true,
        'confirm' => true,
        'metadata' => [
            'ticket_type' => $tickettype,
            'packagesize' => $packagesize
        ],
        'receipt_email' => $user_email,
        'description' => 'HelloIT Ticket - ' . $tickettype . ' Package Size ' . $packagesize
    ]);
    
    // If payment is successful, update the database
    if ($payment_intent->status === 'succeeded') {
        $transaction_id = $payment_intent->id;
        $payment_date = date('Y-m-d H:i:s');
        
        // Update user's ticket count
        $ticket_column = '';
        if ($tickettype == 'STARTER') {
            $ticket_column = 'starter_tickets';
        } else if ($tickettype == 'BUSINESS') {
            $ticket_column = 'premium_tickets';
        } else if ($tickettype == 'ULTIMATE') {
            $ticket_column = 'ultimate_tickets';
        }
        
        if (!empty($ticket_column)) {
            $update_sql = "UPDATE user SET $ticket_column = $ticket_column + 1 WHERE username = '$username'";
            mysqli_query($conn, $update_sql);
        }
        
        // Insert into purchase history
        $insert_sql = "INSERT INTO purchase_history (username, ticket_type, package_size, transaction_id, payment_date) 
                      VALUES ('$username', '$tickettype', '$packagesize', '$transaction_id', '$payment_date')";
        mysqli_query($conn, $insert_sql);
        
        echo json_encode([
            'success' => true,
            'payment_intent_id' => $payment_intent->id,
            'status' => $payment_intent->status
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Payment failed: ' . $payment_intent->status
        ]);
    }
} catch (\Stripe\Exception\CardException $e) {
    // Card was declined
    echo json_encode([
        'success' => false,
        'message' => 'Card declined: ' . $e->getMessage()
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>