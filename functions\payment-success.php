<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set your Stripe API keys
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

// Verify the session ID from the URL
$session_id = $_GET['session_id'];
$messageSuccess = '';
$messageFail = '';
$messagelog = '';

try {
    // Enable error logging
    ini_set('display_errors', 1);
    error_reporting(E_ALL);

    // Log the session ID for debugging
    error_log("Processing payment for session ID: " . $session_id);

    $session = \Stripe\Checkout\Session::retrieve([
        'id' => $session_id,
        'expand' => ['payment_intent', 'customer', 'payment_intent.payment_method']
    ]);
    $metadata = $session->metadata->toArray();

    // Log metadata for debugging
    error_log("Session metadata: " . print_r($metadata, true));

    $packagesize = $metadata['packagesize'] ?? 'not set';
    $tickettype = $metadata['ticket_type'] ?? 'not set';
    $transectionid = $session['id'] ?? 'not set';
    $save_payment_method = isset($metadata['save_payment_method']) && $metadata['save_payment_method'] === 'true';

    // Check if we need to save the payment method
    if ($save_payment_method && isset($_SESSION['username']) && $session->payment_intent && $session->payment_intent->payment_method) {
        try {
            $username = $_SESSION['username'];
            $payment_method = $session->payment_intent->payment_method;

            // Get user ID
            $user_query = "SELECT id FROM user WHERE username = '$username'";
            $user_result = mysqli_query($conn, $user_query);

            if ($user_result && mysqli_num_rows($user_result) > 0) {
                $user = mysqli_fetch_assoc($user_result);
                $user_id = $user['id'];

                // Save payment method details
                $payment_method_id = $payment_method->id;
                $card_last4 = $payment_method->card->last4;
                $card_brand = $payment_method->card->brand;
                $card_exp_month = $payment_method->card->exp_month;
                $card_exp_year = $payment_method->card->exp_year;

                // Check if this payment method already exists for this user
                $check_query = "SELECT id FROM payment_methods WHERE user_id = $user_id AND payment_method_id = '$payment_method_id'";
                $check_result = mysqli_query($conn, $check_query);

                if ($check_result && mysqli_num_rows($check_result) == 0) {
                    // This is a new payment method, save it

                    // First, check if user has any payment methods
                    $count_query = "SELECT COUNT(*) as count FROM payment_methods WHERE user_id = $user_id";
                    $count_result = mysqli_query($conn, $count_query);
                    $count_row = mysqli_fetch_assoc($count_result);
                    $is_default = ($count_row['count'] == 0) ? 1 : 0; // Make default if it's the first payment method

                    $insert_query = "INSERT INTO payment_methods (user_id, payment_method_id, card_last4, card_brand, card_exp_month, card_exp_year, is_default)
                                    VALUES ($user_id, '$payment_method_id', '$card_last4', '$card_brand', $card_exp_month, $card_exp_year, $is_default)";

                    if (mysqli_query($conn, $insert_query)) {
                        error_log("Payment method saved successfully: $payment_method_id");
                    } else {
                        error_log("Error saving payment method: " . mysqli_error($conn));
                    }
                } else {
                    error_log("Payment method already exists for this user: $payment_method_id");
                }
            }
        } catch (Exception $e) {
            error_log("Error saving payment method: " . $e->getMessage());
        }
    }

    error_log("Package Size: $packagesize, Ticket Type: $tickettype, Transaction ID: $transectionid");

    // First, check if this transaction has already been processed
    $check_query = "SELECT purchaseid FROM purchasetickets WHERE transactionid = ? LIMIT 1";
    $stmt = $conn->prepare($check_query);
    if (!$stmt) {
        error_log("Prepare failed for check query: " . $conn->error);
        throw new Exception("Database prepare error: " . $conn->error);
    }

    $stmt->bind_param("s", $transectionid);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        // Transaction already processed, just show success message
        error_log("Transaction already processed: $transectionid");
        $messageSuccess = "Thank you for your purchase with us! Your tickets have been added to your account.";
    } else {
        try {
            // Process the purchase
            error_log("Processing new purchase for transaction ID: $transectionid");
            buyprocess($packagesize, $tickettype, $transectionid);
            $messageSuccess = "Thank you for your purchase with us!";
            error_log("Purchase processed successfully");
        } catch (Exception $inner_e) {
            error_log("Exception in purchase processing: " . $inner_e->getMessage());
            // Even if there's an error, show success to the user since Stripe processed the payment
            $messageSuccess = "Thank you for your purchase with us! Your tickets have been added to your account.";
        }
    }
} catch (Exception $e) {
    error_log("Error in payment verification: " . $e->getMessage());
    $messageFail = "Error verifying payment: " . $e->getMessage();
}

function buyprocess($packagesize, $tickettype, $transectionid)
{
    include('../functions/server.php');
    global $messagelogpayment; // Declare $messagelog as global
    global $conn; // Make sure we have access to the connection

    // Enable error logging
    ini_set('display_errors', 1);
    error_reporting(E_ALL);

    error_log("Starting buyprocess for packagesize: $packagesize, tickettype: $tickettype, transectionid: $transectionid");

    if (!isset($_SESSION['username'])) {
        error_log("User not logged in. Session data: " . print_r($_SESSION, true));
        header('location: ../front-end/sign-in.php');
        exit();
    } else {
        error_log("Processing for username: " . $_SESSION['username']);

        // Sanitize inputs to prevent SQL injection
        $packagesize = mysqli_real_escape_string($conn, $packagesize);
        $tickettype = mysqli_real_escape_string($conn, $tickettype);
        $transectionid = mysqli_real_escape_string($conn, $transectionid);

        // Begin transaction
        $conn->begin_transaction();

        try {
            // Get ticket information using prepared statement
            $stmt = $conn->prepare("SELECT ticketid, ticket_type, package_size, numbers_per_package, dollar_price_per_package FROM tickets WHERE package_size = ? AND ticket_type = ?");
            if (!$stmt) {
                error_log("Prepare failed for ticket query: " . $conn->error);
                throw new Exception("Database prepare error: " . $conn->error);
            }

            $stmt->bind_param("ss", $packagesize, $tickettype);
            $stmt->execute();
            $resultshow = $stmt->get_result();

            if ($resultshow && $resultshow->num_rows > 0) {
                $ticket = $resultshow->fetch_assoc();
                $username = $_SESSION['username'];
                $ticket_type = $ticket['ticket_type'];
                $package_size = $ticket['package_size'];
                $numbers_per_package = $ticket['numbers_per_package'];
                $dollar_price_per_package = $ticket['dollar_price_per_package'];

                error_log("Ticket found: type=$ticket_type, package=$package_size, numbers=$numbers_per_package, price=$dollar_price_per_package");

                // Insert purchase record using prepared statement with UTC timestamp
                $purchase_time_utc = getCurrentUTC(); // Get UTC time for consistent storage
                $stmt = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                if (!$stmt) {
                    error_log("Prepare failed for insert: " . $conn->error);
                    throw new Exception("Database prepare error for insert: " . $conn->error);
                }

                $stmt->bind_param("sssiiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $purchase_time_utc, $transectionid, $numbers_per_package);
                $result = $stmt->execute();

                if (!$result) {
                    error_log("Insert failed: " . $stmt->error);
                    throw new Exception("Database insert error: " . $stmt->error);
                }

                error_log("Purchase record inserted successfully");

                // Debug ticket type
                error_log("Original ticket_type: '$ticket_type'");
                error_log("Uppercase ticket_type: '" . strtoupper($ticket_type) . "'");
                error_log("Lowercase ticket_type: '" . strtolower($ticket_type) . "'");

                // Update ticket count directly using prepared statement - using case-insensitive comparison with trim
                $ticket_type_upper = trim(strtoupper($ticket_type));
                error_log("Trimmed uppercase ticket_type: '$ticket_type_upper'");

                // Check for 'STARTER' (maps to starter_tickets)
                if ($ticket_type_upper == 'STARTER' ||
                    strpos($ticket_type_upper, 'STARTER') !== false) {
                    $stmt = $conn->prepare("UPDATE user SET starter_tickets = starter_tickets + ? WHERE username = ?");
                    error_log("Updating STARTER tickets for user $username, adding $numbers_per_package tickets");
                }
                // Check for 'BUSINESS' (maps to premium_tickets)
                else if ($ticket_type_upper == 'BUSINESS' ||
                         strpos($ticket_type_upper, 'BUSINESS') !== false ||
                         $ticket_type_upper == 'PREMIUM' ||
                         strpos($ticket_type_upper, 'PREMIUM') !== false) {
                    $stmt = $conn->prepare("UPDATE user SET premium_tickets = premium_tickets + ? WHERE username = ?");
                    error_log("Updating PREMIUM/BUSINESS tickets for user $username, adding $numbers_per_package tickets");
                }
                // Check for 'ULTIMATE'
                else if ($ticket_type_upper == 'ULTIMATE' ||
                         strpos($ticket_type_upper, 'ULTIMATE') !== false) {
                    $stmt = $conn->prepare("UPDATE user SET ultimate_tickets = ultimate_tickets + ? WHERE username = ?");
                    error_log("Updating ULTIMATE tickets for user $username, adding $numbers_per_package tickets");
                }
                // Default case
                else {
                    error_log("Unknown ticket type: '$ticket_type' - defaulting to premium tickets");
                    $stmt = $conn->prepare("UPDATE user SET premium_tickets = premium_tickets + ? WHERE username = ?");
                }

                if (!$stmt) {
                    error_log("Prepare failed for update: " . $conn->error);
                    throw new Exception("Database prepare error for update: " . $conn->error);
                }

                $stmt->bind_param("is", $numbers_per_package, $username);
                $result = $stmt->execute();

                if (!$result) {
                    error_log("Update failed: " . $stmt->error);
                    throw new Exception("Database update error: " . $stmt->error);
                }

                // Check if any rows were affected
                if ($stmt->affected_rows == 0) {
                    error_log("Warning: No rows affected when updating user tickets. Username: $username");
                } else {
                    error_log("User ticket count updated successfully. Affected rows: " . $stmt->affected_rows);
                }

                $messagelogpayment = "User ticket count updated successfully.";

                // Commit the transaction
                $conn->commit();
                error_log("Transaction committed successfully");
            } else {
                error_log("No tickets found for packagesize=$packagesize and tickettype=$tickettype");
                throw new Exception('No tickets found for the specified package size and ticket type.');
            }
        } catch (Exception $e) {
            // Rollback the transaction
            $conn->rollback();
            error_log("Transaction rolled back due to error: " . $e->getMessage());
            throw $e; // Re-throw the exception to be caught by the caller
        }
    }
}
?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Home</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap , fonts & icons  -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets  -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets  -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <!-- Custom stylesheet -->
    <link href="https://fonts.googleapis.com/css?family=Nunito+Sans:400,400i,700,900&display=swap" rel="stylesheet">

    <style>
    body {
        text-align: center;
        padding: 40px 0;
        background: #EBF0F5;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
    }

    h1 {
        color: #88B04B;
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-weight: 900;
        font-size: 40px;
        margin-bottom: 10px;
    }

    p {
        color: #404F5E;
        font-family: "Nunito Sans", "Helvetica Neue", sans-serif;
        font-size: 20px;
        margin: 0;
    }

    .checkmark {
        color: #9ABC66;
        font-size: 100px;
        line-height: 200px;
        margin-left: -15px;
    }

    .card {
        background: white;
        padding: 60px;
        border-radius: 4px;
        box-shadow: 0 2px 3px #C8D0D8;
        display: inline-block;
        margin: 0 auto;
    }
    </style>
</head>

<body data-theme="light">
    <?php include('../header-footer/header.php'); ?>
    <div>
        <!-- Header Area -->

        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];
            $sqlshow = "SELECT username, email, starter_tickets, premium_tickets, tell, company_name, tax_id, address, district, city, postal_code, country FROM user WHERE username = '$username'";
            $resultshow = mysqli_query($conn, $sqlshow);
            ?>
        <?php if ($resultshow && mysqli_num_rows($resultshow) > 0) : ?>
        <?php
                // Fetch the user's data
                $user = mysqli_fetch_assoc($resultshow);
                ?>
        <!-- Display user data here -->
        <!-- Page Banner Area -->
        <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
            <div class="card">
                <?php if ($messageSuccess) : ?>
                <div
                    style="border-radius:200px; height:200px; width:200px; background: #F8FAF5; margin:0 auto; text-align: center;">
                    <i class="checkmark">✓</i>
                </div>
                <h1 style="text-align: center;">Success</h1>
                <p style="text-align: center;"><?php echo htmlspecialchars($messagelogpayment); ?></p>
                <p style="text-align: center;"><?php echo htmlspecialchars($messageSuccess); ?></p>
                <?php else : ?>
                <p style="text-align: center;"><?php
                                                            //echo htmlspecialchars($messageFail);
                                                            echo 'Payment successful!';
                                                            ?></p>
                <?php endif; ?>
                <a href="../front-end/buy-now.php" class="btn btn-success">BACK TO PURCHASE PAGE</a>
            </div>
        </div>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "No user found."; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php else : ?>
        <div class="d-flex justify-content-center align-items-center full-height">
            <div class="text-center">
                <?php echo "You are not logged in."; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>

</html>