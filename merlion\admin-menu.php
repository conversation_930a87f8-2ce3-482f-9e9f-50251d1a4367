<?php
// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Get admin role from session
$admin_role = isset($_SESSION['admin_role']) ? $_SESSION['admin_role'] : '';

// Include database connection for notification counts
include_once('../functions/server.php');

// Get notification counts for admin
$admin_id = isset($_SESSION['admin_id']) ? $_SESSION['admin_id'] : 0;

// Count only unseen tickets for notification
$new_tickets_sql = "SELECT COUNT(*) as cnt FROM support_tickets WHERE is_seen_by_admin=0";
$new_tickets_result = mysqli_query($conn, $new_tickets_sql);
$new_tickets_count = mysqli_fetch_assoc($new_tickets_result)['cnt'];

// Count unread messages from users
$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

// Total notification count
$total_notifications = $new_tickets_count + $unread_messages_count;
?>

<div class="dashboard-menu">
    <h5 class="menu-title">Admin Menu <span class="mobile-menu-toggle"><i class="fa fa-chevron-down"></i></span></h5>
    <ul class="list-unstyled menu-items">
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'index' ? 'active' : '' ?>">
            <a href="index"><i class="fa fa-tachometer-alt"></i> Dashboard</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-tickets' ? 'active' : '' ?>">
            <a href="admin-tickets">
                <i class="fa fa-ticket-alt"></i> Local Tickets
                <?php /*
                // Remove PHP badge output, let JS handle it
                if ($new_tickets_count > 0): ?>
                <span id="badge-admin-tickets"
                    class="notification-badge"><?php echo $new_tickets_count >= 10 ? '9+' : $new_tickets_count; ?></span>
                <?php endif;
                */ ?>
            </a>
        </li>

        <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-ticket-logs' ? 'active' : '' ?>">
            <a href="admin-ticket-logs"><i class="fa fa-history"></i> Ticket Logs</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-chat' ? 'active' : '' ?>">
            <a href="admin-chat">
                <i class="fa fa-comments"></i> Chat Support
                <?php if ($unread_messages_count > 0): ?>
                <span id="badge-admin-chat"
                    class="notification-badge"><?php echo $unread_messages_count >= 10 ? '9+' : $unread_messages_count; ?></span>
                <?php endif; ?>
            </a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-purchases' ? 'active' : '' ?>">
            <a href="admin-purchases"><i class="fa fa-shopping-cart"></i> Purchases</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF'], '.php') == 'admin-users' ? 'active' : '' ?>">
            <a href="admin-users"><i class="fa fa-users"></i> Local Users</a>
        </li>

        <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-staff' ? 'active' : '' ?>">
            <a href="admin-staff"><i class="fa fa-user-shield"></i> Staff</a>
        </li>

        <?php if ($admin_role === 'admin' || $admin_role === 'manager'): ?>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'api-key-manager' ? 'active' : '' ?>">
            <a href="api-key-manager"><i class="fa fa-key"></i> API Key Manager</a>
        </li>
        <?php endif; ?>

        <!-- <li class="<?= basename($_SERVER['PHP_SELF']) == 'import-customers' ? 'active' : '' ?>">
            <a href="../api/import-customers"><i class="fa fa-cloud-download-alt"></i> Import Customers</a>
        </li> -->
        <!-- <li class="<?= basename($_SERVER['PHP_SELF']) == 'core_api_post_example' ? 'active' : '' ?>">
            <a href="../api/core_api_post_example"><i class="fa fa-exchange-alt"></i> API POST Examples</a>
        </li> -->
        <!-- <li class="<?= basename($_SERVER['PHP_SELF']) == 'admin-settings' ? 'active' : '' ?>">
            <a href="admin-settings"><i class="fa fa-cog"></i> Settings</a>
        </li> -->

        <?php if ($admin_role === 'admin' || $admin_role === 'manager'): ?>
        <span>Appika</span>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'appika-tickets' ? 'active' : '' ?>">
            <a href="appika-tickets"><i class="fa fa-ticket-alt"></i> Appika Tickets</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'appika-ticket-search' ? 'active' : '' ?>">
            <a href="appika-ticket-search"><i class="fa fa-search"></i> Ticket Search</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'appika-customers' ? 'active' : '' ?>">
            <a href="appika-customers"><i class="fa fa-users"></i> Appika Customers</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'appika-customer-search' ? 'active' : '' ?>">
            <a href="appika-customer-search"><i class="fa fa-search"></i> Customer Search</a>
        </li>
        <span>APIs for testing</span>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'graphql_ticket_test.php' ? 'active' : '' ?>">
            <a href="../api/graphql_ticket_test.php">GraphQL - Tickets Test</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'core_api_post_example.php' ? 'active' : '' ?>">
            <a href="../api/core_api_post_example.php">Core API - Customers Test</a>
        </li>
        <?php endif; ?>
        <li>
            <hr>
        </li>
        <li><a href="#" id="logout-link"><i class="fa fa-sign-out-alt"></i> Log Out</a></li>
    </ul>
</div>

<!-- Logout Confirmation Modal -->
<div class="modal fade" id="logoutConfirmModal" tabindex="-1" role="dialog" aria-labelledby="logoutConfirmModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary">
                <h5 class="modal-title text-white" id="logoutConfirmModalLabel">Confirm Logout</h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to log out?</p>
            </div>
            <div class="modal-footer justify-content-center">
                <a href="index?logout=1" class="btn btn-primary">Yes, Log Out</a>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>
        </div>
    </div>
</div>

<style>
/* Dashboard Menu */
.dashboard-menu {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
    border: 1px solid #e0e6ed !important;
    box-sizing: border-box !important;
    height: auto !important;
    max-width: 250px !important;
    width: 100% !important;
}

.dashboard-menu .menu-title {
    font-size: 18px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    margin-bottom: 10px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e0e6ed !important;
}

.mobile-menu-toggle {
    display: none !important;
    float: right !important;
    cursor: pointer !important;
    transition: transform 0.3s ease !important;
}

.mobile-menu-toggle.active {
    transform: rotate(90deg) !important;
}

.mobile-menu-toggle i {
    transition: transform 0.3s ease !important;
    display: inline-block !important;
}

.mobile-menu-toggle.active i {
    transform: rotate(180deg) !important;
}

.dashboard-menu ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.dashboard-menu ul li {
    margin-bottom: 8px !important;
}

.dashboard-menu ul li a {
    text-decoration: none !important;
    color: #555 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    transition: background-color 0.3s ease, color 0.3s ease !important;
    font-size: 14px !important;
}

.dashboard-menu ul li a i {
    margin-right: 8px !important;
    font-size: 14px !important;
    color: #777 !important;
}

.dashboard-menu ul li a:hover {
    background-color: #f0f2f5 !important;
    color: #007bff !important;
}

.dashboard-menu ul li.active a {
    background-color: #007bff !important;
    color: #fff !important;
    font-weight: bold !important;
}

.dashboard-menu ul li.active a i {
    color: #fff !important;
}

/* Notification Badge Styles */
.notification-badge {
    background-color: #dc3545 !important;
    color: white !important;
    border-radius: 50% !important;
    padding: 2px 6px !important;
    font-size: 11px !important;
    font-weight: bold !important;
    margin-left: 5px !important;
    min-width: 18px !important;
    height: 18px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    line-height: 1 !important;
    position: relative !important;
    top: -1px !important;
}

.dashboard-menu ul li.active a .notification-badge {
    background-color: #fff !important;
    color: #007bff !important;
}

/* Mobile styles */
@media (max-width: 767px) {
    .dashboard-menu {
        margin-right: 0 !important;
        margin-bottom: 15px !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .mobile-menu-toggle {
        display: block !important;
    }

    .dashboard-menu .menu-title {
        cursor: pointer !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .dashboard-menu ul.menu-items {
        max-height: 0 !important;
        overflow: hidden !important;
        transition: max-height 0.5s ease !important;
    }

    .dashboard-menu ul.menu-items.expanded {
        max-height: 500px !important;
    }
}

/* Logout Modal Styles */
#logoutConfirmModal .modal-header {
    background-color: #473BF0 !important;
    color: white !important;
}

#logoutConfirmModal .modal-title {
    font-size: 22px !important;
    color: white !important;
    font-weight: 500 !important;
}

#logoutConfirmModal .modal-body {
    padding: 20px !important;
    font-size: 16px !important;
}

#logoutConfirmModal .modal-footer .btn-primary {
    background-color: #473BF0 !important;
    border-color: #473BF0 !important;
}

#logoutConfirmModal .modal-footer .btn-secondary {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

#logoutConfirmModal .modal-footer {
    display: flex !important;
    justify-content: center !important;
    padding: 1rem !important;
}

#logoutConfirmModal .modal-footer .btn {
    min-width: 120px !important;
    margin: 0 10px !important;
}

@media (max-width: 767px) {
    #logoutConfirmModal .modal-body {
        font-size: 14px !important;
    }

    #logoutConfirmModal .modal-footer .btn {
        min-width: 100px !important;
        margin: 0 5px !important;
        font-size: 14px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeUserMenus();
});

// Run immediately as well
(function() {
    initializeUserMenus();
    // Run again after short delays to catch any late DOM changes
    setTimeout(initializeUserMenus, 100);
    setTimeout(initializeUserMenus, 500);
})();

// Global function that can be called from any page
function initializeUserMenus() {
    console.log('Initializing all user menus');

    // Find ALL dashboard menus on the page (there might be multiple)
    var allMenus = document.querySelectorAll('.dashboard-menu');

    if (allMenus.length === 0) {
        console.log('No dashboard menus found on page');
        return;
    }

    console.log('Found ' + allMenus.length + ' dashboard menus');

    // Process each menu separately
    allMenus.forEach(function(menu, index) {
        var menuTitle = menu.querySelector('.menu-title');
        var menuToggle = menu.querySelector('.mobile-menu-toggle');
        var menuItems = menu.querySelector('.menu-items');

        if (!menuTitle || !menuToggle || !menuItems) {
            console.log('Menu #' + index + ' is missing components');
            return;
        }

        // Create a unique toggle function for this menu
        function toggleThisMenu(e) {
            console.log('Toggle clicked for menu #' + index);
            e.preventDefault();
            e.stopPropagation();
            menuItems.classList.toggle('expanded');
            menuToggle.classList.toggle('active');
        }

        // Remove any existing click handlers to prevent duplicates
        menuTitle.onclick = null;

        // Add the new click handler
        menuTitle.onclick = toggleThisMenu;

        // Also add direct click handler to the toggle icon for better mobile experience
        menuToggle.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            menuItems.classList.toggle('expanded');
            menuToggle.classList.toggle('active');
        };

        // Auto-expand if there's an active item (only on mobile)
        var hasActiveItem = menuItems.querySelector('.active');
        if (hasActiveItem && window.innerWidth <= 767) {
            console.log('Auto-expanding menu #' + index + ' with active item');
            menuItems.classList.add('expanded');
            menuToggle.classList.add('active');
        }

        console.log('Menu #' + index + ' initialized');
    });

    // Initialize logout confirmation
    initializeLogoutConfirmation();

    // Initialize notification system
    initializeNotificationSystem();
}

// Function to initialize notification system
function initializeNotificationSystem() {
    // Update notification counts every 1 seconds
    setInterval(updateNotificationCounts, 1000);
}

// Function to update notification counts
function updateNotificationCounts() {
    fetch('admin-notifications')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update ticket notification badge
                updateNotificationBadge('admin-tickets', data.new_tickets_count);

                // Update chat notification badge
                updateNotificationBadge('admin-chat', data.unread_messages_count);
            }
        })
        .catch(error => console.error('Error updating notifications:', error));
}

// Function to update individual notification badge
function updateNotificationBadge(page, count) {
    // Find the menu item for this page
    const menuItems = document.querySelectorAll('.dashboard-menu .menu-items li');

    menuItems.forEach(item => {
        const link = item.querySelector('a');
        if (link && link.getAttribute('href') === page) {
            // Remove existing badge
            const existingBadge = link.querySelector('.notification-badge');
            if (existingBadge) {
                existingBadge.remove();
            }

            // Add new badge if count > 0
            if (count > 0) {
                const badge = document.createElement('span');
                badge.className = 'notification-badge';
                badge.textContent = count >= 10 ? '9+' : count;
                link.appendChild(badge);
            }
        }
    });
}

// Function to initialize logout confirmation
function initializeLogoutConfirmation() {
    var logoutLink = document.getElementById('logout-link');
    if (logoutLink) {
        logoutLink.addEventListener('click', function(e) {
            e.preventDefault();
            $('#logoutConfirmModal').modal('show');
        });
    }
}
</script>