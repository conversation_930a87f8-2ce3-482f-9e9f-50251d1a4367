<?php
session_start();
include('server.php');

header('Content-Type: application/json');

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// Get session ID
$session_id = $_POST['session_id'] ?? '';

if (empty($session_id)) {
    echo json_encode(['success' => false, 'message' => 'Session ID required']);
    exit;
}

try {
    // Clean up payment_temp data for this session
    $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
    $cleanup_stmt->bind_param("s", $session_id);
    
    if ($cleanup_stmt->execute()) {
        $deleted_rows = $cleanup_stmt->affected_rows;
        error_log("Cleanup: Deleted $deleted_rows payment_temp records for session: $session_id");
        
        // Also clean up cart_sessions data if it exists
        $cart_cleanup_stmt = $conn->prepare("DELETE FROM cart_sessions WHERE session_id = ?");
        $cart_cleanup_stmt->bind_param("s", $session_id);
        $cart_cleanup_stmt->execute();
        $cart_deleted_rows = $cart_cleanup_stmt->affected_rows;
        
        if ($cart_deleted_rows > 0) {
            error_log("Cleanup: Deleted $cart_deleted_rows cart_sessions records for session: $session_id");
        }
        
        echo json_encode([
            'success' => true, 
            'message' => 'Payment data cleaned up successfully',
            'payment_temp_deleted' => $deleted_rows,
            'cart_sessions_deleted' => $cart_deleted_rows
        ]);
    } else {
        error_log("Cleanup: Failed to delete payment_temp data for session: $session_id, error: " . $cleanup_stmt->error);
        echo json_encode(['success' => false, 'message' => 'Failed to cleanup payment data']);
    }
    
} catch (Exception $e) {
    error_log("Cleanup: Error cleaning up payment data: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred during cleanup']);
}
?>
