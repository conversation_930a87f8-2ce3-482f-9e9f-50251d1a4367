<?php
session_start();
include('server.php');

if (isset($_POST['test_signin'])) {
    $login_input = $_POST['username'];
    $password = $_POST['password'];
    
    echo "<h2>Step-by-Step Sign-In Test</h2>";
    echo "<p><strong>Testing:</strong> Username/Email: $login_input, Password: $password</p>";
    
    $is_email = strpos($login_input, '@') !== false;
    $field = $is_email ? 'email' : 'username';
    
    echo "<p><strong>Step 1:</strong> Detected as " . ($is_email ? 'email' : 'username') . "</p>";
    
    // Check user table
    $user_stmt = $conn->prepare("SELECT * FROM user WHERE $field = ? LIMIT 1");
    $user_stmt->bind_param("s", $login_input);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    
    if ($user_result->num_rows == 1) {
        $user_data = $user_result->fetch_assoc();
        echo "<p><strong>Step 2:</strong> ✅ User found in database</p>";
        echo "<ul>";
        echo "<li>ID: " . $user_data['id'] . "</li>";
        echo "<li>Username: " . $user_data['username'] . "</li>";
        echo "<li>Email: " . $user_data['email'] . "</li>";
        echo "<li>Password Hash: " . substr($user_data['password'], 0, 30) . "...</li>";
        echo "<li>Hash Type: " . (strpos($user_data['password'], '$2y$') === 0 ? 'bcrypt' : 'md5') . "</li>";
        echo "</ul>";
        
        $stored_hash = $user_data['password'];
        
        // Test direct password verification
        echo "<p><strong>Step 3:</strong> Testing direct password verification</p>";
        $direct_success = false;
        if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
            $direct_success = password_verify($password, $stored_hash);
            echo "<p>bcrypt verification: " . ($direct_success ? '✅ PASS' : '❌ FAIL') . "</p>";
        } else {
            $direct_success = ($stored_hash === md5($password));
            echo "<p>MD5 verification: " . ($direct_success ? '✅ PASS' : '❌ FAIL') . "</p>";
        }
        
        if (!$direct_success) {
            echo "<p><strong>Step 4:</strong> Direct verification failed, checking payment_temp</p>";
            
            $temp_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE (username = ? OR email = ?) ORDER BY id DESC LIMIT 1");
            $temp_stmt->bind_param("ss", $login_input, $login_input);
            $temp_stmt->execute();
            $temp_result = $temp_stmt->get_result();
            
            if ($temp_result->num_rows > 0) {
                $temp_data = $temp_result->fetch_assoc();
                $temp_password = $temp_data['password'];
                echo "<p>Found in payment_temp: $temp_password</p>";
                echo "<p>Entered password matches temp: " . ($password === $temp_password ? '✅ YES' : '❌ NO') . "</p>";
                
                if ($password === $temp_password) {
                    echo "<p>Testing if temp password verifies against stored hash:</p>";
                    if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
                        $temp_verify = password_verify($temp_password, $stored_hash);
                        echo "<p>bcrypt verify temp password: " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
                    } else {
                        $temp_verify = ($stored_hash === md5($temp_password));
                        echo "<p>MD5 verify temp password: " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
                    }
                }
            } else {
                echo "<p>❌ NOT found in payment_temp</p>";
            }
            $temp_stmt->close();
        } else {
            echo "<p><strong>Step 4:</strong> ✅ Direct verification successful!</p>";
        }
    } else {
        echo "<p><strong>Step 2:</strong> ❌ User NOT found in main table</p>";
        echo "<p><strong>Step 3:</strong> Checking if user exists only in payment_temp</p>";
        
        $temp_only_stmt = $conn->prepare("SELECT * FROM payment_temp WHERE (username = ? OR email = ?) AND password = ? ORDER BY id DESC LIMIT 1");
        $temp_only_stmt->bind_param("sss", $login_input, $login_input, $password);
        $temp_only_stmt->execute();
        $temp_only_result = $temp_only_stmt->get_result();
        
        if ($temp_only_result->num_rows > 0) {
            $temp_only_data = $temp_only_result->fetch_assoc();
            echo "<p>✅ Found in payment_temp only (webhook pending):</p>";
            echo "<ul>";
            echo "<li>Session ID: " . $temp_only_data['session_id'] . "</li>";
            echo "<li>Username: " . $temp_only_data['username'] . "</li>";
            echo "<li>Email: " . $temp_only_data['email'] . "</li>";
            echo "<li>Password: " . $temp_only_data['password'] . "</li>";
            echo "</ul>";
            echo "<p><strong>Result:</strong> Should show 'Account still being set up' message</p>";
        } else {
            echo "<p>❌ Not found in payment_temp either</p>";
            echo "<p><strong>Result:</strong> Should show 'Wrong username/password' message</p>";
        }
        $temp_only_stmt->close();
    }
    $user_stmt->close();
    
    echo "<hr>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Step-by-Step Sign-In Test</title>
    <style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .container { max-width: 800px; margin: 0 auto; }
    .form-group { margin: 10px 0; }
    input[type="text"], input[type="password"] { padding: 8px; width: 200px; }
    input[type="submit"] { padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Step-by-Step Sign-In Test</h1>
        
        <form method="POST">
            <div class="form-group">
                <label>Username or Email:</label><br>
                <input type="text" name="username" required>
            </div>
            <div class="form-group">
                <label>Password:</label><br>
                <input type="text" name="password" required>
            </div>
            <div class="form-group">
                <input type="submit" name="test_signin" value="Test Sign-In">
            </div>
        </form>
        
        <hr>
        
        <h3>Quick Database Check</h3>
        <p><a href="check-database-data.php">View Recent Database Data</a></p>
        
        <h3>Recent Users from Webhook Log</h3>
        <p>Based on the latest webhook logs, try testing with:</p>
        <ul>
            <li><strong>Username:</strong> user71085</li>
            <li><strong>Email:</strong> Check payment_temp table for the email</li>
            <li><strong>Password:</strong> Check payment_temp table for the password</li>
        </ul>
        
        <h3>How to Test</h3>
        <ol>
            <li>First, check the database data to see what users exist</li>
            <li>Find a recent user from the webhook logs</li>
            <li>Get their username/email and password from payment_temp</li>
            <li>Test the sign-in with those exact credentials</li>
            <li>This tool will show you exactly what happens at each step</li>
        </ol>
    </div>
</body>
</html>
