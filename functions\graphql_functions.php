<?php
// GraphQL Functions for Appika API Integration
require_once __DIR__ . '/../vendor/autoload.php'; // Include Composer autoloader for Guzzle

// Load centralized API configuration
require_once __DIR__ . '/../config/api-config.php';

// Get GraphQL API configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$apiKey = $graphqlConfig['key'];

/**
 * Make GraphQL requests to Appika API
 *
 * @param string $query The GraphQL query or mutation
 * @param array $variables Variables for the GraphQL query
 * @return array Response array with success status, data, and error information
 */
function makeGraphQLRequest($query, $variables = []) {
    global $graphqlEndpoint, $apiKey;

    try {
        // Create a Guzzle HTTP client
        $client = new \GuzzleHttp\Client([
            'timeout' => 30,
            'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
        ]);

        // Prepare GraphQL request body
        $requestBody = [
            'query' => $query
        ];

        if (!empty($variables)) {
            $requestBody['variables'] = $variables;
        }

        // Send the request
        $response = $client->request('POST', $graphqlEndpoint, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => $requestBody
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        // Check for HTTP errors
        if ($statusCode >= 400) {
            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $data,
                'error' => "HTTP Error {$statusCode}: " . ($data['message'] ?? 'Unknown error'),
                'raw_response' => $body
            ];
        }

        // Check for GraphQL errors
        if (isset($data['errors']) && !empty($data['errors'])) {
            $errorMessages = [];
            foreach ($data['errors'] as $error) {
                $errorMessages[] = $error['message'] ?? 'Unknown GraphQL error';
            }

            return [
                'success' => false,
                'status' => $statusCode,
                'data' => $data,
                'error' => 'GraphQL Errors: ' . implode(', ', $errorMessages),
                'raw_response' => $body
            ];
        }

        // Success response
        return [
            'success' => true,
            'status' => $statusCode,
            'data' => $data,
            'error' => null,
            'raw_response' => $body
        ];

    } catch (\GuzzleHttp\Exception\ConnectException $e) {
        return [
            'success' => false,
            'status' => 0,
            'data' => null,
            'error' => 'Connection Error: ' . $e->getMessage(),
            'raw_response' => null
        ];
    } catch (\GuzzleHttp\Exception\RequestException $e) {
        return [
            'success' => false,
            'status' => $e->getCode(),
            'data' => null,
            'error' => 'Request Error: ' . $e->getMessage(),
            'raw_response' => null
        ];
    } catch (\Exception $e) {
        return [
            'success' => false,
            'status' => 0,
            'data' => null,
            'error' => 'Unexpected Error: ' . $e->getMessage(),
            'raw_response' => null
        ];
    }
}

/**
 * Get all tickets from Appika API
 *
 * @return array Response array with tickets data
 */
function getAppikaTickets() {
    $query = '
    query GetTickets {
        getTickets {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    return makeGraphQLRequest($query, []);
}

/**
 * Get a specific ticket by ID from Appika API
 *
 * @param int $ticketId The ticket ID
 * @return array Response array with ticket data
 */
function getAppikaTicket($ticketId) {
    $query = '
    query GetTicket($id: Int!) {
        getTicket(id: $id) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    $variables = [
        'id' => (int)$ticketId
    ];

    return makeGraphQLRequest($query, $variables);
}

/**
 * Create a new ticket in Appika API
 *
 * @param array $ticketData Ticket data array
 * @return array Response array with created ticket data
 */
function createAppikaTicket($ticketData) {
    $mutation = '
    mutation CreateTicketByAgent(
        $subject: String!,
        $reply_msg: String!,
        $req_email: String,
        $type: Int!,
        $type_name: String,
        $priority: String!,
        $status: String!,
        $reply_type: String!,
        $tags: String
    ) {
        createTicketByAgent(
            subject: $subject,
            reply_msg: $reply_msg,
            req_email: $req_email,
            type: $type,
            type_name: $type_name,
            priority: $priority,
            status: $status,
            reply_type: $reply_type,
            tags: $tags
        ) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    return makeGraphQLRequest($mutation, $ticketData);
}

/**
 * Update an existing ticket in Appika API
 *
 * @param int $ticketId The ticket ID
 * @param array $updateData Update data array
 * @return array Response array with updated ticket data
 */
function updateAppikaTicket($ticketId, $updateData) {
    $mutation = '
    mutation UpdateTicket(
        $id: Int!,
        $subject: String,
        $priority: String,
        $status: String,
        $reply_msg: String,
        $reply_type: String
    ) {
        updateTicket(
            id: $id,
            subject: $subject,
            priority: $priority,
            status: $status,
            reply_msg: $reply_msg,
            reply_type: $reply_type
        ) {
            id
            ticket_no
            contact_id
            agent_id
            req_email
            subject
            type
            type_name
            priority
            status
            created
            updated
        }
    }';

    $variables = array_merge(['id' => (int)$ticketId], $updateData);

    return makeGraphQLRequest($mutation, $variables);
}

/**
 * Log GraphQL API requests for debugging
 *
 * @param string $operation The operation name
 * @param array $result The API result
 * @param string $logFile The log file path (optional)
 */
function logGraphQLRequest($operation, $result, $logFile = null) {
    if (!$logFile) {
        $logFile = __DIR__ . '/../logs/graphql_api.log';
    }

    // Create logs directory if it doesn't exist
    $logDir = dirname($logFile);
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
    }

    $logEntry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'operation' => $operation,
        'success' => $result['success'],
        'status' => $result['status'],
        'error' => $result['error']
    ];

    $logLine = json_encode($logEntry) . "\n";
    file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
}
?>