<?php
session_start();
include('server.php');

// Set content type to JSON
header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit();
}

$username_or_email = $_POST['username'] ?? '';

if (empty($username_or_email)) {
    echo json_encode(['success' => false, 'message' => 'Username or email required']);
    exit();
}

try {
    // Check if input is email or username
    $is_email = strpos($username_or_email, '@') !== false;
    $field = $is_email ? 'email' : 'username';
    
    // Get user from database
    $stmt = $conn->prepare("SELECT id, username, email, password FROM user WHERE $field = ? LIMIT 1");
    $stmt->bind_param("s", $username_or_email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit();
    }
    
    $user = $result->fetch_assoc();
    $stored_hash = $user['password'];
    
    // Check if this looks like a guest purchase user (bcrypt hash)
    $is_bcrypt = (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0);
    
    if ($is_bcrypt) {
        // This is likely a guest purchase user - check payment_temp for the original password
        $temp_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE username = ? OR email = ? ORDER BY id DESC LIMIT 1");
        $temp_stmt->bind_param("ss", $user['username'], $user['email']);
        $temp_stmt->execute();
        $temp_result = $temp_stmt->get_result();
        
        if ($temp_result->num_rows > 0) {
            $temp_data = $temp_result->fetch_assoc();
            $original_password = $temp_data['password'];
            
            // Verify this password matches the stored hash
            if (password_verify($original_password, $stored_hash)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Guest purchase user found',
                    'user_info' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ],
                    'password_info' => [
                        'original_password' => $original_password,
                        'hash_type' => 'bcrypt',
                        'hash_preview' => substr($stored_hash, 0, 20) . '...'
                    ]
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Password verification failed',
                    'debug' => [
                        'temp_password' => $original_password,
                        'hash_matches' => false
                    ]
                ]);
            }
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Guest purchase user but no temporary password found',
                'user_info' => [
                    'id' => $user['id'],
                    'username' => $user['username'],
                    'email' => $user['email']
                ],
                'suggestion' => 'Use password reset to set a new password'
            ]);
        }
    } else {
        // This is a regular user with MD5 password
        echo json_encode([
            'success' => true,
            'message' => 'Regular user with MD5 password',
            'user_info' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'email' => $user['email']
            ],
            'password_info' => [
                'hash_type' => 'md5',
                'hash_preview' => $stored_hash,
                'note' => 'Enter your original password to sign in'
            ]
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Guest Password Checker - HelloIT</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
        padding: 20px;
    }

    .checker-container {
        max-width: 600px;
        margin: 0 auto;
    }

    .checker-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .password-display {
        background: #e8f5e8;
        border: 2px solid #28a745;
        border-radius: 8px;
        padding: 15px;
        font-family: monospace;
        font-size: 18px;
        font-weight: bold;
        text-align: center;
        margin: 15px 0;
    }

    .hash-display {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        font-family: monospace;
        font-size: 12px;
        word-break: break-all;
    }
    </style>
</head>

<body>
    <div class="checker-container">
        <h1 class="text-center mb-4">Guest Password Checker</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Purpose</h5>
            <p class="mb-0">This tool helps find the correct password for guest purchase users. Guest users have auto-generated passwords that are stored in the payment_temp table.</p>
        </div>

        <div class="checker-section">
            <h3>Check Guest Password</h3>
            <form id="checkerForm">
                <div class="mb-3">
                    <label for="username" class="form-label">Username or Email</label>
                    <input type="text" class="form-control" id="username" placeholder="Enter username or email" required>
                    <div class="form-text">Enter the username or email of the guest purchase user</div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Find Password
                </button>
            </form>
        </div>

        <div id="results" class="checker-section" style="display: none;">
            <h3>Results</h3>
            <div id="resultsContent"></div>
        </div>

        <div class="checker-section">
            <h3>How Guest Passwords Work</h3>
            <div class="row">
                <div class="col-md-6">
                    <h6>Password Generation</h6>
                    <ul>
                        <li>Generated with <code>bin2hex(random_bytes(4))</code></li>
                        <li>Creates 8-character hex string</li>
                        <li>Example: <code>1c0a283b</code></li>
                        <li>Stored in <code>payment_temp</code> table</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Password Storage</h6>
                    <ul>
                        <li>Raw password hashed with bcrypt</li>
                        <li>Stored in <code>user.password</code> field</li>
                        <li>Example: <code>$2y$10$abc123...</code></li>
                        <li>Sign-in requires original password</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="alert alert-warning">
            <h5><i class="fas fa-exclamation-triangle me-2"></i>Important Note</h5>
            <p class="mb-0">If you're trying to sign in with a hash like <code>1c0a283b</code>, this is likely the correct password. The system stores a bcrypt hash of this password, not the password itself.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.getElementById('checkerForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        
        if (!username) {
            alert('Please enter a username or email');
            return;
        }
        
        // Send AJAX request
        fetch('check-guest-password.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `username=${encodeURIComponent(username)}`
        })
        .then(response => response.json())
        .then(data => {
            displayResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred during the check');
        });
    });

    function displayResults(data) {
        const resultsDiv = document.getElementById('results');
        const contentDiv = document.getElementById('resultsContent');
        
        if (data.success) {
            let html = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>${data.message}</h6>
                </div>
                
                <h5>User Information</h5>
                <p><strong>ID:</strong> ${data.user_info.id}</p>
                <p><strong>Username:</strong> ${data.user_info.username}</p>
                <p><strong>Email:</strong> ${data.user_info.email}</p>
            `;
            
            if (data.password_info && data.password_info.original_password) {
                html += `
                    <h5>Password Information</h5>
                    <p><strong>Use this password to sign in:</strong></p>
                    <div class="password-display">
                        ${data.password_info.original_password}
                    </div>
                    <p><strong>Hash Type:</strong> ${data.password_info.hash_type}</p>
                    <p><strong>Stored Hash:</strong></p>
                    <div class="hash-display">${data.password_info.hash_preview}</div>
                `;
            } else if (data.password_info) {
                html += `
                    <h5>Password Information</h5>
                    <p><strong>Hash Type:</strong> ${data.password_info.hash_type}</p>
                    <p><strong>Note:</strong> ${data.password_info.note}</p>
                    <div class="hash-display">${data.password_info.hash_preview}</div>
                `;
            }
            
            if (data.suggestion) {
                html += `
                    <div class="alert alert-warning">
                        <strong>Suggestion:</strong> ${data.suggestion}
                    </div>
                `;
            }
            
        } else {
            html = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-times-circle me-2"></i>Error</h6>
                    <p class="mb-0">${data.message}</p>
                </div>
            `;
            
            if (data.debug) {
                html += `
                    <h6>Debug Information</h6>
                    <pre>${JSON.stringify(data.debug, null, 2)}</pre>
                `;
            }
        }
        
        contentDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    }
    </script>
</body>
</html>
