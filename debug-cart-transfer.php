<?php
session_start();
include('functions/server.php');
include('functions/cart-transfer.php');

echo "<h2>🔍 Cart Transfer Debug</h2>";

// Check current session state
echo "<h3>Current Session State</h3>";
echo "<p><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Not logged in') . "</p>";
echo "<p><strong>Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</p>";

// Check guest cart
echo "<h3>Guest Cart Status</h3>";
if (hasGuestCartItems()) {
    echo "<p style='color: green;'>✅ Guest cart has " . getGuestCartItemsCount() . " items</p>";
    echo "<p><strong>Total Value:</strong> $" . number_format(getGuestCartTotal(), 2) . "</p>";
    
    echo "<h4>Guest Cart Contents:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Ticket ID</th><th>Type</th><th>Quantity</th><th>Price</th><th>Cart Item ID</th></tr>";
    foreach ($_SESSION['guest_cart'] as $item) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($item['ticket_id']) . "</td>";
        echo "<td>" . htmlspecialchars($item['ticket_type']) . "</td>";
        echo "<td>" . htmlspecialchars($item['quantity']) . "</td>";
        echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
        echo "<td>" . htmlspecialchars($item['cart_item_id']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>❌ No guest cart items found</p>";
}

// Check user cart (if logged in)
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    echo "<h3>User Cart Status (Database)</h3>";
    
    $query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";
    
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $result = $stmt->get_result();
    $user_cart_items = $result->fetch_all(MYSQLI_ASSOC);
    
    if (!empty($user_cart_items)) {
        echo "<p style='color: green;'>✅ User has " . count($user_cart_items) . " items in database cart</p>";
        
        echo "<h4>User Cart Contents:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Cart Item ID</th><th>Ticket ID</th><th>Type</th><th>Quantity</th><th>Price</th></tr>";
        foreach ($user_cart_items as $item) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($item['cart_item_id']) . "</td>";
            echo "<td>" . htmlspecialchars($item['ticket_id']) . "</td>";
            echo "<td>" . htmlspecialchars($item['ticket_type']) . "</td>";
            echo "<td>" . htmlspecialchars($item['quantity']) . "</td>";
            echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: orange;'>⚠️ User has no items in database cart</p>";
    }
}

// Test transfer function (if logged in and has guest cart)
if (isset($_SESSION['user_id']) && hasGuestCartItems()) {
    echo "<h3>Test Cart Transfer</h3>";
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Ready to test transfer!</strong></p>";
    echo "<p>You are logged in and have guest cart items.</p>";
    echo "<a href='?test_transfer=1' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔄 Test Transfer Now</a>";
    echo "</div>";
}

// Handle test transfer
if (isset($_GET['test_transfer']) && $_GET['test_transfer'] == '1' && isset($_SESSION['user_id'])) {
    echo "<h3>Transfer Test Results</h3>";
    $result = transferGuestCartToUser($_SESSION['user_id'], $conn);
    
    if ($result['success']) {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
        echo "<h4>✅ Transfer Successful!</h4>";
        echo "<p>" . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
        echo "<h4>❌ Transfer Failed!</h4>";
        echo "<p>" . htmlspecialchars($result['message']) . "</p>";
        echo "</div>";
    }
    
    echo "<p><a href='debug-cart-transfer.php'>🔄 Refresh to see updated state</a></p>";
}

// Check for session messages
echo "<h3>Session Messages</h3>";
if (isset($_SESSION['cart_transfer_success'])) {
    echo "<p style='color: green;'>✅ Transfer Success Message: " . htmlspecialchars($_SESSION['cart_transfer_success']) . "</p>";
}
if (isset($_SESSION['cart_transfer_error'])) {
    echo "<p style='color: red;'>❌ Transfer Error Message: " . htmlspecialchars($_SESSION['cart_transfer_error']) . "</p>";
}
if (isset($_SESSION['preserve_cart_transfer'])) {
    echo "<p style='color: blue;'>🔄 Preserved Cart Transfer: " . json_encode($_SESSION['preserve_cart_transfer']) . "</p>";
}

// Navigation
echo "<h3>Navigation</h3>";
echo "<div style='margin: 10px 0;'>";
echo "<a href='test-cart-transfer.php' style='background: #6754e2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🧪 Test Page</a>";
echo "<a href='front-end/cart.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🛒 Cart</a>";
echo "<a href='front-end/sign-in.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔑 Sign In</a>";
echo "<a href='index.php?logout=1' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🚪 Logout</a>";
echo "</div>";
?>
