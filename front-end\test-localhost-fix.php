<?php
/**
 * Test file to verify localhost webhook simulation works
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

echo "<h2>Localhost Webhook Fix Test</h2>";

// Check if we're on localhost
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);

echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";

if ($is_localhost) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>✓ Localhost detected!</strong><br>";
    echo "When you make a purchase on localhost, the system will:<br>";
    echo "1. Detect that webhooks cannot reach localhost<br>";
    echo "2. Automatically simulate webhook processing<br>";
    echo "3. Create user account and process tickets immediately<br>";
    echo "4. Show credentials on payment success page<br>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>ℹ Production environment detected!</strong><br>";
    echo "When you make a purchase on production, the system will:<br>";
    echo "1. Wait for Stripe webhook to process payment<br>";
    echo "2. Check multiple times if webhook completed<br>";
    echo "3. If webhook fails, fall back to manual processing<br>";
    echo "4. Show appropriate message based on processing status<br>";
    echo "</div>";
}

// Test if simulate-webhook.php file exists
$simulate_file = '../front-end/simulate-webhook.php';
if (file_exists($simulate_file)) {
    echo "<p><strong>✓ Simulate webhook file exists:</strong> $simulate_file</p>";
} else {
    echo "<p><strong>✗ Simulate webhook file missing:</strong> $simulate_file</p>";
}

// Test if payment-success.php has been updated
$success_file = '../front-end/payment-success.php';
if (file_exists($success_file)) {
    $content = file_get_contents($success_file);
    if (strpos($content, 'simulateWebhookProcessing') !== false) {
        echo "<p><strong>✓ Payment success file updated with localhost detection</strong></p>";
    } else {
        echo "<p><strong>✗ Payment success file not updated</strong></p>";
    }
} else {
    echo "<p><strong>✗ Payment success file missing</strong></p>";
}

echo "<hr>";
echo "<h3>Next Steps:</h3>";
echo "<ol>";
echo "<li><strong>Test on localhost:</strong> Add items to cart and complete a purchase. You should see credentials immediately.</li>";
echo "<li><strong>Test on server:</strong> The system should now wait longer for webhooks and fall back if needed.</li>";
echo "<li><strong>Check logs:</strong> Look for 'Localhost detected' or 'Production detected' messages in error logs.</li>";
echo "</ol>";

echo "<hr>";
echo "<h3>Key Improvements:</h3>";
echo "<ul>";
echo "<li><strong>Localhost:</strong> Immediate webhook simulation (no waiting)</li>";
echo "<li><strong>Production:</strong> Better webhook waiting with fallback</li>";
echo "<li><strong>Both:</strong> Consistent user account creation and ticket processing</li>";
echo "</ul>";
?>
