<?php
/**
 * Test Simple Localhost Fix (No Double Processing)
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Simple Localhost Fix Test</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Simple Solution Applied:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ Previous Complex Approach (Caused Double Processing):</h4>";
echo "<ul>";
echo "<li><strong>simulate-webhook.php:</strong> Created separate webhook simulation</li>";
echo "<li><strong>Real webhook:</strong> Also tried to process on localhost</li>";
echo "<li><strong>Result:</strong> Both ran, causing double tickets</li>";
echo "</ul>";

echo "<h4>✅ New Simple Approach:</h4>";
echo "<ul>";
echo "<li><strong>Real webhook:</strong> Completely disabled on localhost</li>";
echo "<li><strong>payment-success.php:</strong> Handles everything directly (like old version)</li>";
echo "<li><strong>No simulate-webhook.php:</strong> Removed complex simulation</li>";
echo "<li><strong>Result:</strong> Single processing, no duplicates</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>How It Works Now:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Localhost Flow (Simplified):</h4>";
echo "<ol>";
echo "<li><strong>Purchase completed</strong> → Stripe redirects to payment-success.php</li>";
echo "<li><strong>Real webhook disabled</strong> → stripe-webhook.php exits immediately on localhost</li>";
echo "<li><strong>payment-success.php</strong> → Detects no webhook data</li>";
echo "<li><strong>Localhost fallback</strong> → Creates user + processes purchase directly</li>";
echo "<li><strong>Uses old buyprocess()</strong> → Same logic as before, proven to work</li>";
echo "<li><strong>Result:</strong> Single processing, correct tickets ✅</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Code Changes:</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📝 stripe-webhook.php:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "if (\$environment === 'localhost') {\n";
echo "    // Exit immediately - don't process on localhost\n";
echo "    exit();\n";
echo "}";
echo "</pre>";

echo "<h4>📝 payment-success.php:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// LOCALHOST FALLBACK: Process manually if no webhook data\n";
echo "if (!\$guest_credentials && \$is_localhost) {\n";
echo "    // Create user if needed\n";
echo "    // Process purchase using old buyprocess() function\n";
echo "    include('../functions/buyprocess.php');\n";
echo "    buyprocess(\$packagesize, \$ticket_type, \$session_id);\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Benefits of This Approach:</h3>";

echo "<ul>";
echo "<li>✅ <strong>Simple:</strong> No complex webhook simulation</li>";
echo "<li>✅ <strong>Reliable:</strong> Uses proven buyprocess() function</li>";
echo "<li>✅ <strong>No duplicates:</strong> Only one processing path</li>";
echo "<li>✅ <strong>Backward compatible:</strong> Works like old system</li>";
echo "<li>✅ <strong>Easy to debug:</strong> Clear separation localhost vs production</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

if ($is_localhost) {
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Add items to cart:</strong> e.g., XS (5 tickets) + S (10 tickets)</li>";
    echo "<li><strong>Complete purchase</strong> → Should process normally</li>";
    echo "<li><strong>Check user tickets:</strong> Should show 15 tickets (not 30)</li>";
    echo "<li><strong>Check purchase history:</strong> Should show 2 entries (not 4)</li>";
    echo "<li><strong>Check credentials:</strong> Should display and work for login</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>Production uses normal webhook processing - no changes needed.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Files Modified:</h3>";

$modified_files = [
    'stripe-webhook.php' => 'Added localhost exit to disable webhook processing',
    'payment-success.php' => 'Added simple localhost fallback using buyprocess()',
    'simulate-webhook.php' => 'No longer used (calls removed)'
];

foreach ($modified_files as $file => $change) {
    echo "<p><strong>$file:</strong> $change</p>";
}

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>Correct ticket counts:</strong> User gets exactly what they purchased</li>";
echo "<li>✅ <strong>Single purchase history:</strong> No duplicate entries</li>";
echo "<li>✅ <strong>Working credentials:</strong> Login works with displayed password</li>";
echo "<li>✅ <strong>Simple & reliable:</strong> No complex webhook simulation</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Why This Works Better:</h3>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>The old system worked perfectly</strong> because it was simple:</p>";
echo "<ul>";
echo "<li>payment-success.php handled everything directly</li>";
echo "<li>Used buyprocess() function that was proven to work</li>";
echo "<li>No webhook complications on localhost</li>";
echo "</ul>";
echo "<p><strong>We're going back to that simplicity</strong> but with guest user support added.</p>";
echo "</div>";
?>
