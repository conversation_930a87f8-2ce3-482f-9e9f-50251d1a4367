<?php
session_start();
include('../functions/server.php');

// Find the most recent session_id for the current email
$email = '<EMAIL>'; // You can make this dynamic based on user

// Get the most recent session_id from payment_temp
$recent_session_query = "SELECT session_id FROM payment_temp WHERE email = ? ORDER BY id DESC LIMIT 1";
$recent_session_stmt = $conn->prepare($recent_session_query);
$recent_session_stmt->bind_param("s", $email);
$recent_session_stmt->execute();
$recent_session_result = $recent_session_stmt->get_result();

if ($recent_session_result->num_rows > 0) {
    $recent_session_data = $recent_session_result->fetch_assoc();
    $latest_session_id = $recent_session_data['session_id'];
    
    // Redirect to payment success page with correct session_id
    header("Location: payment-success.php?session_id=" . urlencode($latest_session_id));
    exit();
} else {
    // No recent purchases found
    echo "<!DOCTYPE html>";
    echo "<html><head><title>No Recent Purchases</title></head><body>";
    echo "<h2>No Recent Purchases Found</h2>";
    echo "<p>No recent purchases found for email: $email</p>";
    echo "<p><a href='cart.php'>Go to Cart</a></p>";
    echo "</body></html>";
}
?>
