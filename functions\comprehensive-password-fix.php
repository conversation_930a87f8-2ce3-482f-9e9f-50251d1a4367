<?php
include('server.php');

echo "<h2>🔧 Comprehensive Password Fix</h2>";

// Fix all users with password mismatches
$mismatch_query = "
    SELECT u.id, u.username, u.email, u.password as user_hash, pt.password as temp_password
    FROM user u
    JOIN payment_temp pt ON u.username = pt.username
    WHERE u.username LIKE 'user%'
    ORDER BY u.id DESC
";

$mismatch_result = mysqli_query($conn, $mismatch_query);

if ($mismatch_result && mysqli_num_rows($mismatch_result) > 0) {
    echo "<h3>Found " . mysqli_num_rows($mismatch_result) . " users to check:</h3>";
    
    $fixed_count = 0;
    $already_working_count = 0;
    
    while ($user = mysqli_fetch_assoc($mismatch_result)) {
        $username = $user['username'];
        $user_hash = $user['user_hash'];
        $temp_password = $user['temp_password'];
        
        echo "<div style='border: 1px solid #ccc; padding: 15px; margin: 10px 0; border-radius: 5px;'>";
        echo "<h4>Checking: $username</h4>";
        echo "<p><strong>Email:</strong> " . $user['email'] . "</p>";
        echo "<p><strong>Temp Password:</strong> $temp_password</p>";
        echo "<p><strong>Hash Type:</strong> " . (strpos($user_hash, '$2y$') === 0 ? 'bcrypt' : 'md5') . "</p>";
        
        // Test if current password works
        if (strpos($user_hash, '$2y$') === 0) {
            $verify_result = password_verify($temp_password, $user_hash);
        } else {
            $verify_result = ($user_hash === md5($temp_password));
        }
        
        if ($verify_result) {
            echo "<p style='color: green;'>✅ <strong>Already working!</strong> No fix needed.</p>";
            $already_working_count++;
        } else {
            echo "<p style='color: red;'>❌ <strong>Password mismatch detected!</strong></p>";
            
            // Apply fix: Update user hash to match temp password
            $new_hash = password_hash($temp_password, PASSWORD_DEFAULT);
            
            $update_stmt = $conn->prepare("UPDATE user SET password = ? WHERE username = ?");
            $update_stmt->bind_param("ss", $new_hash, $username);
            
            if ($update_stmt->execute()) {
                // Verify the fix worked
                $verify_fix = password_verify($temp_password, $new_hash);
                if ($verify_fix) {
                    echo "<p style='color: green;'>✅ <strong>FIXED!</strong> Password now works.</p>";
                    $fixed_count++;
                } else {
                    echo "<p style='color: red;'>❌ <strong>Fix failed!</strong> Verification still fails.</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ <strong>Database update failed:</strong> " . $conn->error . "</p>";
            }
        }
        
        echo "</div>";
    }
    
    echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 20px 0;'>";
    echo "<h3>📊 Summary:</h3>";
    echo "<ul>";
    echo "<li><strong>Already working:</strong> $already_working_count users</li>";
    echo "<li><strong>Fixed:</strong> $fixed_count users</li>";
    echo "<li><strong>Total processed:</strong> " . ($already_working_count + $fixed_count) . " users</li>";
    echo "</ul>";
    echo "</div>";
    
} else {
    echo "<p>✅ No users found with potential password mismatches.</p>";
}

// Also fix the webhook to prevent future issues
echo "<h3>🔧 Webhook Fix Recommendation:</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>⚠️ Webhook Issue Identified:</h4>";
echo "<p>The webhook has a potential issue where for existing users, it generates a new password but doesn't update the user table hash.</p>";
echo "<p><strong>Location:</strong> front-end/stripe-webhook.php lines 190-191</p>";
echo "<p><strong>Current code:</strong></p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px;'>";
echo "// For existing users, generate a new password for this session\n";
echo "\$password = bin2hex(random_bytes(4));\n";
echo "// But doesn't update user table hash!";
echo "</pre>";
echo "<p><strong>Recommended fix:</strong> Either use the existing user's password or update the hash when generating a new password.</p>";
echo "</div>";

// Test specific user
if (isset($_GET['test_user'])) {
    $test_user = $_GET['test_user'];
    echo "<h3>🧪 Testing User: $test_user</h3>";
    
    $test_query = "
        SELECT u.id, u.username, u.email, u.password as user_hash, pt.password as temp_password
        FROM user u
        LEFT JOIN payment_temp pt ON u.username = pt.username
        WHERE u.username = ?
    ";
    
    $test_stmt = $conn->prepare($test_query);
    $test_stmt->bind_param("s", $test_user);
    $test_stmt->execute();
    $test_result = $test_stmt->get_result();
    
    if ($test_result->num_rows > 0) {
        $test_data = $test_result->fetch_assoc();
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>User Details:</h4>";
        echo "<p><strong>ID:</strong> " . $test_data['id'] . "</p>";
        echo "<p><strong>Username:</strong> " . $test_data['username'] . "</p>";
        echo "<p><strong>Email:</strong> " . $test_data['email'] . "</p>";
        echo "<p><strong>User Hash:</strong> " . substr($test_data['user_hash'], 0, 30) . "...</p>";
        echo "<p><strong>Temp Password:</strong> " . ($test_data['temp_password'] ?? 'Not found') . "</p>";
        
        if ($test_data['temp_password']) {
            $test_verify = password_verify($test_data['temp_password'], $test_data['user_hash']);
            echo "<p><strong>Password Verification:</strong> " . ($test_verify ? '✅ WORKS' : '❌ FAILS') . "</p>";
            
            if ($test_verify) {
                echo "<p style='color: green;'><strong>✅ This user can sign in with:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Username:</strong> " . $test_data['username'] . "</li>";
                echo "<li><strong>Email:</strong> " . $test_data['email'] . "</li>";
                echo "<li><strong>Password:</strong> " . $test_data['temp_password'] . "</li>";
                echo "</ul>";
            }
        }
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ User not found: $test_user</p>";
    }
}
?>

<h3>🧪 Test Specific User</h3>
<form method="GET">
    <label>Username to test:</label>
    <input type="text" name="test_user" placeholder="user95404" value="<?php echo $_GET['test_user'] ?? 'user95404'; ?>">
    <input type="submit" value="Test User">
</form>

<h3>🔗 Quick Actions</h3>
<p><a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Sign-In</a></p>
<p><a href="test-user95404-password.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Test user95404 Password</a></p>
<p><a href="../front-end/payment-success.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Payment Success Page</a></p>

<h3>✅ Expected Result</h3>
<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>
    <p>After running this fix:</p>
    <ol>
        <li>All users should have matching passwords between user table hash and payment_temp</li>
        <li>Sign-in should work with the credentials shown on payment success page</li>
        <li>No more "Wrong Username/Email or Password" errors for valid credentials</li>
    </ol>
</div>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
pre { font-size: 12px; }
form { margin: 20px 0; }
input[type="text"] { padding: 5px; margin: 5px; }
input[type="submit"] { padding: 8px 15px; background: #007cba; color: white; border: none; cursor: pointer; }
</style>
