# GraphQL Ticket API Examples

This folder contains examples for testing GraphQL ticket operations with Appika's GraphQL API.

## Endpoints

- **GraphQL API**: https://dev-sgsg-tktapi.appika.com/graphql
- **GraphiQL Interface**: https://dev-sgsg-tktapi.appika.com/graphiql

## Files

1. `ticket_queries.graphql` - Sample GraphQL queries for tickets
2. `ticket_mutations.graphql` - Sample GraphQL mutations for creating/updating tickets
3. `countries_example.php` - Example using Postman's countries API for testing
4. `ticket_schema_example.md` - Expected ticket schema structure

## Database Mapping

Your local database `support_tickets` table maps to Appika GraphQL API as follows:

| Local Database Field | GraphQL API Field | Description |
|----------------------|-------------------|-------------|
| `id` | `id` | Ticket ID |
| `subject` | `subject` | Ticket subject |
| `status` | `status` | Ticket status (open, in_progress, resolved, closed) |
| `priority` | `priority` | Ticket priority (low, medium, high, urgent) |
| `ticket_type` | `type_name` | Ticket type (starter, premium, ultimate) |
| `created_at` | `created` | Creation timestamp |
| `updated_at` | `updated` | Last update timestamp |
| `description` | `description` | Ticket description |
| `severity` | `severity` | Ticket severity |
| `problem_type` | `problem_type` | Problem type |
| - | `ticket_no` | Generated ticket number (TKT-XXXXXX) |
| - | `req_email` | Requester email (from user table) |

## Usage

1. Open the main test file: `../api/graphql_ticket_test.php`
2. Set your GraphQL API token
3. Test different operations using the tabbed interface
4. View the GraphiQL interface for interactive testing

## Example Queries

### Query Specific Tickets
```graphql
query {
  ticket1: getTicket(id: 1) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
  ticket3: getTicket(id: 3) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}
```

### Create Ticket
```graphql
mutation CreateTicket($input: CreateTicketInput!) {
  createTicket(input: $input) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}
```

### Update Ticket
```graphql
mutation UpdateTicket($id: ID!, $input: UpdateTicketInput!) {
  updateTicket(id: $id, input: $input) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}
```

## Testing with Local Database

The test interface shows sample tickets from your local database for reference. These can be used as templates for creating new tickets via the GraphQL API.
