<?php
// Simple test to verify webhook endpoint is accessible
echo "<h1>Webhook Endpoint Test</h1>";

echo "<h2>1. Testing Webhook URL Access</h2>";
echo "<p>This page confirms that the webhook endpoint is accessible.</p>";

echo "<h2>2. Testing Database Connection</h2>";
require_once '../functions/server.php';

if ($conn) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test user table
    $test_query = "SELECT COUNT(*) as count FROM user";
    $result = $conn->query($test_query);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>User table has " . $row['count'] . " records</p>";
    }
    
    // Test payment_temp table
    $test_query = "SELECT COUNT(*) as count FROM payment_temp";
    $result = $conn->query($test_query);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>Payment_temp table has " . $row['count'] . " records</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
}

echo "<h2>3. Webhook URL Information</h2>";
echo "<p>Webhook should be accessible at:</p>";
echo "<ul>";
echo "<li>Direct: <code>http://localhost/helloit/front-end/stripe-webhook.php</code></li>";
echo "<li>Clean URL: <code>http://localhost/helloit/support-ticket/webhook</code></li>";
echo "</ul>";

echo "<h2>4. Recent Webhook Logs</h2>";
$log_file = __DIR__ . '/webhook.log';
if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
    $lines = explode("\n", $log_content);
    $recent_lines = array_slice($lines, -20); // Last 20 lines
    
    echo "<pre style='background: #f5f5f5; padding: 10px; border-radius: 5px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "<p>No webhook log file found.</p>";
}

echo "<h2>5. Test Summary</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px;'>";
echo "<p><strong>If you see this page, the webhook endpoint is accessible!</strong></p>";
echo "<p>The 404 errors in Stripe logs should be resolved now.</p>";
echo "</div>";
?>
