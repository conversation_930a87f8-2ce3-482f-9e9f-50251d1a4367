<?php
include('server.php');

echo "<h2>🧪 Test user95404 Password Verification</h2>";

$username = 'user95404';
$displayed_password = 'dc6fb0b1';

// Get user data from database
$user_query = "SELECT id, username, email, password FROM user WHERE username = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $username);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows > 0) {
    $user_data = $user_result->fetch_assoc();
    $stored_hash = $user_data['password'];
    
    echo "<h3>Database Data:</h3>";
    echo "<p><strong>Username:</strong> " . $user_data['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $user_data['email'] . "</p>";
    echo "<p><strong>Stored Hash:</strong> " . $stored_hash . "</p>";
    echo "<p><strong>Hash Type:</strong> " . (strpos($stored_hash, '$2y$') === 0 ? 'bcrypt' : 'md5') . "</p>";
    
    echo "<h3>Password Verification Tests:</h3>";
    
    // Test 1: Direct verification with displayed password
    echo "<h4>Test 1: Direct Verification</h4>";
    echo "<p><strong>Testing password:</strong> '$displayed_password'</p>";
    
    if (strpos($stored_hash, '$2y$') === 0) {
        $verify_result = password_verify($displayed_password, $stored_hash);
        echo "<p><strong>bcrypt verification result:</strong> " . ($verify_result ? '✅ PASS' : '❌ FAIL') . "</p>";
        
        if (!$verify_result) {
            echo "<p style='color: red;'>❌ The displayed password does NOT verify against the stored hash!</p>";
        }
    } else {
        $verify_result = ($stored_hash === md5($displayed_password));
        echo "<p><strong>MD5 verification result:</strong> " . ($verify_result ? '✅ PASS' : '❌ FAIL') . "</p>";
    }
    
    // Test 2: Check what password was actually saved to payment_temp
    echo "<h4>Test 2: Payment Temp Password Check</h4>";
    $temp_query = "SELECT password FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1";
    $temp_stmt = $conn->prepare($temp_query);
    $temp_stmt->bind_param("s", $username);
    $temp_stmt->execute();
    $temp_result = $temp_stmt->get_result();
    
    if ($temp_result->num_rows > 0) {
        $temp_data = $temp_result->fetch_assoc();
        $temp_password = $temp_data['password'];
        
        echo "<p><strong>Password in payment_temp:</strong> '$temp_password'</p>";
        echo "<p><strong>Matches displayed password:</strong> " . ($temp_password === $displayed_password ? '✅ YES' : '❌ NO') . "</p>";
        
        // Test if temp password verifies against hash
        if (strpos($stored_hash, '$2y$') === 0) {
            $temp_verify = password_verify($temp_password, $stored_hash);
            echo "<p><strong>bcrypt verify(temp_password, hash):</strong> " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
        } else {
            $temp_verify = ($stored_hash === md5($temp_password));
            echo "<p><strong>MD5 verify(temp_password, hash):</strong> " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ No password found in payment_temp</p>";
    }
    
    // Test 3: Generate what the hash should be
    echo "<h4>Test 3: Hash Generation Test</h4>";
    $test_hash = password_hash($displayed_password, PASSWORD_DEFAULT);
    echo "<p><strong>New hash for '$displayed_password':</strong> " . substr($test_hash, 0, 30) . "...</p>";
    
    $test_verify = password_verify($displayed_password, $test_hash);
    echo "<p><strong>Verification of new hash:</strong> " . ($test_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
    
    // Test 4: Check if there's a mismatch in the webhook process
    echo "<h4>Test 4: Webhook Process Analysis</h4>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>What Should Have Happened:</h5>";
    echo "<ol>";
    echo "<li>Webhook generates: username='user95404', password='dc6fb0b1'</li>";
    echo "<li>Webhook creates hash: password_hash('dc6fb0b1', PASSWORD_DEFAULT)</li>";
    echo "<li>Webhook saves to user table: hash</li>";
    echo "<li>Webhook saves to payment_temp: 'dc6fb0b1' (raw)</li>";
    echo "<li>Sign-in should verify: password_verify('dc6fb0b1', hash) = TRUE</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>What Actually Happened:</h5>";
    echo "<ol>";
    echo "<li>✅ Webhook generated: username='user95404', password='dc6fb0b1'</li>";
    echo "<li>❓ Webhook created hash: " . substr($stored_hash, 0, 30) . "...</li>";
    echo "<li>✅ Webhook saved to user table: hash</li>";
    echo "<li>✅ Webhook saved to payment_temp: 'dc6fb0b1'</li>";
    echo "<li>❌ Sign-in verification: password_verify('dc6fb0b1', hash) = FALSE</li>";
    echo "</ol>";
    echo "</div>";
    
    // Test 5: Manual sign-in simulation
    echo "<h4>Test 5: Manual Sign-In Simulation</h4>";
    $login_input = $username;
    $password_input = $displayed_password;
    
    echo "<p><strong>Simulating sign-in with:</strong></p>";
    echo "<ul>";
    echo "<li>Login input: '$login_input'</li>";
    echo "<li>Password input: '$password_input'</li>";
    echo "</ul>";
    
    // Step 1: Find user
    $signin_stmt = $conn->prepare("SELECT * FROM user WHERE username = ? LIMIT 1");
    $signin_stmt->bind_param("s", $login_input);
    $signin_stmt->execute();
    $signin_result = $signin_stmt->get_result();
    
    if ($signin_result->num_rows == 1) {
        $signin_user = $signin_result->fetch_assoc();
        $signin_hash = $signin_user['password'];
        
        echo "<p>✅ <strong>Step 1:</strong> User found</p>";
        
        // Step 2: Direct password verification
        if (strpos($signin_hash, '$2y$') === 0) {
            $direct_verify = password_verify($password_input, $signin_hash);
            echo "<p><strong>Step 2:</strong> Direct bcrypt verification: " . ($direct_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
            
            if (!$direct_verify) {
                // Step 3: Check payment_temp
                echo "<p><strong>Step 3:</strong> Checking payment_temp for alternative password...</p>";
                
                $temp_check_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1");
                $temp_check_stmt->bind_param("s", $login_input);
                $temp_check_stmt->execute();
                $temp_check_result = $temp_check_stmt->get_result();
                
                if ($temp_check_result->num_rows > 0) {
                    $temp_check_data = $temp_check_result->fetch_assoc();
                    $temp_check_password = $temp_check_data['password'];
                    
                    echo "<p>Found temp password: '$temp_check_password'</p>";
                    echo "<p>Matches input: " . ($temp_check_password === $password_input ? '✅ YES' : '❌ NO') . "</p>";
                    
                    if ($temp_check_password === $password_input) {
                        $temp_verify = password_verify($temp_check_password, $signin_hash);
                        echo "<p>Temp password verification: " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
                        
                        if ($temp_verify) {
                            echo "<p style='color: green;'>✅ <strong>RESULT:</strong> Sign-in should SUCCEED via temp password verification</p>";
                        } else {
                            echo "<p style='color: red;'>❌ <strong>RESULT:</strong> Sign-in will FAIL - temp password doesn't verify against hash</p>";
                        }
                    }
                }
            } else {
                echo "<p style='color: green;'>✅ <strong>RESULT:</strong> Sign-in should SUCCEED via direct verification</p>";
            }
        }
    } else {
        echo "<p style='color: red;'>❌ <strong>Step 1:</strong> User not found</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ User not found in database</p>";
}
?>

<h3>🔧 Solution</h3>
<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>
    <h4>If the password verification is failing:</h4>
    <ol>
        <li><strong>Check webhook logic:</strong> Ensure the same password is hashed and saved to payment_temp</li>
        <li><strong>Check sign-in logic:</strong> Ensure it properly handles temp password verification</li>
        <li><strong>Manual fix:</strong> Update payment_temp with the correct password that verifies</li>
    </ol>
</div>

<h3>🔗 Quick Actions</h3>
<p><a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Sign-In</a></p>
<p><a href="debug-signin-issue.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Debug Sign-In</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
</style>
