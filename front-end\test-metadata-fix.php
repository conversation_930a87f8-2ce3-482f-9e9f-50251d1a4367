<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h1>Stripe Metadata Fix Test</h1>";

// Simulate a large guest cart that would cause the 500-character limit issue
$large_guest_cart = [
    [
        'ticket_id' => 1,
        'ticket_type' => 'STARTER',
        'package_size' => 'XS',
        'numbers_per_package' => 1,
        'dollar_price_per_package' => 20,
        'quantity' => 1
    ],
    [
        'ticket_id' => 2,
        'ticket_type' => 'STARTER',
        'package_size' => 'S',
        'numbers_per_package' => 15,
        'dollar_price_per_package' => 150,
        'quantity' => 1
    ],
    [
        'ticket_id' => 3,
        'ticket_type' => 'BUSINESS',
        'package_size' => 'XS',
        'numbers_per_package' => 1,
        'dollar_price_per_package' => 25,
        'quantity' => 1
    ],
    [
        'ticket_id' => 4,
        'ticket_type' => 'BUSINESS',
        'package_size' => 'S',
        'numbers_per_package' => 10,
        'dollar_price_per_package' => 200,
        'quantity' => 1
    ],
    [
        'ticket_id' => 5,
        'ticket_type' => 'ULTIMATE',
        'package_size' => 'XS',
        'numbers_per_package' => 1,
        'dollar_price_per_package' => 30,
        'quantity' => 1
    ],
    [
        'ticket_id' => 6,
        'ticket_type' => 'ULTIMATE',
        'package_size' => 'S',
        'numbers_per_package' => 10,
        'dollar_price_per_package' => 250,
        'quantity' => 1
    ]
];

echo "<h2>Test Scenario: Large Cart Metadata</h2>";
echo "<p>This simulates the exact scenario that caused the 722-character error.</p>";

// Test old method (would fail)
echo "<h3>1. Old Method (JSON in metadata - WOULD FAIL)</h3>";
$old_cart_json = json_encode($large_guest_cart);
echo "<p><strong>JSON Length:</strong> " . strlen($old_cart_json) . " characters</p>";
echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; border-radius: 5px; color: #721c24;'>";
echo "<strong>❌ ERROR:</strong> This would exceed Stripe's 500-character limit!<br>";
echo "<strong>Actual JSON:</strong> <code style='word-break: break-all;'>" . htmlspecialchars($old_cart_json) . "</code>";
echo "</div>";

// Test new method (should work)
echo "<h3>2. New Method (Individual metadata fields - SHOULD WORK)</h3>";

$cart_metadata = [];
foreach ($large_guest_cart as $item) {
    $cart_metadata[] = [
        'ticket_type' => $item['ticket_type'],
        'package_size' => $item['package_size'] ?? '',
        'numbers_per_package' => $item['numbers_per_package'] ?? 1,
        'dollar_price_per_package' => $item['dollar_price_per_package'],
        'quantity' => $item['quantity']
    ];
}

// Create metadata with individual items (limited approach to avoid 500 char limit)
$metadata = [
    'user_id' => 'guest',
    'total_items' => count($cart_metadata)
];

// Add up to 5 items individually to metadata
$item_count = 0;
foreach ($cart_metadata as $index => $item) {
    if ($item_count >= 5) break;
    
    $metadata["item_{$index}_type"] = $item['ticket_type'];
    $metadata["item_{$index}_size"] = $item['package_size'] ?? '';
    $metadata["item_{$index}_qty"] = $item['quantity'];
    $metadata["item_{$index}_price"] = $item['dollar_price_per_package'];
    $item_count++;
}

if (count($cart_metadata) > 5) {
    $metadata['has_more_items'] = 'true';
    $metadata['overflow_count'] = count($cart_metadata) - 5;
}

echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;'>";
echo "<strong>✅ SUCCESS:</strong> New method uses individual metadata fields<br>";
echo "<strong>Total metadata fields:</strong> " . count($metadata) . "<br>";
echo "<strong>Metadata structure:</strong><br>";
echo "<pre>" . json_encode($metadata, JSON_PRETTY_PRINT) . "</pre>";
echo "</div>";

// Calculate total character usage
$total_chars = 0;
foreach ($metadata as $key => $value) {
    $total_chars += strlen($key) + strlen($value);
}
echo "<p><strong>Total character usage:</strong> " . $total_chars . " characters (well under 500 limit per field)</p>";

echo "<h2>Test Instructions</h2>";
echo "<div style='background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h4>How to Test the Fix:</h4>";
echo "<ol>";
echo "<li><strong>Clear your session:</strong> Close browser or clear cookies</li>";
echo "<li><strong>Add multiple items to cart:</strong> Go to <a href='/helloit/support-ticket/buy-now' target='_blank'>buy-now page</a> and add 6+ different items</li>";
echo "<li><strong>Try guest checkout:</strong> Click 'Pay Now' without logging in</li>";
echo "<li><strong>Expected result:</strong> Should proceed to Stripe checkout without the 722-character error</li>";
echo "</ol>";
echo "</div>";

echo "<h2>Webhook Compatibility</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;'>";
echo "<h4>Webhook Processing:</h4>";
echo "<ul>";
echo "<li>✅ Webhook updated to handle new individual metadata format</li>";
echo "<li>✅ Fallback support for old JSON format (backward compatibility)</li>";
echo "<li>✅ Database lookup for missing numbers_per_package data</li>";
echo "<li>✅ Comprehensive logging for debugging</li>";
echo "</ul>";
echo "</div>";

// Simulate what the webhook would receive
echo "<h2>Webhook Processing Simulation</h2>";
echo "<h3>How webhook will process the new metadata:</h3>";

$simulated_purchased_items = [];
if (isset($metadata['total_items']) && $metadata['total_items'] > 0) {
    for ($i = 0; $i < $metadata['total_items']; $i++) {
        if (isset($metadata["item_{$i}_type"])) {
            $simulated_purchased_items[] = [
                'ticket_type' => $metadata["item_{$i}_type"],
                'package_size' => $metadata["item_{$i}_size"] ?? '',
                'numbers_per_package' => 1, // Will be fetched from database
                'dollar_price_per_package' => $metadata["item_{$i}_price"] ?? 0,
                'quantity' => $metadata["item_{$i}_qty"] ?? 1
            ];
        }
    }
}

echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<strong>Webhook will process these items:</strong><br>";
echo "<pre>" . json_encode($simulated_purchased_items, JSON_PRETTY_PRINT) . "</pre>";
echo "</div>";

if (count($cart_metadata) > 5) {
    echo "<div style='background: #fff3cd; padding: 10px; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404; margin-top: 10px;'>";
    echo "<strong>⚠️ Note:</strong> " . (count($cart_metadata) - 5) . " additional items will be processed from Stripe line items (fallback method)";
    echo "</div>";
}

echo "<h2>Quick Actions</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='/helloit/support-ticket/buy-now' style='background: #6754e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Buy Now</a>";
echo "<a href='cart.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Cart</a>";
echo "<a href='stripe-webhook.php' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Webhook</a>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    overflow-x: auto;
    font-size: 12px;
}

code {
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

a {
    color: #6754e2;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
