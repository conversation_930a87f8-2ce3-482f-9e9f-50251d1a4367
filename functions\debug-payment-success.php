<?php
include('server.php');

// Get session_id from URL (simulate payment success page)
$session_id = $_GET['session_id'] ?? '';

if (empty($session_id)) {
    echo "<h2>❌ No session_id provided</h2>";
    echo "<p>Please provide a session_id in the URL: ?session_id=cs_test_...</p>";
    exit;
}

echo "<h2>🔍 Payment Success Page Debug</h2>";
echo "<p><strong>Session ID:</strong> " . htmlspecialchars($session_id) . "</p>";

// Simulate the exact logic from payment-success.php
echo "<h3>Step 1: Check payment_temp by session_id</h3>";
$temp_stmt = $conn->prepare("SELECT username, email, password FROM payment_temp WHERE session_id = ? ORDER BY id DESC LIMIT 1");
$temp_stmt->bind_param("s", $session_id);
$temp_stmt->execute();
$temp_result = $temp_stmt->get_result();

if ($temp_result->num_rows > 0) {
    $temp_data = $temp_result->fetch_assoc();
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Found in payment_temp by session_id:</h4>";
    echo "<p><strong>Username:</strong> " . $temp_data['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $temp_data['email'] . "</p>";
    echo "<p><strong>Password:</strong> " . $temp_data['password'] . "</p>";
    echo "</div>";
    
    $guest_credentials = [
        'username' => $temp_data['username'],
        'email' => $temp_data['email'],
        'password' => $temp_data['password']
    ];
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h4>❌ NOT found in payment_temp by session_id</h4>";
    echo "</div>";
    
    // Try to get customer email from Stripe (simulate)
    $customer_email = '<EMAIL>'; // This would come from Stripe session
    
    echo "<h3>Step 2: Check user table by email</h3>";
    $user_check_stmt = $conn->prepare("SELECT username, email FROM user WHERE email = ? ORDER BY id DESC LIMIT 1");
    $user_check_stmt->bind_param("s", $customer_email);
    $user_check_stmt->execute();
    $user_check_result = $user_check_stmt->get_result();
    
    if ($user_check_result->num_rows > 0) {
        $user_check_data = $user_check_result->fetch_assoc();
        $recent_username = $user_check_data['username'];
        
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Found user in main table:</h4>";
        echo "<p><strong>Username:</strong> " . $recent_username . "</p>";
        echo "<p><strong>Email:</strong> " . $user_check_data['email'] . "</p>";
        echo "</div>";
        
        echo "<h3>Step 3: Check payment_temp by username</h3>";
        $temp_by_user_stmt = $conn->prepare("SELECT username, email, password FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1");
        $temp_by_user_stmt->bind_param("s", $recent_username);
        $temp_by_user_stmt->execute();
        $temp_by_user_result = $temp_by_user_stmt->get_result();
        
        if ($temp_by_user_result->num_rows > 0) {
            $temp_by_user_data = $temp_by_user_result->fetch_assoc();
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Found password in payment_temp:</h4>";
            echo "<p><strong>Username:</strong> " . $temp_by_user_data['username'] . "</p>";
            echo "<p><strong>Email:</strong> " . $temp_by_user_data['email'] . "</p>";
            echo "<p><strong>Password:</strong> " . $temp_by_user_data['password'] . "</p>";
            echo "</div>";
            
            $guest_credentials = [
                'username' => $temp_by_user_data['username'],
                'email' => $temp_by_user_data['email'],
                'password' => $temp_by_user_data['password']
            ];
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h4>⚠️ User exists but no password in payment_temp</h4>";
            echo "<p>Would generate new password for: " . $recent_username . "</p>";
            echo "</div>";
            
            $guest_credentials = [
                'username' => $recent_username,
                'email' => $customer_email,
                'password' => 'WOULD_GENERATE_NEW'
            ];
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h4>❌ No user found in main table</h4>";
        echo "<p>Would generate completely new credentials</p>";
        echo "</div>";
        
        $guest_credentials = [
            'username' => 'WOULD_GENERATE_NEW',
            'email' => $customer_email,
            'password' => 'WOULD_GENERATE_NEW'
        ];
    }
}

echo "<h3>🎯 Final Result (What Payment Success Page Would Show):</h3>";
echo "<div style='background: #e3f2fd; border: 2px solid #2196f3; padding: 20px; border-radius: 10px;'>";
echo "<h4>Credentials that would be displayed:</h4>";
echo "<p><strong>Username:</strong> " . $guest_credentials['username'] . "</p>";
echo "<p><strong>Email:</strong> " . $guest_credentials['email'] . "</p>";
echo "<p><strong>Password:</strong> " . $guest_credentials['password'] . "</p>";
echo "</div>";

// Check if these credentials would actually work
if ($guest_credentials['username'] !== 'WOULD_GENERATE_NEW') {
    echo "<h3>🧪 Sign-In Test:</h3>";
    $test_username = $guest_credentials['username'];
    $test_password = $guest_credentials['password'];
    
    // Check if user exists in main table
    $signin_test_stmt = $conn->prepare("SELECT id, username, email, password FROM user WHERE username = ?");
    $signin_test_stmt->bind_param("s", $test_username);
    $signin_test_stmt->execute();
    $signin_test_result = $signin_test_stmt->get_result();
    
    if ($signin_test_result->num_rows > 0) {
        $signin_user = $signin_test_result->fetch_assoc();
        $stored_hash = $signin_user['password'];
        
        // Test password verification
        if (strpos($stored_hash, '$2y$') === 0) {
            $verify_result = password_verify($test_password, $stored_hash);
            $hash_type = 'bcrypt';
        } else {
            $verify_result = ($stored_hash === md5($test_password));
            $hash_type = 'md5';
        }
        
        if ($verify_result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Sign-In Would SUCCEED</h4>";
            echo "<p>User exists and password verification passes ($hash_type)</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Sign-In Would FAIL</h4>";
            echo "<p>User exists but password verification fails ($hash_type)</p>";
            echo "<p><strong>Stored hash:</strong> " . substr($stored_hash, 0, 30) . "...</p>";
            echo "<p><strong>Test password:</strong> $test_password</p>";
            echo "<p><strong>MD5 of test password:</strong> " . md5($test_password) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ Sign-In Would Show 'Account Being Set Up'</h4>";
        echo "<p>User not found in main table (webhook pending)</p>";
        echo "</div>";
    }
}

// Show recent session IDs for reference
echo "<h3>📋 Recent Session IDs in payment_temp:</h3>";
$recent_sessions_query = "SELECT session_id, username, email FROM payment_temp ORDER BY id DESC LIMIT 5";
$recent_sessions_result = mysqli_query($conn, $recent_sessions_query);

if ($recent_sessions_result && mysqli_num_rows($recent_sessions_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Session ID</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Email</th>";
    echo "</tr>";
    
    while ($session_row = mysqli_fetch_assoc($recent_sessions_result)) {
        $is_current = ($session_row['session_id'] === $session_id);
        $row_color = $is_current ? '#fff3cd' : 'white';
        
        echo "<tr style='background: $row_color;'>";
        echo "<td style='padding: 10px;'>" . substr($session_row['session_id'], 0, 30) . "...</td>";
        echo "<td style='padding: 10px;'>" . $session_row['username'] . "</td>";
        echo "<td style='padding: 10px;'>" . $session_row['email'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
?>

<h3>🔧 Test Different Session IDs:</h3>
<form method="GET">
    <label>Session ID:</label><br>
    <input type="text" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>" style="width: 500px; padding: 5px;">
    <input type="submit" value="Test" style="padding: 5px 15px;">
</form>

<h3>🔗 Quick Links:</h3>
<p><a href="check-recent-purchase.php">Check Recent Purchase</a></p>
<p><a href="../front-end/sign-in.php">Go to Sign-In</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
</style>
