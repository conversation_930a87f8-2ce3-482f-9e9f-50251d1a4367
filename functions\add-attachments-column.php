<?php
include('server.php');

echo "<h2>🔧 Add Attachments Column to support_tickets Table</h2>";

echo "<h3>🔍 **Current Table Structure**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check current support_tickets table structure
$table_info_query = "DESCRIBE support_tickets";
$table_info_result = mysqli_query($conn, $table_info_query);

echo "<h4>Current support_tickets Table Columns:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
echo "<tr style='background: #f8f9fa;'>";
echo "<th style='padding: 8px;'>Field</th>";
echo "<th style='padding: 8px;'>Type</th>";
echo "<th style='padding: 8px;'>Null</th>";
echo "<th style='padding: 8px;'>Key</th>";
echo "<th style='padding: 8px;'>Default</th>";
echo "<th style='padding: 8px;'>Extra</th>";
echo "</tr>";

$has_attachments_column = false;
$columns = [];
while ($row = mysqli_fetch_assoc($table_info_result)) {
    echo "<tr>";
    echo "<td style='padding: 8px;'>" . $row['Field'] . "</td>";
    echo "<td style='padding: 8px;'>" . $row['Type'] . "</td>";
    echo "<td style='padding: 8px;'>" . $row['Null'] . "</td>";
    echo "<td style='padding: 8px;'>" . $row['Key'] . "</td>";
    echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
    echo "<td style='padding: 8px;'>" . $row['Extra'] . "</td>";
    echo "</tr>";
    
    $columns[] = $row['Field'];
    if ($row['Field'] === 'attachments') {
        $has_attachments_column = true;
    }
}
echo "</table>";

echo "<p><strong>Total Columns:</strong> " . count($columns) . "</p>";
echo "<p><strong>Attachments Column Exists:</strong> " . ($has_attachments_column ? '✅ Yes' : '❌ No') . "</p>";
echo "</div>";

echo "<h3>🛠️ **Add Attachments Column**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";

if (isset($_POST['action']) && $_POST['action'] === 'add_attachments_column') {
    if (!$has_attachments_column) {
        // Add the attachments column
        $alter_query = "ALTER TABLE support_tickets ADD COLUMN attachments JSON NULL COMMENT 'JSON array of attachment file paths'";
        
        if (mysqli_query($conn, $alter_query)) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
            echo "<h4>✅ Column Added Successfully!</h4>";
            echo "<p>The 'attachments' column has been added to the support_tickets table.</p>";
            echo "<ul>";
            echo "<li><strong>Column Name:</strong> attachments</li>";
            echo "<li><strong>Data Type:</strong> JSON</li>";
            echo "<li><strong>Null:</strong> YES (optional)</li>";
            echo "<li><strong>Purpose:</strong> Store array of photo file paths</li>";
            echo "</ul>";
            echo "</div>";
            $has_attachments_column = true;
            
            // Refresh table info
            $table_info_query = "DESCRIBE support_tickets";
            $table_info_result = mysqli_query($conn, $table_info_query);
            
            echo "<h4>Updated Table Structure:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Field</th>";
            echo "<th style='padding: 8px;'>Type</th>";
            echo "<th style='padding: 8px;'>Null</th>";
            echo "<th style='padding: 8px;'>Key</th>";
            echo "<th style='padding: 8px;'>Default</th>";
            echo "<th style='padding: 8px;'>Extra</th>";
            echo "</tr>";
            
            while ($row = mysqli_fetch_assoc($table_info_result)) {
                $highlight = ($row['Field'] === 'attachments') ? 'background: #d4edda;' : '';
                echo "<tr style='$highlight'>";
                echo "<td style='padding: 8px;'>" . $row['Field'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Type'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Null'] . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Key'] . "</td>";
                echo "<td style='padding: 8px;'>" . ($row['Default'] ?? 'NULL') . "</td>";
                echo "<td style='padding: 8px;'>" . $row['Extra'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0; color: #721c24;'>";
            echo "<h4>❌ Error Adding Column</h4>";
            echo "<p><strong>Error:</strong> " . mysqli_error($conn) . "</p>";
            echo "<p><strong>Query:</strong> " . $alter_query . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; color: #0c5460;'>";
        echo "<h4>ℹ️ Column Already Exists</h4>";
        echo "<p>The 'attachments' column already exists in the support_tickets table.</p>";
        echo "</div>";
    }
}

if (!$has_attachments_column) {
    echo "<h4>⚠️ Missing Column: attachments</h4>";
    echo "<p style='color: #856404;'>The support_tickets table needs an 'attachments' column to store photo file paths.</p>";
    
    echo "<h4>Column Specification:</h4>";
    echo "<ul style='color: #856404;'>";
    echo "<li><strong>Column Name:</strong> attachments</li>";
    echo "<li><strong>Data Type:</strong> JSON</li>";
    echo "<li><strong>Null:</strong> YES (photos are optional)</li>";
    echo "<li><strong>Default:</strong> NULL</li>";
    echo "<li><strong>Purpose:</strong> Store array of photo file paths as JSON</li>";
    echo "</ul>";
    
    echo "<h4>SQL Command:</h4>";
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
    echo "ALTER TABLE support_tickets ADD COLUMN attachments JSON NULL COMMENT 'JSON array of attachment file paths';";
    echo "</pre>";
    
    echo "<form method='POST' style='margin: 15px 0;'>";
    echo "<input type='hidden' name='action' value='add_attachments_column'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 14px;' onclick='return confirm(\"Are you sure you want to add the attachments column to the support_tickets table?\")'>Add attachments Column</button>";
    echo "</form>";
} else {
    echo "<h4 style='color: #155724;'>✅ Column Already Exists</h4>";
    echo "<p style='color: #155724;'>The 'attachments' column already exists in the support_tickets table.</p>";
}
echo "</div>";

echo "<h3>📁 **Upload Directory Setup**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

$upload_dir = '../uploads/tickets';
$upload_dir_exists = is_dir($upload_dir);
$upload_dir_writable = $upload_dir_exists && is_writable($upload_dir);

echo "<h4>Directory Status:</h4>";
echo "<ul>";
echo "<li><strong>Upload Directory:</strong> uploads/tickets/</li>";
echo "<li><strong>Full Path:</strong> " . realpath($upload_dir) . "</li>";
echo "<li><strong>Exists:</strong> " . ($upload_dir_exists ? '✅ Yes' : '❌ No') . "</li>";
echo "<li><strong>Writable:</strong> " . ($upload_dir_writable ? '✅ Yes' : '❌ No') . "</li>";
echo "</ul>";

if (!$upload_dir_exists) {
    if (isset($_POST['action']) && $_POST['action'] === 'create_upload_dir') {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
            echo "<h4>✅ Directory Created!</h4>";
            echo "<p>Upload directory created successfully at: " . realpath($upload_dir) . "</p>";
            echo "</div>";
            $upload_dir_exists = true;
            $upload_dir_writable = true;
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;'>";
            echo "<h4>❌ Failed to Create Directory</h4>";
            echo "<p>Could not create upload directory. Check permissions.</p>";
            echo "</div>";
        }
    }
    
    if (!$upload_dir_exists) {
        echo "<form method='POST' style='margin: 10px 0;'>";
        echo "<input type='hidden' name='action' value='create_upload_dir'>";
        echo "<button type='submit' style='background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Upload Directory</button>";
        echo "</form>";
    }
}

if ($upload_dir_exists && $upload_dir_writable) {
    echo "<p style='color: #0c5460;'><strong>✅ Ready for file uploads!</strong></p>";
}
echo "</div>";

echo "<h3>🧪 **Test Photo Upload**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";

$database_ready = $has_attachments_column;
$directory_ready = $upload_dir_exists && $upload_dir_writable;
$ready_for_testing = $database_ready && $directory_ready;

echo "<h4>Readiness Checklist:</h4>";
echo "<ul>";
echo "<li>" . ($database_ready ? '✅' : '❌') . " <strong>Database:</strong> attachments column " . ($database_ready ? 'exists' : 'missing') . "</li>";
echo "<li>" . ($directory_ready ? '✅' : '❌') . " <strong>Upload Directory:</strong> " . ($directory_ready ? 'ready' : 'not ready') . "</li>";
echo "<li>" . ($ready_for_testing ? '✅' : '❌') . " <strong>Ready for Testing:</strong> " . ($ready_for_testing ? 'Yes' : 'No') . "</li>";
echo "</ul>";

if ($ready_for_testing) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; color: #155724;'>";
    echo "<h4>🚀 Ready to Test Photo Upload!</h4>";
    echo "<p>All prerequisites are met. You can now test the photo upload functionality.</p>";
    echo "<div style='display: flex; gap: 10px; margin: 15px 0;'>";
    echo "<a href='../front-end/create-ticket.php' style='background: #28a745; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px;'>Test Create Ticket Form</a>";
    echo "<a href='test-photo-upload.php' style='background: #007cba; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px;'>Test Photo Upload Only</a>";
    echo "</div>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; color: #856404;'>";
    echo "<h4>⚠️ Prerequisites Required</h4>";
    echo "<p>Complete the database and directory setup above before testing.</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>📋 **Summary**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>What This Script Does:</h4>";
echo "<ol>";
echo "<li><strong>Checks</strong> if the attachments column exists in support_tickets table</li>";
echo "<li><strong>Adds</strong> the attachments column if missing (JSON type, nullable)</li>";
echo "<li><strong>Creates</strong> the upload directory if missing</li>";
echo "<li><strong>Verifies</strong> directory permissions for file uploads</li>";
echo "<li><strong>Provides</strong> testing links once everything is ready</li>";
echo "</ol>";

echo "<h4>After Running This Script:</h4>";
echo "<ul>";
echo "<li>✅ Database will have attachments column for storing photo paths</li>";
echo "<li>✅ Upload directory will be created with proper permissions</li>";
echo "<li>✅ Photo upload functionality will work in create-ticket form</li>";
echo "<li>✅ Photos will be saved and linked to tickets properly</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
pre { font-family: 'Courier New', monospace; }
</style>
