<?php
session_start();
include('server.php');

// Get recent users and payment_temp data
echo "<h2>Recent Database Data</h2>";

echo "<h3>Recent Users (last 5)</h3>";
$user_query = "SELECT id, username, email, password, registration_time FROM user ORDER BY id DESC LIMIT 5";
$user_result = mysqli_query($conn, $user_query);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Password Hash</th><th>Registration Time</th></tr>";
while ($row = mysqli_fetch_assoc($user_result)) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['username'] . "</td>";
    echo "<td>" . $row['email'] . "</td>";
    echo "<td>" . substr($row['password'], 0, 30) . "...</td>";
    echo "<td>" . $row['registration_time'] . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Recent Payment Temp (last 10)</h3>";
$temp_query = "SELECT id, session_id, username, email, password FROM payment_temp ORDER BY id DESC LIMIT 10";
$temp_result = mysqli_query($conn, $temp_query);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Session ID</th><th>Username</th><th>Email</th><th>Password</th></tr>";
while ($row = mysqli_fetch_assoc($temp_result)) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . substr($row['session_id'], 0, 20) . "...</td>";
    echo "<td>" . $row['username'] . "</td>";
    echo "<td>" . $row['email'] . "</td>";
    echo "<td>" . $row['password'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Test specific user
if (isset($_GET['test_user'])) {
    $test_username = $_GET['test_user'];
    echo "<h3>Testing User: $test_username</h3>";
    
    // Check in user table
    $user_check = "SELECT id, username, email, password FROM user WHERE username = '$test_username' OR email = '$test_username'";
    $user_check_result = mysqli_query($conn, $user_check);
    
    if (mysqli_num_rows($user_check_result) > 0) {
        $user_data = mysqli_fetch_assoc($user_check_result);
        echo "<p><strong>Found in user table:</strong></p>";
        echo "<ul>";
        echo "<li>ID: " . $user_data['id'] . "</li>";
        echo "<li>Username: " . $user_data['username'] . "</li>";
        echo "<li>Email: " . $user_data['email'] . "</li>";
        echo "<li>Password Hash: " . $user_data['password'] . "</li>";
        echo "<li>Hash Type: " . (strpos($user_data['password'], '$2y$') === 0 ? 'bcrypt' : 'md5') . "</li>";
        echo "</ul>";
        
        // Check payment_temp for this user
        $temp_check = "SELECT password FROM payment_temp WHERE username = '$test_username' OR email = '$test_username' ORDER BY id DESC LIMIT 1";
        $temp_check_result = mysqli_query($conn, $temp_check);
        
        if (mysqli_num_rows($temp_check_result) > 0) {
            $temp_data = mysqli_fetch_assoc($temp_check_result);
            echo "<p><strong>Found in payment_temp:</strong></p>";
            echo "<ul>";
            echo "<li>Raw Password: " . $temp_data['password'] . "</li>";
            echo "<li>Password Verify Test: " . (password_verify($temp_data['password'], $user_data['password']) ? 'PASS' : 'FAIL') . "</li>";
            echo "</ul>";
        } else {
            echo "<p><strong>NOT found in payment_temp</strong></p>";
        }
    } else {
        echo "<p><strong>NOT found in user table</strong></p>";
        
        // Check if only in payment_temp
        $temp_only_check = "SELECT * FROM payment_temp WHERE username = '$test_username' OR email = '$test_username' ORDER BY id DESC LIMIT 1";
        $temp_only_result = mysqli_query($conn, $temp_only_check);
        
        if (mysqli_num_rows($temp_only_result) > 0) {
            $temp_only_data = mysqli_fetch_assoc($temp_only_result);
            echo "<p><strong>Found ONLY in payment_temp:</strong></p>";
            echo "<ul>";
            echo "<li>Session ID: " . $temp_only_data['session_id'] . "</li>";
            echo "<li>Username: " . $temp_only_data['username'] . "</li>";
            echo "<li>Email: " . $temp_only_data['email'] . "</li>";
            echo "<li>Password: " . $temp_only_data['password'] . "</li>";
            echo "</ul>";
            echo "<p><em>This means webhook hasn't processed yet!</em></p>";
        }
    }
}

echo "<h3>Test a User</h3>";
echo "<form method='GET'>";
echo "<input type='text' name='test_user' placeholder='Enter username or email' value='" . ($_GET['test_user'] ?? '') . "'>";
echo "<input type='submit' value='Test User'>";
echo "</form>";
?>
