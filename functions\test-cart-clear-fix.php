<?php
session_start();
include('server.php');

echo "<h2>🧪 Test Cart Clearing Fix</h2>";

echo "<h3>📊 **Current Cart Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check guest cart in session
echo "<h4>Guest Cart (Session):</h4>";
if (isset($_SESSION['guest_cart']) && !empty($_SESSION['guest_cart'])) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; color: #856404;'>";
    echo "<strong>⚠️ Guest cart has " . count($_SESSION['guest_cart']) . " items:</strong><br>";
    foreach ($_SESSION['guest_cart'] as $index => $item) {
        echo "- Item $index: " . ($item['ticket_type'] ?? 'Unknown') . " (Qty: " . ($item['quantity'] ?? 0) . ")<br>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; color: #155724;'>";
    echo "<strong>✅ Guest cart is empty</strong>";
    echo "</div>";
}

echo "</div>";

echo "<h3>🔧 **Test Cart Clearing**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'add_guest_items':
            // Add test items to guest cart
            $_SESSION['guest_cart'] = [
                [
                    'ticket_id' => 1,
                    'ticket_type' => 'STARTER',
                    'package_size' => 'XS',
                    'quantity' => 1,
                    'dollar_price_per_package' => 20.00
                ],
                [
                    'ticket_id' => 2,
                    'ticket_type' => 'BUSINESS',
                    'package_size' => 'S',
                    'quantity' => 2,
                    'dollar_price_per_package' => 25.00
                ]
            ];
            
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "<h4>✅ Test Guest Cart Created</h4>";
            echo "<p>Added 2 test items to guest cart</p>";
            echo "</div>";
            break;
            
        case 'clear_guest_cart':
            // Clear guest cart (simulating payment success)
            if (isset($_SESSION['guest_cart'])) {
                $item_count = count($_SESSION['guest_cart']);
                unset($_SESSION['guest_cart']);
                
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "<h4>✅ Guest Cart Cleared</h4>";
                echo "<p>Removed $item_count items from guest cart (simulating payment success)</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
                echo "<h4>⚠️ No Guest Cart to Clear</h4>";
                echo "<p>Guest cart was already empty</p>";
                echo "</div>";
            }
            break;
    }
}

echo "<h4>Test Actions:</h4>";

// Add guest cart items
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>1. Create Test Guest Cart</h5>";
echo "<form method='POST' style='display: inline; margin-right: 10px;'>";
echo "<input type='hidden' name='action' value='add_guest_items'>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Add Test Items to Cart</button>";
echo "</form>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>2. Test Cart Clearing (Simulate Payment Success)</h5>";
echo "<form method='POST' style='display: inline;'>";
echo "<input type='hidden' name='action' value='clear_guest_cart'>";
echo "<button type='submit' style='background: #ffc107; color: black; padding: 8px 16px; border: none; border-radius: 4px;'>Clear Cart (Payment Success)</button>";
echo "</form>";
echo "</div>";

echo "</div>";

echo "<h3>📋 **Testing Instructions**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

echo "<h4>How to Test the Fix:</h4>";
echo "<ol>";
echo "<li><strong>Add test items</strong> using the button above</li>";
echo "<li><strong>Go to cart page</strong> - you should see the items</li>";
echo "<li><strong>Make a real purchase</strong> as a guest user</li>";
echo "<li><strong>After payment success</strong> - cart should be empty</li>";
echo "<li><strong>Go back to cart page</strong> - should show 'Your cart is empty'</li>";
echo "</ol>";

echo "<h4>What the Fix Does:</h4>";
echo "<ul>";
echo "<li>✅ <strong>payment-success.php:</strong> Clears \$_SESSION['guest_cart'] when guest purchase is detected</li>";
echo "<li>✅ <strong>JavaScript:</strong> Clears any localStorage/sessionStorage cart data</li>";
echo "<li>✅ <strong>Immediate clearing:</strong> Cart is cleared as soon as payment success page loads</li>";
echo "</ul>";

echo "<h4>Expected Result:</h4>";
echo "<ul>";
echo "<li>✅ Guest cart should be empty after successful payment</li>";
echo "<li>✅ Cart page should show 'Your cart is empty' message</li>";
echo "<li>✅ User can add new items to cart for next purchase</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔍 **Quick Test Links**</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Test the Complete Flow:</h4>";
echo "<ol>";
echo "<li><a href='/helloit/support-ticket/buy-now' target='_blank' style='color: #473BF0;'>Add items to cart</a></li>";
echo "<li><a href='/helloit/front-end/cart.php' target='_blank' style='color: #473BF0;'>View cart page</a></li>";
echo "<li><strong>Make a guest purchase</strong> (complete payment)</li>";
echo "<li><a href='/helloit/front-end/cart.php' target='_blank' style='color: #473BF0;'>Check cart is empty</a></li>";
echo "</ol>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
button:hover { opacity: 0.9; }
ol, ul { text-align: left; }
a { text-decoration: none; }
a:hover { text-decoration: underline; }
</style>
