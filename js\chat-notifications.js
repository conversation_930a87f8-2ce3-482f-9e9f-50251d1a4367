/**
 * Chat Notifications System
 * Handles browser notifications and sound alerts for chat messages
 */

class ChatNotificationSystem {
  constructor(options = {}) {
    // Default options
    this.options = {
      soundEnabled: true,
      browserNotificationsEnabled: true,
      soundFile: "../sounds/mixkit-software-interface-start-2574.wav",
      notificationTitle: "New Message",
      notificationIcon: "../image/png/favicon.png",
      notificationTimeout: 5000,
      tabTitle: document.title,
      unreadBadgeText: "(🔔) ",
      ...options,
    };

    // Initialize properties
    this.audioElement = null;
    this.notificationPermission = "default";
    this.unreadCount = 0;
    this.originalTitle = document.title;
    this.titleInterval = null;
    this.isFocused = true;
    this.isInitialized = false;

    // Bind methods
    this.initialize = this.initialize.bind(this);
    this.playSound = this.playSound.bind(this);
    this.showBrowserNotification = this.showBrowserNotification.bind(this);
    this.updateTabTitle = this.updateTabTitle.bind(this);
    this.resetTabTitle = this.resetTabTitle.bind(this);
    this.notify = this.notify.bind(this);
    this.requestPermission = this.requestPermission.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
  }

  /**
   * Initialize the notification system
   */
  initialize() {
    if (this.isInitialized) return;

    // Create audio element if sound is enabled
    if (this.options.soundEnabled) {
      this.audioElement = new Audio(this.options.soundFile);
      this.audioElement.preload = "auto";
    }

    // Check notification permission
    if (this.options.browserNotificationsEnabled && "Notification" in window) {
      this.notificationPermission = Notification.permission;

      // Request permission if not granted
      if (
        this.notificationPermission !== "granted" &&
        this.notificationPermission !== "denied"
      ) {
        // We'll request permission when user interacts with the page
        document.addEventListener("click", this.requestPermission, {
          once: true,
        });
      }
    }

    // Set up visibility change detection
    document.addEventListener("visibilitychange", this.handleVisibilityChange);
    window.addEventListener("focus", () => {
      this.isFocused = true;
      this.resetTabTitle();
    });
    window.addEventListener("blur", () => {
      this.isFocused = false;
    });

    // Store original title
    this.originalTitle = document.title;
    this.options.tabTitle = document.title;

    this.isInitialized = true;
    console.log("Chat notification system initialized");
  }

  /**
   * Request notification permission
   */
  requestPermission() {
    if (
      "Notification" in window &&
      Notification.permission !== "granted" &&
      Notification.permission !== "denied"
    ) {
      Notification.requestPermission().then((permission) => {
        this.notificationPermission = permission;
        console.log("Notification permission:", permission);
      });
    }
  }

  /**
   * Handle visibility change events
   */
  handleVisibilityChange() {
    this.isFocused = document.visibilityState === "visible";

    if (this.isFocused) {
      this.resetTabTitle();
      this.unreadCount = 0;
    }
  }

  /**
   * Play notification sound
   */
  playSound() {
    if (!this.options.soundEnabled || !this.audioElement) return;

    // Reset the audio and play
    this.audioElement.currentTime = 0;
    this.audioElement.play().catch((error) => {
      console.warn("Could not play notification sound:", error);
    });
  }

  /**
   * Show browser notification
   * @param {string} message - The notification message
   * @param {Object} data - Additional notification data
   */
  showBrowserNotification(message, data = {}) {
    if (
      !this.options.browserNotificationsEnabled ||
      !("Notification" in window)
    )
      return;
    if (this.notificationPermission !== "granted") return;
    if (this.isFocused) return; // Don't show browser notifications if the tab is focused

    const title = data.title || this.options.notificationTitle;
    const options = {
      body: message,
      icon: this.options.notificationIcon,
      tag: "chat-notification",
      ...data,
    };

    try {
      const notification = new Notification(title, options);

      // Auto close after timeout
      setTimeout(() => {
        notification.close();
      }, this.options.notificationTimeout);

      // Handle click
      notification.onclick = () => {
        window.focus();
        notification.close();
      };
    } catch (error) {
      console.warn("Could not create notification:", error);
    }
  }

  /**
   * Update the tab title to indicate unread messages
   */
  updateTabTitle() {
    if (this.isFocused) return;

    this.unreadCount++;

    // Clear existing interval if any
    if (this.titleInterval) {
      clearInterval(this.titleInterval);
    }

    // Set up flashing title
    let showOriginal = false;
    this.titleInterval = setInterval(() => {
      document.title = showOriginal
        ? this.originalTitle
        : `${this.options.unreadBadgeText}${this.originalTitle}`;
      showOriginal = !showOriginal;
    }, 1000);
  }

  /**
   * Reset the tab title to its original state
   */
  resetTabTitle() {
    if (this.titleInterval) {
      clearInterval(this.titleInterval);
      this.titleInterval = null;
    }
    document.title = this.originalTitle;
  }

  /**
   * Trigger a notification (sound, browser notification, and tab title update)
   * @param {string} message - The notification message
   * @param {Object} data - Additional notification data
   */
  notify(message, data = {}) {
    if (!this.isInitialized) {
      this.initialize();
    }

    // Don't notify if the tab is focused and we're not forcing notifications
    if (this.isFocused && !data.forceNotification) return;

    // Play sound
    this.playSound();

    // Show browser notification
    this.showBrowserNotification(message, data);

    // Update tab title
    this.updateTabTitle();
  }
}

// Create global instance
window.chatNotificationSystem = new ChatNotificationSystem();
