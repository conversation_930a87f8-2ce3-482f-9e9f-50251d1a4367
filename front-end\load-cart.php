<?php
require_once("../functions/server.php");

$user_id = 1;
$cart = mysqli_fetch_assoc(mysqli_query($conn, "SELECT * FROM cart WHERE user_id = $user_id AND status = 'open' LIMIT 1"));
$cart_id = $cart['cart_id'];

$sql = "SELECT ci.id as item_id, t.ticket_type, t.dollar_price_per_package, ci.quantity
        FROM cart_items ci
        JOIN tickets t ON t.ticketid = ci.ticket_id
        WHERE ci.cart_id = $cart_id";

$result = mysqli_query($conn, $sql);

if (mysqli_num_rows($result) > 0) {
    echo "<table class='table table-bordered'>";
    echo "<thead><tr><th>Type</th><th>Qty</th><th>Price</th><th>Action</th></tr></thead><tbody>";

    $total = 0;  // เริ่มต้นคำนวณ total
    while ($row = mysqli_fetch_assoc($result)) {
        // คำนวณ subtotal ของแต่ละรายการ
        $subtotal = $row['dollar_price_per_package'] * $row['quantity'];
        $total += $subtotal;  // บวก subtotal ไปยัง total

        echo "<tr>
                <td>{$row['ticket_type']}</td>
                <td>
                    <input type='number' min='1' value='{$row['quantity']}' 
                           class='form-control qty-input' 
                           data-price='{$row['dollar_price_per_package']}'
                           data-item='{$row['item_id']}'>
                </td>
                <td>
                    $<span class='subtotal' id='subtotal-{$row['item_id']}'>" . number_format($subtotal, 2) . "</span>
                </td>
                <td>
                    <button class='btn btn-success update-qty' data-id='{$row['item_id']}'>Update</button>
                    <button class='btn btn-danger remove-from-cart' data-id='{$row['item_id']}'>Remove</button>
                </td>
              </tr>";
    }
    echo "</tbody></table>";

    // แสดงผลยอดรวมทั้งหมด
    echo "<div class='text-center'><b>Total: $<span id='cart-total'>" . number_format($total, 2) . "</span></b></div>";
} else {
    echo "<p>Your cart is empty.</p>";
}
