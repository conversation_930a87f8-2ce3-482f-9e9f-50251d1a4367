<?php
/**
 * Test Password Fix for Localhost
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Password Fix Test</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Password Issue Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ The Problem:</h4>";
echo "<ul>";
echo "<li><strong>simulate-webhook.php</strong> created user with hashed password: <code>\$2y\$10\$...</code></li>";
echo "<li><strong>payment_temp</strong> stored raw password: <code>871a1a12</code></li>";
echo "<li><strong>payment-success.php</strong> showed raw password: <code>871a1a12</code></li>";
echo "<li><strong>Login failed</strong> because raw password didn't match hash in database</li>";
echo "</ul>";

echo "<h4>✅ The Solution:</h4>";
echo "<ul>";
echo "<li><strong>simulate-webhook.php</strong> now updates user password hash to match displayed password</li>";
echo "<li><strong>Raw password shown</strong> = <code>871a1a12</code></li>";
echo "<li><strong>Database hash</strong> = <code>password_hash('871a1a12')</code></li>";
echo "<li><strong>Login works</strong> because <code>password_verify('871a1a12', hash)</code> returns true</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>How the Fix Works:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Code Changes in simulate-webhook.php:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// Before: User created with initial hashed password\n";
echo "\$hashed_password = password_hash(\$password, PASSWORD_DEFAULT);\n";
echo "// User saved with: \$hashed_password\n\n";
echo "// After: Update password to match what we show\n";
echo "\$correct_password_hash = password_hash(\$password, PASSWORD_DEFAULT);\n";
echo "\$update_password_stmt = \$conn->prepare(\"UPDATE user SET password = ? WHERE username = ?\");\n";
echo "\$update_password_stmt->bind_param(\"ss\", \$correct_password_hash, \$username);";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Test Flow:</h3>";

if ($is_localhost) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Complete a purchase</strong> → Add items to cart, pay</li>";
    echo "<li><strong>Check payment success page</strong> → Should show credentials</li>";
    echo "<li><strong>Note the password</strong> → e.g., <code>871a1a12</code></li>";
    echo "<li><strong>Check database</strong> → User password should be hash of that password</li>";
    echo "<li><strong>Try login</strong> → Use displayed username/password</li>";
    echo "<li><strong>Login should work</strong> → No more 'Wrong password' error</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>This fix is specifically for localhost. Production uses the real webhook which handles passwords correctly.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Password Verification Test:</h3>";

// Test password verification logic
$test_password = "871a1a12";
$test_hash = password_hash($test_password, PASSWORD_DEFAULT);

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 Password Verification Example:</h4>";
echo "<p><strong>Raw Password:</strong> <code>$test_password</code></p>";
echo "<p><strong>Generated Hash:</strong> <code>" . substr($test_hash, 0, 30) . "...</code></p>";

$verification_result = password_verify($test_password, $test_hash);
echo "<p><strong>Verification Result:</strong> " . ($verification_result ? "✅ SUCCESS" : "❌ FAILED") . "</p>";
echo "<p><strong>This is how login will work</strong> after the fix!</p>";
echo "</div>";

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>Payment success page:</strong> Shows actual credentials</li>";
echo "<li>✅ <strong>Database password:</strong> Hash that matches displayed password</li>";
echo "<li>✅ <strong>Login works:</strong> No more password mismatch errors</li>";
echo "<li>✅ <strong>User experience:</strong> Seamless from purchase to login</li>";
echo "</ul>";
?>
