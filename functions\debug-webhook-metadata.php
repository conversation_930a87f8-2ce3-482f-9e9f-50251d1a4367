<?php
include('server.php');

echo "<h2>🔍 Debug Webhook Metadata</h2>";

echo "<h3>📊 **Recent Cart Sessions**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Get recent cart sessions
$query = "SELECT * FROM cart_sessions ORDER BY created_at DESC LIMIT 5";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<h4>Last 5 Cart Sessions:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 6px;'>ID</th>";
    echo "<th style='padding: 6px;'>Session ID</th>";
    echo "<th style='padding: 6px;'>User ID</th>";
    echo "<th style='padding: 6px;'>Created</th>";
    echo "<th style='padding: 6px;'>Processed</th>";
    echo "<th style='padding: 6px;'>Cart Data</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $status_color = $row['processed_at'] ? 'background: #d4edda;' : 'background: #fff3cd;';
        echo "<tr style='$status_color'>";
        echo "<td style='padding: 6px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 6px; font-family: monospace;'>" . htmlspecialchars($row['session_id']) . "</td>";
        echo "<td style='padding: 6px;'>" . ($row['user_id'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 6px;'>" . date('M j H:i', strtotime($row['created_at'])) . "</td>";
        echo "<td style='padding: 6px;'>" . ($row['processed_at'] ? date('M j H:i', strtotime($row['processed_at'])) : 'No') . "</td>";
        echo "<td style='padding: 6px; max-width: 300px; overflow: hidden; text-overflow: ellipsis;'>" . htmlspecialchars(substr($row['cart_data'], 0, 100)) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No cart sessions found.</p>";
}

echo "</div>";

echo "<h3>📋 **Recent Webhook Log Entries**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Read last 50 lines of webhook log
$webhook_log_file = '../front-end/webhook.log';
if (file_exists($webhook_log_file)) {
    $log_lines = file($webhook_log_file);
    $recent_lines = array_slice($log_lines, -50); // Last 50 lines
    
    echo "<h4>Last 50 Webhook Log Entries:</h4>";
    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 11px; max-height: 400px; overflow-y: auto;'>";
    
    foreach ($recent_lines as $line) {
        $line = htmlspecialchars(trim($line));
        
        // Highlight important lines
        $color = '';
        if (strpos($line, 'cart_session_id') !== false) {
            $color = 'background: #d4edda; color: #155724;'; // Green for cart session
        } elseif (strpos($line, 'metadata') !== false) {
            $color = 'background: #d1ecf1; color: #0c5460;'; // Blue for metadata
        } elseif (strpos($line, 'cleanup') !== false) {
            $color = 'background: #fff3cd; color: #856404;'; // Yellow for cleanup
        } elseif (strpos($line, 'Error') !== false || strpos($line, 'error') !== false) {
            $color = 'background: #f8d7da; color: #721c24;'; // Red for errors
        }
        
        echo "<div style='$color padding: 2px; margin: 1px 0;'>$line</div>";
    }
    
    echo "</div>";
} else {
    echo "<p>Webhook log file not found.</p>";
}

echo "</div>";

echo "<h3>🧪 **Test Webhook Metadata Parsing**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

if (isset($_POST['test_metadata'])) {
    $test_metadata_json = $_POST['metadata_json'];
    
    echo "<h4>Testing Metadata Parsing:</h4>";
    echo "<div style='background: #e9ecef; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
    echo "<strong>Input JSON:</strong><br>";
    echo "<pre style='font-size: 11px;'>" . htmlspecialchars($test_metadata_json) . "</pre>";
    echo "</div>";
    
    try {
        $metadata = json_decode($test_metadata_json, true);
        
        if ($metadata === null) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; color: #721c24;'>";
            echo "<strong>❌ JSON Parse Error:</strong> " . json_last_error_msg();
            echo "</div>";
        } else {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; color: #155724;'>";
            echo "<strong>✅ Parsed Successfully:</strong><br>";
            echo "<pre style='font-size: 11px;'>" . print_r($metadata, true) . "</pre>";
            echo "</div>";
            
            // Test cart_session_id extraction
            if (isset($metadata['cart_session_id'])) {
                $cart_session_id = $metadata['cart_session_id'];
                echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 3px; color: #0c5460;'>";
                echo "<strong>🔍 Cart Session ID Found:</strong> " . htmlspecialchars($cart_session_id);
                
                // Check if this cart session exists in database
                $check_query = "SELECT * FROM cart_sessions WHERE session_id = ?";
                $check_stmt = $conn->prepare($check_query);
                $check_stmt->bind_param("s", $cart_session_id);
                $check_stmt->execute();
                $check_result = $check_stmt->get_result();
                
                if ($check_result->num_rows > 0) {
                    $cart_session = $check_result->fetch_assoc();
                    echo "<br><strong>✅ Cart Session Found in Database:</strong>";
                    echo "<br>ID: " . $cart_session['id'];
                    echo "<br>User ID: " . ($cart_session['user_id'] ?? 'NULL');
                    echo "<br>Created: " . $cart_session['created_at'];
                    echo "<br>Processed: " . ($cart_session['processed_at'] ?? 'Not processed');
                } else {
                    echo "<br><strong>❌ Cart Session NOT Found in Database</strong>";
                }
                echo "</div>";
            } else {
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; color: #856404;'>";
                echo "<strong>⚠️ No cart_session_id found in metadata</strong>";
                echo "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; color: #721c24;'>";
        echo "<strong>❌ Error:</strong> " . $e->getMessage();
        echo "</div>";
    }
}

echo "<h4>Test Metadata Parsing:</h4>";
echo "<form method='POST'>";
echo "<p><strong>Paste Stripe metadata JSON here:</strong></p>";
echo "<textarea name='metadata_json' rows='10' cols='80' placeholder='{\"user_id\":\"guest\",\"username\":\"\",\"total_items\":1,\"cart_session_id\":\"cart_683b657e37dca_1747723070\",\"total_amount\":29.5,\"save_payment_method\":\"1\"}'></textarea>";
echo "<br><br>";
echo "<input type='hidden' name='test_metadata' value='1'>";
echo "<button type='submit' style='background: #473BF0; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Test Parse Metadata</button>";
echo "</form>";

echo "</div>";

echo "<h3>🔧 **Manual Cart Session Cleanup Test**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

if (isset($_POST['manual_cleanup'])) {
    $cleanup_session_id = $_POST['cleanup_session_id'];
    
    if ($cleanup_session_id) {
        echo "<h4>Testing Manual Cleanup:</h4>";
        
        // Check if session exists before cleanup
        $check_query = "SELECT * FROM cart_sessions WHERE session_id = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("s", $cleanup_session_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $session_data = $check_result->fetch_assoc();
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 3px; color: #0c5460; margin: 10px 0;'>";
            echo "<strong>📋 Session Found Before Cleanup:</strong>";
            echo "<br>ID: " . $session_data['id'];
            echo "<br>Session ID: " . $session_data['session_id'];
            echo "<br>User ID: " . ($session_data['user_id'] ?? 'NULL');
            echo "<br>Created: " . $session_data['created_at'];
            echo "<br>Processed: " . ($session_data['processed_at'] ?? 'Not processed');
            echo "</div>";
            
            // Perform cleanup
            $cleanup_query = "DELETE FROM cart_sessions WHERE session_id = ?";
            $cleanup_stmt = $conn->prepare($cleanup_query);
            $cleanup_stmt->bind_param("s", $cleanup_session_id);
            $cleanup_stmt->execute();
            $deleted_count = $cleanup_stmt->affected_rows;
            
            if ($deleted_count > 0) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; color: #155724; margin: 10px 0;'>";
                echo "<strong>✅ Cleanup Successful:</strong> Deleted $deleted_count cart session(s)";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; color: #721c24; margin: 10px 0;'>";
                echo "<strong>❌ Cleanup Failed:</strong> No sessions were deleted";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 3px; color: #856404; margin: 10px 0;'>";
            echo "<strong>⚠️ Session Not Found:</strong> No cart session with ID '$cleanup_session_id' exists";
            echo "</div>";
        }
    }
}

echo "<h4>Manual Cleanup Test:</h4>";
echo "<form method='POST'>";
echo "<p><strong>Enter cart session ID to test cleanup:</strong></p>";
echo "<input type='text' name='cleanup_session_id' placeholder='cart_683b657e37dca_1747723070' style='width: 300px; padding: 8px;'>";
echo "<br><br>";
echo "<input type='hidden' name='manual_cleanup' value='1'>";
echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px;' onclick='return confirm(\"Are you sure you want to delete this cart session?\")'>Test Cleanup</button>";
echo "</form>";

echo "</div>";

echo "<h3>💡 **Debugging Tips**</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Common Issues & Solutions:</h4>";
echo "<ul>";
echo "<li><strong>Empty Metadata:</strong> Check if Stripe session is created with correct metadata</li>";
echo "<li><strong>Missing cart_session_id:</strong> Verify create-checkout-session.php includes cart_session_id in metadata</li>";
echo "<li><strong>Session Not Found:</strong> Check if cart session was created before webhook processing</li>";
echo "<li><strong>Cleanup Not Working:</strong> Verify webhook has access to correct cart_session_id</li>";
echo "<li><strong>Guest User Issues:</strong> Ensure guest users have cart sessions created properly</li>";
echo "</ul>";

echo "<h4>Next Steps:</h4>";
echo "<ol>";
echo "<li>Make a test purchase and check if cart_session_id appears in webhook logs</li>";
echo "<li>Use the metadata parser above to test actual webhook metadata</li>";
echo "<li>Check if cart sessions are being created with correct session_id format</li>";
echo "<li>Verify webhook cleanup logic is using the correct cart_session_id</li>";
echo "</ol>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
input[type="text"], textarea { border: 1px solid #ddd; border-radius: 4px; padding: 6px; }
pre { background: #f8f9fa; padding: 8px; border-radius: 3px; overflow-x: auto; }
</style>
