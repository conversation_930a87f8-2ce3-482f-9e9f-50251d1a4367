<?php
// Debug script to test payment success functionality
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

$session_id = $_GET['session_id'] ?? '';

echo "<h1>Payment Debug Information</h1>";
echo "<p><strong>Session ID:</strong> " . htmlspecialchars($session_id) . "</p>";

if (!$session_id) {
    echo "<p style='color: red;'>No session ID provided</p>";
    exit;
}

// Check payment_temp table
echo "<h2>1. Checking payment_temp table</h2>";
$stmt = $conn->prepare("SELECT username, email, password FROM payment_temp WHERE session_id = ?");
$stmt->bind_param("s", $session_id);
$stmt->execute();
$stmt->store_result();

if ($stmt->num_rows > 0) {
    echo "<p style='color: green;'>✓ Found data in payment_temp table</p>";
    $stmt->bind_result($username, $email, $password);
    $stmt->fetch();
    echo "<p>Username: " . htmlspecialchars($username) . "</p>";
    echo "<p>Email: " . htmlspecialchars($email) . "</p>";
} else {
    echo "<p style='color: orange;'>⚠ No data found in payment_temp table</p>";
}

// Test Stripe session retrieval
echo "<h2>2. Testing Stripe Session Retrieval</h2>";
try {
    $session = \Stripe\Checkout\Session::retrieve($session_id);
    echo "<p style='color: green;'>✓ Successfully retrieved Stripe session</p>";
    echo "<p><strong>Payment Status:</strong> " . $session->payment_status . "</p>";
    echo "<p><strong>Customer ID:</strong> " . ($session->customer ?? 'None') . "</p>";

    if ($session->customer_details) {
        echo "<p><strong>Customer Email:</strong> " . ($session->customer_details->email ?? 'None') . "</p>";
        echo "<p><strong>Customer Name:</strong> " . ($session->customer_details->name ?? 'None') . "</p>";
    }

    // Show metadata
    if ($session->metadata) {
        echo "<h3>Session Metadata:</h3>";
        foreach ($session->metadata as $key => $value) {
            echo "<p><strong>$key:</strong> $value</p>";
        }
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error retrieving Stripe session: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Test database connection
echo "<h2>3. Testing Database Connection</h2>";
if ($conn) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";

    // Test user table
    $test_query = "SELECT COUNT(*) as count FROM user";
    $result = $conn->query($test_query);
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p>Users table has " . $row['count'] . " records</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
}

echo "<h2>4. Recommendations</h2>";
if ($stmt->num_rows == 0) {
    echo "<p>Since no data was found in payment_temp, the webhook likely hasn't processed this payment yet.</p>";
    echo "<p>The fallback mechanism should create the user account directly.</p>";
}

?>
