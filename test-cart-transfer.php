<?php
session_start();
include('functions/server.php');
include('functions/cart-transfer.php');

echo "<h2>🧪 Cart Transfer Test</h2>";

// Test 1: Check current session state
echo "<h3>1. Current Session State</h3>";
echo "<p><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Not logged in') . "</p>";
echo "<p><strong>Username:</strong> " . (isset($_SESSION['username']) ? $_SESSION['username'] : 'Not set') . "</p>";
echo "<p><strong>Guest Cart Items:</strong> " . (hasGuestCartItems() ? getGuestCartItemsCount() . ' items' : 'Empty') . "</p>";

if (hasGuestCartItems()) {
    echo "<p><strong>Guest Cart Total:</strong> $" . number_format(getGuestCartTotal(), 2) . "</p>";
    echo "<h4>Guest Cart Contents:</h4>";
    echo "<ul>";
    foreach ($_SESSION['guest_cart'] as $item) {
        echo "<li>" . htmlspecialchars($item['ticket_type']) . " - Qty: " . $item['quantity'] . " - $" . number_format($item['dollar_price_per_package'], 2) . "</li>";
    }
    echo "</ul>";
}

// Test 2: Add test items to guest cart
if (isset($_GET['action']) && $_GET['action'] === 'add_test_items') {
    $_SESSION['guest_cart'] = [
        [
            'ticket_id' => 1,
            'ticket_type' => 'STARTER',
            'package_size' => 'XS',
            'quantity' => 2,
            'dollar_price_per_package' => 20.00,
            'cart_item_id' => 'guest_' . time() . '_1001'
        ],
        [
            'ticket_id' => 2,
            'ticket_type' => 'BUSINESS',
            'package_size' => 'S',
            'quantity' => 1,
            'dollar_price_per_package' => 35.00,
            'cart_item_id' => 'guest_' . time() . '_1002'
        ]
    ];
    
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
    echo "<h4>✅ Test Guest Cart Created</h4>";
    echo "<p>Added 2 test items to guest cart</p>";
    echo "</div>";
}

// Test 3: Clear guest cart
if (isset($_GET['action']) && $_GET['action'] === 'clear_guest_cart') {
    if (isset($_SESSION['guest_cart'])) {
        $count = count($_SESSION['guest_cart']);
        unset($_SESSION['guest_cart']);
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
        echo "<h4>✅ Guest Cart Cleared</h4>";
        echo "<p>Removed $count items from guest cart</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404; margin: 10px 0;'>";
        echo "<h4>⚠️ No Guest Cart to Clear</h4>";
        echo "</div>";
    }
}

// Test 4: Test cart transfer (only if logged in)
if (isset($_GET['action']) && $_GET['action'] === 'test_transfer') {
    if (isset($_SESSION['user_id'])) {
        $result = transferGuestCartToUser($_SESSION['user_id'], $conn);
        
        if ($result['success']) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
            echo "<h4>✅ Cart Transfer Successful</h4>";
            echo "<p>" . htmlspecialchars($result['message']) . "</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24; margin: 10px 0;'>";
            echo "<h4>❌ Cart Transfer Failed</h4>";
            echo "<p>" . htmlspecialchars($result['message']) . "</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404; margin: 10px 0;'>";
        echo "<h4>⚠️ Login Required</h4>";
        echo "<p>You must be logged in to test cart transfer</p>";
        echo "</div>";
    }
}

// Test Actions
echo "<h3>2. Test Actions</h3>";
echo "<div style='margin: 10px 0;'>";
echo "<a href='?action=add_test_items' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Add Test Items to Guest Cart</a>";
echo "<a href='?action=clear_guest_cart' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Clear Guest Cart</a>";
echo "<a href='?action=test_transfer' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Test Cart Transfer</a>";
echo "</div>";

// Navigation
echo "<h3>3. Navigation</h3>";
echo "<div style='margin: 10px 0;'>";
echo "<a href='front-end/cart.php' style='background: #6754e2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Go to Cart</a>";
echo "<a href='front-end/sign-in.php' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>Sign In</a>";
echo "<a href='index.php?logout=1' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>Logout</a>";
echo "</div>";

echo "<h3>4. Instructions</h3>";
echo "<ol>";
echo "<li>First, add test items to guest cart</li>";
echo "<li>Go to cart page - you should see 'Already have account? Sign in' message</li>";
echo "<li>Click sign in and login with your credentials</li>";
echo "<li><strong>Test wrong password:</strong> Try entering wrong password first - you should stay on sign-in page with cart transfer parameters preserved</li>";
echo "<li>Enter correct credentials - you should be redirected back to cart with transfer success message</li>";
echo "<li>Your guest cart items should now be in your user cart</li>";
echo "</ol>";

echo "<h3>5. Test Scenarios</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Scenario A: Wrong Password Test</h4>";
echo "<ol>";
echo "<li>Add test items to guest cart</li>";
echo "<li>Go to cart and click 'Sign In'</li>";
echo "<li>Enter correct username but wrong password</li>";
echo "<li>Submit form - should return to sign-in page with cart transfer message still showing</li>";
echo "<li>Enter correct password - should successfully transfer cart</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>Scenario B: Empty Fields Test</h4>";
echo "<ol>";
echo "<li>Add test items to guest cart</li>";
echo "<li>Go to cart and click 'Sign In'</li>";
echo "<li>Leave username or password empty</li>";
echo "<li>Submit form - should return to sign-in page with validation error and cart transfer preserved</li>";
echo "<li>Fill in correct credentials - should successfully transfer cart</li>";
echo "</ol>";
echo "</div>";
?>
