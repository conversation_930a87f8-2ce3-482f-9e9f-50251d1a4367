<?php
/**
 * Test Correct Purchase Processing Fix
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Correct Purchase Processing Fix</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Issues Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ Previous Problems:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Wrong ticket types:</strong> All purchases treated as STARTER XS</li>";
echo "<li>✅ <strong>Incorrect ticket counts:</strong> Getting 30 tickets instead of 15</li>";
echo "<li>✅ <strong>Wrong purchase history:</strong> Showing duplicate/wrong items</li>";
echo "<li>✅ <strong>Payment method not saved:</strong> Save checkbox ignored</li>";
echo "<li>✅ <strong>Data source issue:</strong> Using Stripe product metadata instead of cart data</li>";
echo "</ul>";

echo "<h4>✅ Complete Solution Applied:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Use cart_sessions data:</strong> Get actual purchased items from database</li>";
echo "<li>✅ <strong>Correct ticket processing:</strong> Each item processed with correct type/size</li>";
echo "<li>✅ <strong>Accurate ticket counts:</strong> Proper calculation per item</li>";
echo "<li>✅ <strong>Correct purchase history:</strong> Save actual purchased items</li>";
echo "<li>✅ <strong>Payment method saving:</strong> Handle save_payment_method flag</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>How the Fix Works:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Data Flow (Fixed):</h4>";
echo "<ol>";
echo "<li><strong>Cart Creation:</strong> Items stored in cart_sessions table with cart_session_id</li>";
echo "<li><strong>Checkout Session:</strong> cart_session_id stored in Stripe metadata</li>";
echo "<li><strong>Payment Success:</strong> Retrieve cart_session_id from metadata</li>";
echo "<li><strong>Get Cart Data:</strong> Query cart_sessions table for actual items</li>";
echo "<li><strong>Process Each Item:</strong> Use real ticket_type, package_size, numbers_per_package</li>";
echo "<li><strong>Update Tickets:</strong> Add correct amounts to correct ticket columns</li>";
echo "<li><strong>Save History:</strong> Record actual purchased items</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Example Purchase Processing:</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📝 Example: STARTER XS (5) + BUSINESS S (10) + ULTIMATE XS (5)</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "Cart Data Retrieved:\n";
echo "[\n";
echo "  {\n";
echo "    \"ticket_type\": \"STARTER\",\n";
echo "    \"package_size\": \"XS\",\n";
echo "    \"numbers_per_package\": 5,\n";
echo "    \"quantity\": 1\n";
echo "  },\n";
echo "  {\n";
echo "    \"ticket_type\": \"BUSINESS\",\n";
echo "    \"package_size\": \"S\",\n";
echo "    \"numbers_per_package\": 10,\n";
echo "    \"quantity\": 1\n";
echo "  },\n";
echo "  {\n";
echo "    \"ticket_type\": \"ULTIMATE\",\n";
echo "    \"package_size\": \"XS\",\n";
echo "    \"numbers_per_package\": 5,\n";
echo "    \"quantity\": 1\n";
echo "  }\n";
echo "]\n\n";
echo "Processing Results:\n";
echo "- STARTER XS: +5 starter_tickets\n";
echo "- BUSINESS S: +10 premium_tickets\n";
echo "- ULTIMATE XS: +5 ultimate_tickets\n\n";
echo "Final Ticket Counts:\n";
echo "- starter_tickets: 5\n";
echo "- premium_tickets: 10\n";
echo "- ultimate_tickets: 5\n\n";
echo "Purchase History: 3 separate records";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Payment Method Saving:</h3>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>💳 How Payment Method Saving Works:</h4>";
echo "<ol>";
echo "<li><strong>Checkbox checked:</strong> save_payment_method = '1' in metadata</li>";
echo "<li><strong>Create Stripe customer:</strong> Link user to Stripe</li>";
echo "<li><strong>Get payment method:</strong> From payment intent</li>";
echo "<li><strong>Attach to customer:</strong> Save for future use</li>";
echo "<li><strong>Result:</strong> Payment method available for future purchases</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Database Tables Updated:</h3>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📊 What Gets Updated Correctly:</h4>";
echo "<ul>";
echo "<li><strong>user table:</strong>";
echo "<ul>";
echo "<li>starter_tickets, premium_tickets, ultimate_tickets (correct amounts)</li>";
echo "<li>stripe_customer_id (for payment methods)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>purchasetickets table:</strong>";
echo "<ul>";
echo "<li>Separate record for each purchased item</li>";
echo "<li>Correct ticket_type, package_size, numbers_per_package</li>";
echo "<li>Accurate dollar_price_per_package per item</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>cart_sessions table:</strong>";
echo "<ul>";
echo "<li>Contains the source of truth for purchased items</li>";
echo "</ul>";
echo "</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

if ($is_localhost) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test Mixed Purchase on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Add different items:</strong> STARTER XS + BUSINESS S + ULTIMATE XS</li>";
    echo "<li><strong>Check save payment method</strong> checkbox</li>";
    echo "<li><strong>Complete purchase</strong> → Should process correctly</li>";
    echo "<li><strong>Check user tickets:</strong>";
    echo "<ul>";
    echo "<li>starter_tickets: 5 (not 30)</li>";
    echo "<li>premium_tickets: 10 (not 0)</li>";
    echo "<li>ultimate_tickets: 5 (not 0)</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Check purchase history:</strong> Should show 3 separate items</li>";
    echo "<li><strong>Check Stripe customer:</strong> Should have saved payment method</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>Same logic applies to production webhook processing.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>Correct ticket types:</strong> STARTER → starter_tickets, BUSINESS → premium_tickets, ULTIMATE → ultimate_tickets</li>";
echo "<li>✅ <strong>Accurate ticket counts:</strong> Exactly what was purchased</li>";
echo "<li>✅ <strong>Proper purchase history:</strong> Separate record for each item</li>";
echo "<li>✅ <strong>Payment method saved:</strong> If checkbox was checked</li>";
echo "<li>✅ <strong>No more wrong data:</strong> Uses actual cart data, not Stripe product metadata</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Summary:</h3>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Purchase processing is now completely accurate!</strong></p>";
echo "<ul>";
echo "<li><strong>Root cause:</strong> Using wrong data source (Stripe products vs cart data)</li>";
echo "<li><strong>Solution:</strong> Use cart_sessions table data for accurate processing</li>";
echo "<li><strong>Result:</strong> Correct tickets, accurate history, working payment method saving</li>";
echo "</ul>";
echo "<p><strong>Your purchases should now work exactly as expected with correct ticket types and counts!</strong></p>";
echo "</div>";
?>
