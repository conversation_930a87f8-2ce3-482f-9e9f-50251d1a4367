<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Fix Test - HelloIT</title>
    <!-- Bootstrap 4 CSS (same as sign-in page) -->
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
        padding: 40px;
    }

    .test-container {
        max-width: 600px;
        margin: 0 auto;
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }

    .status-success { background-color: #28a745; }
    .status-danger { background-color: #dc3545; }

    .test-section {
        margin: 20px 0;
        padding: 15px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
    }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Reset Password Modal Fix Test</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle mr-2"></i>Issue Fixed</h5>
            <p>The reset password modal was not working because it was using Bootstrap 5 syntax while the sign-in page uses Bootstrap 4. This has been corrected.</p>
        </div>

        <div class="test-section">
            <h4>Bootstrap Version Check</h4>
            <p><span class="status-indicator status-success"></span><strong>Bootstrap 4:</strong> Using correct syntax for data attributes and modal methods</p>
            <p><span class="status-indicator status-success"></span><strong>jQuery:</strong> Using jQuery modal methods instead of vanilla JS</p>
            <p><span class="status-indicator status-success"></span><strong>CSS Classes:</strong> Using Bootstrap 4 spacing classes (mr-2 instead of me-2)</p>
        </div>

        <div class="test-section">
            <h4>Changes Made</h4>
            <ul>
                <li><strong>Modal Trigger:</strong> Changed <code>data-bs-toggle</code> to <code>data-toggle</code></li>
                <li><strong>Modal Target:</strong> Changed <code>data-bs-target</code> to <code>data-target</code></li>
                <li><strong>Close Button:</strong> Changed <code>btn-close</code> to <code>close</code> with <code>&times;</code></li>
                <li><strong>Modal Dismiss:</strong> Changed <code>data-bs-dismiss</code> to <code>data-dismiss</code></li>
                <li><strong>JavaScript:</strong> Changed <code>bootstrap.Modal.getInstance()</code> to <code>$('#modal').modal('hide')</code></li>
                <li><strong>CSS Classes:</strong> Changed <code>me-2</code> to <code>mr-2</code> for Bootstrap 4 compatibility</li>
            </ul>
        </div>

        <div class="test-section">
            <h4>Test the Modal</h4>
            <p>Click the button below to test if the modal opens correctly:</p>
            
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#testModal">
                <i class="fas fa-key mr-2"></i>Test Reset Password Modal
            </button>
        </div>

        <div class="test-section">
            <h4>Verification Steps</h4>
            <ol>
                <li><strong>Modal Opens:</strong> Click the test button above - modal should open</li>
                <li><strong>Form Fields:</strong> All form fields should be visible and functional</li>
                <li><strong>Password Toggle:</strong> Eye icon should show/hide password</li>
                <li><strong>Validation:</strong> Try submitting with empty fields</li>
                <li><strong>Close Modal:</strong> Cancel button and X button should close modal</li>
            </ol>
        </div>

        <div class="text-center mt-4">
            <a href="sign-in.php" class="btn btn-success btn-lg">
                <i class="fas fa-sign-in-alt mr-2"></i>Test on Actual Sign-In Page
            </a>
        </div>
    </div>

    <!-- Test Modal (same structure as reset password modal) -->
    <div class="modal fade" id="testModal" tabindex="-1" role="dialog" aria-labelledby="testModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="testModalLabel">
                        <i class="fas fa-key mr-2"></i>Reset Password (Test)
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle mr-2"></i>
                        <strong>Modal Working!</strong> The Bootstrap 4 modal is functioning correctly.
                    </div>
                    <form id="testResetForm">
                        <div class="form-group">
                            <label for="testEmail">Email Address</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="Enter your email address">
                            <small class="form-text text-muted">Enter the email address associated with your account.</small>
                        </div>
                        <div class="form-group">
                            <label for="testNewPassword">New Password</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="testNewPassword" placeholder="Enter new password">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary" type="button" id="toggleTestPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="form-text text-muted">Password must be at least 6 characters long.</small>
                        </div>
                        <div class="form-group">
                            <label for="testConfirmPassword">Confirm New Password</label>
                            <input type="password" class="form-control" id="testConfirmPassword" placeholder="Confirm new password">
                        </div>
                        <div id="testMessage" class="alert" style="display: none;"></div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="testReset()">
                        <i class="fas fa-sync-alt mr-2"></i>Test Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery (required for Bootstrap 4) -->
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>
    <!-- Bootstrap 4 JS -->
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

    <script>
    // Test password toggle
    $('#toggleTestPassword').click(function() {
        var passwordField = $('#testNewPassword');
        var icon = $(this).find('i');
        
        if (passwordField.attr('type') === 'password') {
            passwordField.attr('type', 'text');
            icon.removeClass('fa-eye').addClass('fa-eye-slash');
        } else {
            passwordField.attr('type', 'password');
            icon.removeClass('fa-eye-slash').addClass('fa-eye');
        }
    });

    function testReset() {
        var email = $('#testEmail').val();
        var password = $('#testNewPassword').val();
        var confirm = $('#testConfirmPassword').val();
        
        if (!email || !password || !confirm) {
            showTestMessage('Please fill in all fields.', 'danger');
            return;
        }
        
        if (password !== confirm) {
            showTestMessage('Passwords do not match.', 'danger');
            return;
        }
        
        if (password.length < 6) {
            showTestMessage('Password must be at least 6 characters long.', 'danger');
            return;
        }
        
        showTestMessage('Test successful! All validation passed.', 'success');
        
        // Auto-close modal after 2 seconds
        setTimeout(function() {
            $('#testModal').modal('hide');
        }, 2000);
    }

    function showTestMessage(message, type) {
        var messageDiv = $('#testMessage');
        messageDiv.removeClass().addClass('alert alert-' + type);
        messageDiv.html(message);
        messageDiv.show();
    }

    // Show success message when page loads
    $(document).ready(function() {
        console.log('Bootstrap 4 and jQuery loaded successfully');
        console.log('Modal functionality should now work correctly');
    });
    </script>
</body>
</html>
