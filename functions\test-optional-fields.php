<?php
include('server.php');

echo "<h2>✅ Profile Fields Made Optional</h2>";

echo "<h3>🎯 **Changes Made**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>Fields Changed from Required to Optional:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Last Name</strong> - Now optional</li>";
echo "<li>✅ <strong>Phone</strong> - Now optional</li>";
echo "<li>✅ <strong>District</strong> - Now optional</li>";
echo "<li>✅ <strong>Postal Code</strong> - Now optional</li>";
echo "</ul>";

echo "<h4>What was updated:</h4>";
echo "<ul>";
echo "<li>✅ Removed <code>required</code> attribute from HTML fields</li>";
echo "<li>✅ Added '(Optional)' labels to field names</li>";
echo "<li>✅ Updated JavaScript validation to make fields optional</li>";
echo "<li>✅ Improved validation messages</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 **Field Status Summary**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Field</th>";
echo "<th style='padding: 10px;'>Status</th>";
echo "<th style='padding: 10px;'>Validation</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Username</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>Required</td>";
echo "<td style='padding: 10px;'>Format + Uniqueness</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>First Name</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>Required</td>";
echo "<td style='padding: 10px;'>Basic validation</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Last Name</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Email</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>Required</td>";
echo "<td style='padding: 10px;'>Format + Uniqueness</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Phone</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>10 digits (if provided)</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Company Name</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Tax ID</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>Numbers only (if provided)</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Address</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Address Notes</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>District</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>City</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>Required</td>";
echo "<td style='padding: 10px;'>Basic validation</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>State/Province</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>None</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Country</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>Required</td>";
echo "<td style='padding: 10px;'>Basic validation</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Postal Code</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>Optional</td>";
echo "<td style='padding: 10px;'>Numbers only (if provided)</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h3>🔧 **Code Changes Made**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>HTML Changes:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Required):
<label for="last_name" class="form-label">Last Name</label>
<input type="text" class="form-control" id="last_name" name="last_name" required>

<label for="tell" class="form-label">Phone</label>
<input type="text" class="form-control" id="tell" name="tell" required>

<label for="district" class="form-label">District</label>
<input type="text" class="form-control" id="district" name="district" required>

<label for="postal_code" class="form-label">Postal Code</label>
<input type="text" class="form-control" id="postal_code" name="postal_code" required>

// AFTER (Optional):
<label for="last_name" class="form-label">Last Name (Optional)</label>
<input type="text" class="form-control" id="last_name" name="last_name">

<label for="tell" class="form-label">Phone (Optional)</label>
<input type="text" class="form-control" id="tell" name="tell">

<label for="district" class="form-label">District (Optional)</label>
<input type="text" class="form-control" id="district" name="district">

<label for="postal_code" class="form-label">Postal Code (Optional)</label>
<input type="text" class="form-control" id="postal_code" name="postal_code">
');
echo "</pre>";

echo "<h4>JavaScript Validation Changes:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Always validated):
if (digitsOnly.length !== 10) {
    showAlert(\'Phone number must be exactly 10 digits.\');
    return false;
}

if (!/^\d+$/.test(postalCode)) {
    showAlert(\'Postal Code must contain only numbers.\');
    return false;
}

// AFTER (Only validated if provided):
if (phoneNumber.length !== 0) {
    const digitsOnly = phoneNumber.replace(/\D/g, \'\');
    if (digitsOnly.length !== 10) {
        showAlert(\'Phone number must be exactly 10 digits (if provided).\');
        return false;
    }
}

if (postalCode.length !== 0 && !/^\d+$/.test(postalCode)) {
    showAlert(\'Postal Code must contain only numbers (if provided).\');
    return false;
}
');
echo "</pre>";
echo "</div>";

echo "<h3>🎯 **User Experience Improvements**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>Before (Forced Data Entry):</h4>";
echo "<ul>";
echo "<li>❌ Users had to fill in Last Name even if they preferred not to</li>";
echo "<li>❌ Phone number was mandatory even for users who didn't want to share it</li>";
echo "<li>❌ District was required even if not applicable to user's location</li>";
echo "<li>❌ Postal code was mandatory even if user didn't know it</li>";
echo "<li>❌ Form validation would block submission if any of these were empty</li>";
echo "</ul>";

echo "<h4>After (User-Friendly):</h4>";
echo "<ul>";
echo "<li>✅ Users can leave Last Name empty if they prefer</li>";
echo "<li>✅ Phone number is optional - users can choose to provide it or not</li>";
echo "<li>✅ District is optional - not all locations have districts</li>";
echo "<li>✅ Postal code is optional - users can skip if unknown</li>";
echo "<li>✅ Form validation only checks format if data is provided</li>";
echo "<li>✅ Clear labeling shows which fields are optional</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🧪 **Testing Instructions**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Test Optional Fields:</h4>";
echo "<ol>";
echo "<li><strong>Log in</strong> to your account</li>";
echo "<li><strong>Go to Profile page</strong></li>";
echo "<li><strong>Click 'Edit Profile'</strong> button</li>";
echo "<li><strong>Test Cases:</strong>";
echo "<ul>";
echo "<li><strong>Leave Last Name empty:</strong> Should save successfully</li>";
echo "<li><strong>Leave Phone empty:</strong> Should save successfully</li>";
echo "<li><strong>Leave District empty:</strong> Should save successfully</li>";
echo "<li><strong>Leave Postal Code empty:</strong> Should save successfully</li>";
echo "<li><strong>Provide invalid phone:</strong> Should show validation error</li>";
echo "<li><strong>Provide invalid postal code:</strong> Should show validation error</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Verify:</strong> Profile saves successfully with minimal required data</li>";
echo "</ol>";

echo "<h4>Minimum Required Fields:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Username</strong> (must be unique)</li>";
echo "<li>✅ <strong>First Name</strong></li>";
echo "<li>✅ <strong>Email</strong> (must be unique)</li>";
echo "<li>✅ <strong>City</strong></li>";
echo "<li>✅ <strong>Country</strong></li>";
echo "</ul>";
echo "</div>";

echo "<h3>🎉 **Expected Results**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #bee5eb;'>";
echo "<h4 style='color: #0c5460;'>✅ Success Indicators:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li>✅ <strong>Form Labels:</strong> Show '(Optional)' for non-required fields</li>";
echo "<li>✅ <strong>Form Submission:</strong> Works with minimal data</li>";
echo "<li>✅ <strong>Validation:</strong> Only validates format if data is provided</li>";
echo "<li>✅ <strong>User Freedom:</strong> Users can skip non-essential information</li>";
echo "<li>✅ <strong>Better UX:</strong> Faster profile completion</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 **Quick Test Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/profile.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Profile Page</a>";
echo "<a href='../front-end/sign-in.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Login Page</a>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🚀 Optional Fields Update Complete!</h4>";
echo "<p style='color: #155724; margin: 0;'>Users now have more flexibility when editing their profiles. Last Name, Phone, District, and Postal Code are all optional fields, making the profile editing process more user-friendly and less intrusive. The form will only validate the format of these fields if the user chooses to provide the information.</p>";
echo "</div>";

// Show current user info if logged in
if (isset($_SESSION['username'])) {
    echo "<h3>🔍 **Current User Profile**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $username = $_SESSION['username'];
    $query = "SELECT * FROM user WHERE username = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<p><strong>Username:</strong> " . htmlspecialchars($user['username']) . "</p>";
        echo "<p><strong>First Name:</strong> " . htmlspecialchars($user['first_name']) . " <span style='color: #dc3545;'>(Required)</span></p>";
        echo "<p><strong>Last Name:</strong> " . (empty($user['last_name']) ? '<em>Not provided</em>' : htmlspecialchars($user['last_name'])) . " <span style='color: #28a745;'>(Optional)</span></p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . " <span style='color: #dc3545;'>(Required)</span></p>";
        echo "<p><strong>Phone:</strong> " . (empty($user['tell']) ? '<em>Not provided</em>' : htmlspecialchars($user['tell'])) . " <span style='color: #28a745;'>(Optional)</span></p>";
        echo "<p><strong>District:</strong> " . (empty($user['district']) ? '<em>Not provided</em>' : htmlspecialchars($user['district'])) . " <span style='color: #28a745;'>(Optional)</span></p>";
        echo "<p><strong>City:</strong> " . htmlspecialchars($user['city']) . " <span style='color: #dc3545;'>(Required)</span></p>";
        echo "<p><strong>Country:</strong> " . htmlspecialchars($user['country']) . " <span style='color: #dc3545;'>(Required)</span></p>";
        echo "<p><strong>Postal Code:</strong> " . (empty($user['postal_code']) ? '<em>Not provided</em>' : htmlspecialchars($user['postal_code'])) . " <span style='color: #28a745;'>(Optional)</span></p>";
        echo "<p style='color: #28a745;'>✅ You can now edit your profile with more flexibility!</p>";
    } else {
        echo "<p style='color: #dc3545;'>❌ User not found in database</p>";
    }
    
    $stmt->close();
} else {
    echo "<h3>🔍 **User Status**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p style='color: #6c757d;'>Not logged in. Please log in to test the profile editing feature.</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
