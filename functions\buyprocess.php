<?php
session_start();
include('../functions/server.php');

if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: index.php');
    exit();
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Retrieve values from POST request
    $tickettype = $_POST["tickettype"];
    $packagesize = $_POST["packagesize"];

    buyprocess($packagesize, $tickettype);

    echo "Data updated successfully: Package Size = $packagesize, Ticket Type = $tickettype";
}

function buyprocess($packagesize, $tickettype)
{
    include('../functions/server.php');
    if (!isset($_SESSION['username'])) {
        header('location: sign-up.php');
        exit();
    } else {
        // Sanitize inputs to prevent SQL injection
        $packagesize = mysqli_real_escape_string($conn, $packagesize);
        $tickettype = mysqli_real_escape_string($conn, $tickettype);


        $username = $_SESSION['username'];
        $sqlpackage = "SELECT ticketid, ticket_type, package_size, numbers_per_package, dollar_price_per_package FROM tickets WHERE package_size = '$packagesize' AND ticket_type = '$tickettype'";
        $resultshow = mysqli_query($conn, $sqlpackage);

        if ($resultshow) {
            if (mysqli_num_rows($resultshow) > 0) {
                $ticket = mysqli_fetch_assoc($resultshow);

                // Create a new entry to keep details
                $ticket_type = $ticket['ticket_type'];
                $package_size = $ticket['package_size'];
                $numbers_per_package = $ticket['numbers_per_package'];
                $dollar_price_per_package = $ticket['dollar_price_per_package'];
                $purchase_time_utc = getCurrentUTC(); // Get UTC time for consistent storage

                $sqlInsert = "INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, remaining_tickets)
                              VALUES ('$username', '$ticket_type','$package_size','$numbers_per_package','$dollar_price_per_package', '$purchase_time_utc', '$numbers_per_package')";

                if (mysqli_query($conn, $sqlInsert)) {
                    // Update number of tickets in the user's table
                    /*  if ($ticket_type == 'STARTER') {
                        $sqlUserTickets = "SELECT starter_tickets FROM user WHERE username = '$username'";
                    } else {
                        $sqlUserTickets = "SELECT premium_tickets FROM user WHERE username = '$username'";
                    }
                    $resultUserTickets = mysqli_query($conn, $sqlUserTickets);

                    if ($resultUserTickets) {
                        // Fetch the current number of tickets
                        $userTickets = mysqli_fetch_assoc($resultUserTickets);

                        // Update the ticket count based on the ticket type
                        if ($ticket_type == 'STARTER') {
                            $new_ticket_count = $userTickets['starter_tickets'] + $numbers_per_package;
                            $sqlUpdateUserTickets = "UPDATE user SET starter_tickets = '$new_ticket_count' WHERE username = '$username'";
                        } else {
                            $new_ticket_count = $userTickets['premium_tickets'] + $numbers_per_package;
                            $sqlUpdateUserTickets = "UPDATE user SET premium_tickets = '$new_ticket_count' WHERE username = '$username'";
                        }

                        if (mysqli_query($conn, $sqlUpdateUserTickets)) {
                            header('location: index.php');
                            exit();
                        } else {
                            echo "Error updating user ticket count: " . mysqli_error($conn);
                        }
                    } else {
                        echo "Error fetching user tickets: " . mysqli_error($conn);
                    } */
                    //
                    header('location: index.php');
                    exit();
                } else {
                    echo "Error: " . $sqlInsert . "<br>" . mysqli_error($conn);
                }
            } else {
                echo "No tickets found for the specified package size and ticket type.";
            }
        } else {
            echo "Error: " . $sqlpackage . "<br>" . mysqli_error($conn);
        }
    }
}