<?php
session_start();
include('server.php');

// Set content type to JSON for AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
}

// Handle AJAX requests
if (isset($_POST['action']) && $_POST['action'] === 'check_password') {
    $username_or_email = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username_or_email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Username and password required']);
        exit();
    }
    
    // Check if input is email or username
    $is_email = strpos($username_or_email, '@') !== false;
    $field = $is_email ? 'email' : 'username';
    
    // Get user from database
    $stmt = $conn->prepare("SELECT id, username, email, password FROM user WHERE $field = ? LIMIT 1");
    $stmt->bind_param("s", $username_or_email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 0) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit();
    }
    
    $user = $result->fetch_assoc();
    $stored_hash = $user['password'];
    
    // Analyze the stored password hash
    $hash_info = [
        'length' => strlen($stored_hash),
        'starts_with_2y' => strpos($stored_hash, '$2y$') === 0,
        'starts_with_2a' => strpos($stored_hash, '$2a$') === 0,
        'is_bcrypt' => (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0),
        'first_10_chars' => substr($stored_hash, 0, 10),
        'hash_preview' => strlen($stored_hash) > 20 ? substr($stored_hash, 0, 20) . '...' : $stored_hash
    ];
    
    // Test password verification
    $verification_results = [];
    
    // Test bcrypt verification
    if ($hash_info['is_bcrypt']) {
        $bcrypt_result = password_verify($password, $stored_hash);
        $verification_results['bcrypt'] = $bcrypt_result;
    } else {
        $verification_results['bcrypt'] = 'N/A (not bcrypt)';
    }
    
    // Test MD5 verification
    $md5_hash = md5($password);
    $md5_result = ($stored_hash === $md5_hash);
    $verification_results['md5'] = $md5_result;
    $verification_results['md5_hash'] = $md5_hash;
    
    // Test direct comparison (for debugging)
    $direct_match = ($stored_hash === $password);
    $verification_results['direct'] = $direct_match;
    
    // Determine the correct verification method
    $login_success = false;
    $method_used = '';
    
    if ($hash_info['is_bcrypt']) {
        if (password_verify($password, $stored_hash)) {
            $login_success = true;
            $method_used = 'bcrypt';
        }
    } else {
        if ($stored_hash === md5($password)) {
            $login_success = true;
            $method_used = 'md5';
        }
    }
    
    echo json_encode([
        'success' => true,
        'user_info' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email']
        ],
        'hash_info' => $hash_info,
        'verification_results' => $verification_results,
        'login_success' => $login_success,
        'method_used' => $method_used,
        'stored_hash' => $stored_hash // For debugging only
    ]);
    exit();
}

// Handle password update requests
if (isset($_POST['action']) && $_POST['action'] === 'update_password') {
    $user_id = $_POST['user_id'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $hash_type = $_POST['hash_type'] ?? 'bcrypt';
    
    if (empty($user_id) || empty($new_password)) {
        echo json_encode(['success' => false, 'message' => 'User ID and password required']);
        exit();
    }
    
    // Hash the password according to the specified type
    if ($hash_type === 'bcrypt') {
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    } else {
        $hashed_password = md5($new_password);
    }
    
    // Update the password
    $stmt = $conn->prepare("UPDATE user SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $hashed_password, $user_id);
    
    if ($stmt->execute()) {
        echo json_encode([
            'success' => true, 
            'message' => 'Password updated successfully',
            'new_hash' => $hashed_password,
            'hash_type' => $hash_type
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update password']);
    }
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Diagnostic Tool - HelloIT</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
        padding: 20px;
    }

    .diagnostic-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .diagnostic-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .hash-display {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 10px;
        font-family: monospace;
        word-break: break-all;
        margin: 10px 0;
    }

    .result-success {
        color: #28a745;
        font-weight: bold;
    }

    .result-failure {
        color: #dc3545;
        font-weight: bold;
    }

    .result-na {
        color: #6c757d;
        font-style: italic;
    }
    </style>
</head>

<body>
    <div class="diagnostic-container">
        <h1 class="text-center mb-4">Password Diagnostic Tool</h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>Purpose</h5>
            <p class="mb-0">This tool helps diagnose password authentication issues by analyzing stored password hashes and testing verification methods.</p>
        </div>

        <div class="diagnostic-section">
            <h3>Password Analysis</h3>
            <form id="diagnosticForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <input type="text" class="form-control" id="username" placeholder="Enter username or email">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="password" class="form-label">Password to Test</label>
                            <input type="password" class="form-control" id="password" placeholder="Enter password">
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Analyze Password
                </button>
            </form>
        </div>

        <div id="results" class="diagnostic-section" style="display: none;">
            <h3>Analysis Results</h3>
            <div id="resultsContent"></div>
        </div>

        <div id="passwordUpdate" class="diagnostic-section" style="display: none;">
            <h3>Password Update</h3>
            <p>If the current password hash is corrupted or incorrect, you can update it here:</p>
            <form id="updateForm">
                <input type="hidden" id="updateUserId">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" placeholder="Enter new password">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="hashType" class="form-label">Hash Type</label>
                            <select class="form-control" id="hashType">
                                <option value="bcrypt">bcrypt (Recommended)</option>
                                <option value="md5">MD5 (Legacy)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-warning">
                    <i class="fas fa-key me-2"></i>Update Password
                </button>
            </form>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.getElementById('diagnosticForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            alert('Please enter both username and password');
            return;
        }
        
        // Send AJAX request
        fetch('password-diagnostic.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=check_password&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayResults(data);
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred during analysis');
        });
    });

    function displayResults(data) {
        const resultsDiv = document.getElementById('results');
        const contentDiv = document.getElementById('resultsContent');
        
        let html = `
            <div class="row">
                <div class="col-md-6">
                    <h5>User Information</h5>
                    <p><strong>ID:</strong> ${data.user_info.id}</p>
                    <p><strong>Username:</strong> ${data.user_info.username}</p>
                    <p><strong>Email:</strong> ${data.user_info.email}</p>
                </div>
                <div class="col-md-6">
                    <h5>Hash Information</h5>
                    <p><strong>Length:</strong> ${data.hash_info.length} characters</p>
                    <p><strong>Type:</strong> ${data.hash_info.is_bcrypt ? 'bcrypt' : 'MD5/Other'}</p>
                    <p><strong>Preview:</strong> <code>${data.hash_info.hash_preview}</code></p>
                </div>
            </div>
            
            <h5>Stored Hash</h5>
            <div class="hash-display">${data.stored_hash}</div>
            
            <h5>Verification Results</h5>
            <table class="table table-bordered">
                <tr>
                    <td><strong>bcrypt Verification</strong></td>
                    <td class="${data.verification_results.bcrypt === true ? 'result-success' : (data.verification_results.bcrypt === false ? 'result-failure' : 'result-na')}">
                        ${data.verification_results.bcrypt === true ? '✅ PASS' : (data.verification_results.bcrypt === false ? '❌ FAIL' : data.verification_results.bcrypt)}
                    </td>
                </tr>
                <tr>
                    <td><strong>MD5 Verification</strong></td>
                    <td class="${data.verification_results.md5 ? 'result-success' : 'result-failure'}">
                        ${data.verification_results.md5 ? '✅ PASS' : '❌ FAIL'}
                    </td>
                </tr>
                <tr>
                    <td><strong>Direct Match</strong></td>
                    <td class="${data.verification_results.direct ? 'result-success' : 'result-failure'}">
                        ${data.verification_results.direct ? '✅ PASS (Plaintext!)' : '❌ FAIL'}
                    </td>
                </tr>
            </table>
            
            <div class="alert ${data.login_success ? 'alert-success' : 'alert-danger'}">
                <h6>Final Result</h6>
                <p class="mb-0">
                    <strong>Login Status:</strong> ${data.login_success ? '✅ SUCCESS' : '❌ FAILED'}<br>
                    <strong>Method Used:</strong> ${data.method_used || 'None'}
                </p>
            </div>
        `;
        
        contentDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
        
        // Show password update section if login failed
        if (!data.login_success) {
            document.getElementById('updateUserId').value = data.user_info.id;
            document.getElementById('passwordUpdate').style.display = 'block';
        }
    }

    document.getElementById('updateForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const userId = document.getElementById('updateUserId').value;
        const newPassword = document.getElementById('newPassword').value;
        const hashType = document.getElementById('hashType').value;
        
        if (!newPassword) {
            alert('Please enter a new password');
            return;
        }
        
        if (confirm('Are you sure you want to update this user\'s password?')) {
            fetch('password-diagnostic.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=update_password&user_id=${userId}&new_password=${encodeURIComponent(newPassword)}&hash_type=${hashType}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Password updated successfully!');
                    document.getElementById('updateForm').reset();
                    document.getElementById('passwordUpdate').style.display = 'none';
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred during password update');
            });
        }
    });
    </script>
</body>
</html>
