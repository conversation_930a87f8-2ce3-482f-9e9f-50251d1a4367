<?php
include('server.php');

echo "<h2>🧹 Payment Temp Cleanup Feature</h2>";

echo "<h3>✨ **New Feature Overview**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>What was implemented:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Automatic cleanup in webhook:</strong> Deletes payment_temp when user account is created</li>";
echo "<li>✅ <strong>Enhanced payment success page:</strong> Handles missing payment_temp data gracefully</li>";
echo "<li>✅ <strong>Manual cleanup function:</strong> Cleans up old payment_temp records</li>";
echo "<li>✅ <strong>Safety mechanisms:</strong> Prevents accidental data loss</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔄 **How It Works**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Automatic Cleanup (Webhook):</h4>";
echo "<ol>";
echo "<li><strong>Non-login user completes purchase</strong> → Stripe webhook triggered</li>";
echo "<li><strong>User account created</strong> in user table</li>";
echo "<li><strong>Tickets processed</strong> and added to user account</li>";
echo "<li><strong>payment_temp data deleted</strong> automatically</li>";
echo "</ol>";

echo "<h4>Payment Success Page Handling:</h4>";
echo "<ol>";
echo "<li><strong>Check payment_temp</strong> for session data</li>";
echo "<li><strong>If not found:</strong> Look for recently created user (within 10 minutes)</li>";
echo "<li><strong>Generate new password</strong> and show credentials</li>";
echo "<li><strong>User can login</strong> with displayed credentials</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔧 **Technical Implementation**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Webhook Changes:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Keep payment_temp):
$cleanup_stmt = $conn->prepare("UPDATE payment_temp SET user_created = 1, processed_at = NOW() WHERE session_id = ?");

// AFTER (Delete payment_temp):
$cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
$cleanup_stmt->bind_param("s", $session->id);
$cleanup_stmt->execute();
');
echo "</pre>";

echo "<h4>Payment Success Page Changes:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Enhanced fallback logic when payment_temp is missing:
if ($temp_result->num_rows == 0) {
    // Check for recently created user (within 10 minutes)
    $time_diff = time() - strtotime($registration_time);
    if ($time_diff <= 600) {
        // Generate new password and show credentials
        $password = bin2hex(random_bytes(4));
        // Update user password and display credentials
    }
}
');
echo "</pre>";
echo "</div>";

echo "<h3>🛡️ **Safety Features**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>Data Protection:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Only deletes after successful user creation</strong></li>";
echo "<li>✅ <strong>Payment success page handles missing data</strong></li>";
echo "<li>✅ <strong>Manual cleanup has dry-run option</strong></li>";
echo "<li>✅ <strong>Time-based safety checks</strong> (10 minutes for recent users)</li>";
echo "</ul>";

echo "<h4>Fallback Mechanisms:</h4>";
echo "<ul>";
echo "<li>✅ <strong>User table as primary source</strong> when payment_temp is missing</li>";
echo "<li>✅ <strong>Password regeneration</strong> for credential display</li>";
echo "<li>✅ <strong>Error logging</strong> for debugging</li>";
echo "<li>✅ <strong>Graceful degradation</strong> if cleanup fails</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📊 **Current Database Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check current payment_temp status
$total_query = "SELECT COUNT(*) as total FROM payment_temp";
$total_result = mysqli_query($conn, $total_query);
$total_count = mysqli_fetch_assoc($total_result)['total'];

// Check records with created users
$created_query = "
    SELECT COUNT(*) as created_users
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 1
";
$created_result = mysqli_query($conn, $created_query);
$created_count = mysqli_fetch_assoc($created_result)['created_users'];

// Check recent records (last 24 hours)
$recent_query = "SELECT COUNT(*) as recent FROM payment_temp WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
$recent_result = mysqli_query($conn, $recent_query);
$recent_count = mysqli_fetch_assoc($recent_result)['recent'];

echo "<h4>Payment Temp Statistics:</h4>";
echo "<ul>";
echo "<li><strong>Total payment_temp records:</strong> $total_count</li>";
echo "<li><strong>Records with created users:</strong> $created_count</li>";
echo "<li><strong>Recent records (24h):</strong> $recent_count</li>";
echo "</ul>";

if ($created_count > 0) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ <strong>Note:</strong> There are $created_count payment_temp records for users that have been created. These can be safely cleaned up.</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>🎯 **Benefits of This Implementation**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";
echo "<h4>Database Efficiency:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Reduced storage:</strong> No accumulation of temporary data</li>";
echo "<li>✅ <strong>Better performance:</strong> Smaller payment_temp table</li>";
echo "<li>✅ <strong>Cleaner data:</strong> Only active/pending records remain</li>";
echo "</ul>";

echo "<h4>Security Improvements:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Less sensitive data:</strong> Temporary passwords removed quickly</li>";
echo "<li>✅ <strong>Reduced attack surface:</strong> Fewer records to protect</li>";
echo "<li>✅ <strong>Data minimization:</strong> Only keep what's necessary</li>";
echo "</ul>";

echo "<h4>User Experience:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Consistent credentials:</strong> Always shows working login info</li>";
echo "<li>✅ <strong>Reliable process:</strong> Works even if cleanup happens</li>";
echo "<li>✅ <strong>No user impact:</strong> Transparent to end users</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🧪 **Testing the Feature**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Test Automatic Cleanup:</h4>";
echo "<ol>";
echo "<li><strong>Make a non-login purchase</strong> → Complete Stripe checkout</li>";
echo "<li><strong>Check payment_temp</strong> → Should have record initially</li>";
echo "<li><strong>Wait for webhook</strong> → User account gets created</li>";
echo "<li><strong>Check payment_temp again</strong> → Record should be deleted</li>";
echo "<li><strong>Visit payment success page</strong> → Should show credentials from user table</li>";
echo "</ol>";

echo "<h4>Test Manual Cleanup:</h4>";
echo "<ol>";
echo "<li><strong>Go to cleanup function</strong> → Check current status</li>";
echo "<li><strong>Run dry run</strong> → See what would be cleaned</li>";
echo "<li><strong>Run actual cleanup</strong> → Clean up old records</li>";
echo "<li><strong>Verify results</strong> → Check database changes</li>";
echo "</ol>";
echo "</div>";

echo "<h3>📋 **Before vs After Comparison**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Aspect</th>";
echo "<th style='padding: 10px;'>Before (Accumulating Data)</th>";
echo "<th style='padding: 10px;'>After (Auto Cleanup)</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>payment_temp Growth</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Grows indefinitely</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Only active records</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Database Size</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Increases over time</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Stays minimal</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Security</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Old passwords stored</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Temporary data removed</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Maintenance</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Manual cleanup needed</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Automatic maintenance</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>User Experience</strong></td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Works from payment_temp</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Works from user table</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h3>🔗 **Quick Access Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='cleanup-payment-temp.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Manual Cleanup Tool</a>";
echo "<a href='../front-end/payment-success.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Payment Success Page</a>";
echo "<a href='process-pending-users.php' style='background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Process Pending Users</a>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🧹 Payment Temp Cleanup Complete!</h4>";
echo "<p style='color: #155724; margin: 0;'>The system now automatically cleans up payment_temp data when user accounts are created, maintaining database efficiency while ensuring the payment success page continues to work seamlessly. Manual cleanup tools are also available for maintenance.</p>";
echo "</div>";

// Show webhook log if available
if (file_exists('../front-end/webhook.log')) {
    echo "<h3>📝 **Recent Webhook Activity**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $log_content = file_get_contents('../front-end/webhook.log');
    $log_lines = array_slice(explode("\n", $log_content), -10); // Last 10 lines
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; max-height: 200px; overflow-y: auto;'>";
    foreach ($log_lines as $line) {
        if (trim($line)) {
            if (strpos($line, 'payment_temp data deleted') !== false) {
                echo "<span style='color: #28a745;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'error') !== false || strpos($line, 'Error') !== false) {
                echo "<span style='color: #dc3545;'>" . htmlspecialchars($line) . "</span>\n";
            } else {
                echo htmlspecialchars($line) . "\n";
            }
        }
    }
    echo "</pre>";
    echo "<p><em>Green lines show successful payment_temp cleanup operations.</em></p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
