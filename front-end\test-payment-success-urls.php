<?php
/**
 * Test Payment Success URL Fixes
 */

// Auto-detect environment for URL paths (same logic as payment-success.php)
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';

echo "<h2>Payment Success URL Fix Test</h2>";

echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";
echo "<p><strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>URL Base:</strong> " . ($url_base ?: '(empty)') . "</p>";

echo "<hr>";
echo "<h3>Fixed URLs in Payment Success Page:</h3>";

$payment_success_urls = [
    'Cart Redirect (no session)' => $url_base . '/front-end/cart.php',
    'Sign-in Redirect (not guest)' => $url_base . '/front-end/sign-in.php',
    'Sign-in Redirect (error)' => $url_base . '/front-end/sign-in.php',
    'Cart Redirect (checkout error)' => $url_base . '/front-end/cart.php',
    'Login to Your Account (guest)' => $url_base . '/front-end/sign-in.php?username=user12345&email=<EMAIL>&password=abc123',
    'Try Sign-In (pending account)' => $url_base . '/front-end/sign-in.php',
    'View My Tickets (logged-in)' => $url_base . '/front-end/my-ticket.php',
    'Purchase History (logged-in)' => $url_base . '/front-end/purchase-history.php'
];

foreach ($payment_success_urls as $description => $url) {
    echo "<p><strong>$description:</strong><br>";
    echo "<code>$url</code></p>";
}

echo "<hr>";
echo "<h3>File Existence Check:</h3>";

$files_to_check = [
    'sign-in.php' => '../front-end/sign-in.php',
    'cart.php' => '../front-end/cart.php',
    'my-ticket.php' => '../front-end/my-ticket.php',
    'purchase-history.php' => '../front-end/purchase-history.php'
];

foreach ($files_to_check as $name => $path) {
    $exists = file_exists($path);
    $status = $exists ? '✅ EXISTS' : '❌ MISSING';
    echo "<p><strong>$name:</strong> $status</p>";
}

echo "<hr>";
echo "<h3>Expected Behavior:</h3>";

if ($is_localhost) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Localhost (XAMPP) Environment</h4>";
    echo "<ul>";
    echo "<li><strong>Purchase complete:</strong> Shows credentials immediately</li>";
    echo "<li><strong>Login to Your Account:</strong> Goes to /helloit/front-end/sign-in.php</li>";
    echo "<li><strong>View My Tickets:</strong> Goes to /helloit/front-end/my-ticket.php</li>";
    echo "<li><strong>Purchase History:</strong> Goes to /helloit/front-end/purchase-history.php</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Production Environment</h4>";
    echo "<ul>";
    echo "<li><strong>Purchase complete:</strong> Waits for webhook or falls back</li>";
    echo "<li><strong>Login to Your Account:</strong> Goes to /front-end/sign-in.php</li>";
    echo "<li><strong>View My Tickets:</strong> Goes to /front-end/my-ticket.php</li>";
    echo "<li><strong>Purchase History:</strong> Goes to /front-end/purchase-history.php</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Test Links (Click to Verify):</h3>";

$test_links = [
    'Sign-in Page' => $url_base . '/front-end/sign-in.php',
    'Cart Page' => $url_base . '/front-end/cart.php',
    'My Tickets Page' => $url_base . '/front-end/my-ticket.php',
    'Purchase History Page' => $url_base . '/front-end/purchase-history.php'
];

foreach ($test_links as $name => $url) {
    echo "<p><a href='$url' target='_blank' style='color: #007bff; text-decoration: none;'>🔗 Test $name</a></p>";
}

echo "<hr>";
echo "<h3>Issues Fixed:</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>❌ Before Fix:</h4>";
echo "<ul>";
echo "<li>Localhost: URLs went to /helloit/support-ticket/ (404 errors)</li>";
echo "<li>Server: URLs went to /helloit/support-ticket/ (double /helloit)</li>";
echo "<li>Login button: 404 not found on server</li>";
echo "</ul>";

echo "<h4>✅ After Fix:</h4>";
echo "<ul>";
echo "<li>Localhost: URLs go to /helloit/front-end/ (correct)</li>";
echo "<li>Server: URLs go to /front-end/ (correct)</li>";
echo "<li>All buttons work on both environments</li>";
echo "</ul>";
echo "</div>";
?>
