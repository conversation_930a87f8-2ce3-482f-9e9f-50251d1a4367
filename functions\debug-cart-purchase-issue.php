<?php
include('server.php');

echo "<h2>🔍 Debug Cart Purchase Issue</h2>";

// Find the most recent large purchase
$recent_purchase_query = "
    SELECT transactionid, COUNT(*) as item_count, SUM(dollar_price_per_package) as total_amount, purchase_time
    FROM purchasetickets 
    WHERE purchase_time >= DATE_SUB(NOW(), INTERVAL 1 DAY)
    GROUP BY transactionid 
    HAVING item_count >= 3
    ORDER BY purchase_time DESC 
    LIMIT 5
";

$recent_result = mysqli_query($conn, $recent_purchase_query);

echo "<h3>📋 Recent Large Purchases (3+ items):</h3>";

if ($recent_result && mysqli_num_rows($recent_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Transaction ID</th>";
    echo "<th style='padding: 10px;'>Items Count</th>";
    echo "<th style='padding: 10px;'>Total Amount</th>";
    echo "<th style='padding: 10px;'>Purchase Time</th>";
    echo "<th style='padding: 10px;'>Action</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($recent_result)) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . substr($row['transactionid'], 0, 20) . "...</td>";
        echo "<td style='padding: 10px;'>" . $row['item_count'] . "</td>";
        echo "<td style='padding: 10px;'>$" . $row['total_amount'] . "</td>";
        echo "<td style='padding: 10px;'>" . $row['purchase_time'] . "</td>";
        echo "<td style='padding: 10px;'><a href='?analyze=" . urlencode($row['transactionid']) . "' style='background: #007cba; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Analyze</a></td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No recent large purchases found.</p>";
}

// Analyze specific transaction if requested
if (isset($_GET['analyze'])) {
    $transaction_id = $_GET['analyze'];
    
    echo "<h3>🔍 Analyzing Transaction: " . substr($transaction_id, 0, 30) . "...</h3>";
    
    // Get purchase details
    $purchase_query = "
        SELECT purchaseid, username, ticket_type, package_size, numbers_per_package, 
               dollar_price_per_package, purchase_time, remaining_tickets
        FROM purchasetickets 
        WHERE transactionid = ?
        ORDER BY purchaseid ASC
    ";
    
    $purchase_stmt = $conn->prepare($purchase_query);
    $purchase_stmt->bind_param("s", $transaction_id);
    $purchase_stmt->execute();
    $purchase_result = $purchase_stmt->get_result();
    
    if ($purchase_result->num_rows > 0) {
        echo "<h4>📦 Items in Purchase History:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Ticket Type</th>";
        echo "<th style='padding: 8px;'>Package Size</th>";
        echo "<th style='padding: 8px;'>Numbers per Package</th>";
        echo "<th style='padding: 8px;'>Price</th>";
        echo "<th style='padding: 8px;'>Remaining Tickets</th>";
        echo "</tr>";
        
        $total_items = 0;
        $total_amount = 0;
        $username = '';
        
        while ($row = mysqli_fetch_assoc($purchase_result)) {
            $total_items++;
            $total_amount += $row['dollar_price_per_package'];
            $username = $row['username'];
            
            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $row['purchaseid'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['username'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['ticket_type'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['package_size'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['numbers_per_package'] . "</td>";
            echo "<td style='padding: 8px;'>$" . $row['dollar_price_per_package'] . "</td>";
            echo "<td style='padding: 8px;'>" . $row['remaining_tickets'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>📊 Purchase Summary:</h5>";
        echo "<ul>";
        echo "<li><strong>Total Items:</strong> $total_items</li>";
        echo "<li><strong>Total Amount:</strong> $$total_amount</li>";
        echo "<li><strong>Username:</strong> $username</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check user's current ticket counts
        if ($username) {
            $user_query = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE username = ?";
            $user_stmt = $conn->prepare($user_query);
            $user_stmt->bind_param("s", $username);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();
            
            if ($user_result->num_rows > 0) {
                $user_data = $user_result->fetch_assoc();
                
                echo "<h4>🎫 User's Current Ticket Counts:</h4>";
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<ul>";
                echo "<li><strong>Starter Tickets:</strong> " . $user_data['starter_tickets'] . "</li>";
                echo "<li><strong>Premium/Business Tickets:</strong> " . $user_data['premium_tickets'] . "</li>";
                echo "<li><strong>Ultimate Tickets:</strong> " . $user_data['ultimate_tickets'] . "</li>";
                echo "</ul>";
                echo "</div>";
            }
        }
        
        // Check webhook logs for this transaction
        echo "<h4>📋 Webhook Processing Analysis:</h4>";
        
        // Read recent webhook logs
        $webhook_log_file = __DIR__ . '/../front-end/webhook.log';
        if (file_exists($webhook_log_file)) {
            $log_content = file_get_contents($webhook_log_file);
            $log_lines = explode("\n", $log_content);
            
            // Find lines related to this transaction
            $relevant_logs = [];
            foreach ($log_lines as $line) {
                if (strpos($line, substr($transaction_id, -20)) !== false) {
                    $relevant_logs[] = $line;
                }
            }
            
            if (!empty($relevant_logs)) {
                echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0; max-height: 300px; overflow-y: auto;'>";
                echo "<h5>Webhook Logs for this Transaction:</h5>";
                echo "<pre style='font-size: 12px;'>";
                foreach ($relevant_logs as $log) {
                    echo htmlspecialchars($log) . "\n";
                }
                echo "</pre>";
                echo "</div>";
            } else {
                echo "<p>No webhook logs found for this transaction.</p>";
            }
        }
        
        // Expected vs Actual comparison
        echo "<h4>🔍 Expected vs Actual Analysis:</h4>";
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>Based on your screenshot, you purchased:</h5>";
        echo "<ol>";
        echo "<li><strong>STARTER XS:</strong> $20.00 (1 ticket)</li>";
        echo "<li><strong>STARTER S:</strong> $150.00 (15 tickets)</li>";
        echo "<li><strong>BUSINESS XS:</strong> $25.00 (1 ticket)</li>";
        echo "<li><strong>BUSINESS S:</strong> $200.00 (10 tickets)</li>";
        echo "<li><strong>ULTIMATE XS:</strong> $30.00 (1 ticket)</li>";
        echo "<li><strong>ULTIMATE S:</strong> $250.00 (10 tickets) <span style='color: red;'>← MISSING!</span></li>";
        echo "</ol>";
        echo "<p><strong>Expected Total:</strong> $675.00</p>";
        echo "<p><strong>Expected Items:</strong> 6</p>";
        echo "<p><strong>Actual Items Found:</strong> $total_items</p>";
        echo "<p><strong>Actual Total:</strong> $$total_amount</p>";
        
        if ($total_items < 6 || $total_amount < 675) {
            echo "<p style='color: red;'><strong>⚠️ ISSUE CONFIRMED:</strong> Missing items or incorrect amounts!</p>";
        } else {
            echo "<p style='color: green;'><strong>✅ All items processed correctly!</strong></p>";
        }
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ No purchase details found for this transaction.</p>";
    }
}

// Check for common webhook processing issues
echo "<h3>🔧 Common Issues Check:</h3>";

// Check if there are any failed webhook entries
$failed_webhook_query = "
    SELECT session_id, username, email, user_created, processed_at, created_at
    FROM payment_temp 
    WHERE user_created = 0 OR processed_at IS NULL
    ORDER BY created_at DESC 
    LIMIT 10
";

$failed_result = mysqli_query($conn, $failed_webhook_query);

if ($failed_result && mysqli_num_rows($failed_result) > 0) {
    echo "<h4>⚠️ Potentially Failed Webhook Processing:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Session ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>User Created</th>";
    echo "<th style='padding: 8px;'>Processed At</th>";
    echo "<th style='padding: 8px;'>Created At</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($failed_result)) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . substr($row['session_id'], 0, 20) . "...</td>";
        echo "<td style='padding: 8px;'>" . $row['username'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['email'] . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['user_created'] ? 'Yes' : 'No') . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['processed_at'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['created_at'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>✅ No failed webhook processing detected.</p>";
}
?>

<h3>🔧 Potential Solutions</h3>
<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
    <h4>Common Causes of Missing Items:</h4>
    <ul>
        <li><strong>Webhook timeout:</strong> Processing too many items causes timeout</li>
        <li><strong>Metadata parsing issues:</strong> Cart items not properly parsed from Stripe metadata</li>
        <li><strong>Database transaction rollback:</strong> Error in one item causes all to fail</li>
        <li><strong>Memory limits:</strong> Large carts exceed PHP memory limits</li>
        <li><strong>Stripe API limits:</strong> Too many API calls in webhook</li>
    </ul>
    
    <h4>Recommended Fixes:</h4>
    <ol>
        <li><strong>Increase webhook timeout</strong> in Stripe settings</li>
        <li><strong>Improve error handling</strong> in webhook processing</li>
        <li><strong>Add retry mechanism</strong> for failed items</li>
        <li><strong>Better logging</strong> for debugging</li>
        <li><strong>Process items individually</strong> instead of in one transaction</li>
    </ol>
</div>

<h3>🔗 Quick Actions</h3>
<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>
    <a href="../front-end/webhook.log" target="_blank" style="background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">View Webhook Logs</a>
    <a href="fix-missing-purchase-items.php" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Fix Missing Items</a>
    <a href="../front-end/my-ticket.php" style="background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Check My Tickets</a>
    <a href="../front-end/payment-history.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Payment History</a>
</div>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; }
</style>
