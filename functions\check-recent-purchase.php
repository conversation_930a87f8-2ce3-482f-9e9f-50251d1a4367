<?php
include('server.php');

echo "<h2>Recent Purchase Credentials Check</h2>";

// Get the most recent user from the main user table
$recent_user_query = "SELECT id, username, email, registration_time FROM user ORDER BY id DESC LIMIT 1";
$recent_user_result = mysqli_query($conn, $recent_user_query);

if ($recent_user_result && mysqli_num_rows($recent_user_result) > 0) {
    $recent_user = mysqli_fetch_assoc($recent_user_result);
    
    echo "<div style='border: 2px solid #28a745; padding: 20px; margin: 20px 0; border-radius: 10px; background: #f8fff8;'>";
    echo "<h3>✅ Most Recent User (from webhook)</h3>";
    echo "<p><strong>ID:</strong> " . $recent_user['id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $recent_user['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $recent_user['email'] . "</p>";
    echo "<p><strong>Registration Time:</strong> " . $recent_user['registration_time'] . "</p>";
    
    // Find the password for this user in payment_temp
    $temp_query = "SELECT password, session_id FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1";
    $temp_stmt = $conn->prepare($temp_query);
    $temp_stmt->bind_param("s", $recent_user['username']);
    $temp_stmt->execute();
    $temp_result = $temp_stmt->get_result();
    
    if ($temp_result->num_rows > 0) {
        $temp_data = $temp_result->fetch_assoc();
        echo "<p><strong>Password:</strong> " . $temp_data['password'] . "</p>";
        echo "<p><strong>Session ID:</strong> " . substr($temp_data['session_id'], 0, 30) . "...</p>";
        
        echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🔑 Correct Credentials to Use:</h4>";
        echo "<ul>";
        echo "<li><strong>Username:</strong> " . $recent_user['username'] . "</li>";
        echo "<li><strong>Email:</strong> " . $recent_user['email'] . "</li>";
        echo "<li><strong>Password:</strong> " . $temp_data['password'] . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check if user has tickets
        $ticket_query = "SELECT starter_tickets, premium_tickets, ultimate_tickets FROM user WHERE id = ?";
        $ticket_stmt = $conn->prepare($ticket_query);
        $ticket_stmt->bind_param("i", $recent_user['id']);
        $ticket_stmt->execute();
        $ticket_result = $ticket_stmt->get_result();
        
        if ($ticket_result->num_rows > 0) {
            $ticket_data = $ticket_result->fetch_assoc();
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>🎫 Ticket Count:</h4>";
            echo "<ul>";
            echo "<li><strong>Starter Tickets:</strong> " . $ticket_data['starter_tickets'] . "</li>";
            echo "<li><strong>Premium Tickets:</strong> " . $ticket_data['premium_tickets'] . "</li>";
            echo "<li><strong>Ultimate Tickets:</strong> " . $ticket_data['ultimate_tickets'] . "</li>";
            echo "</ul>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: red;'><strong>❌ No password found in payment_temp for this user</strong></p>";
    }
    
    echo "</div>";
} else {
    echo "<p style='color: red;'>❌ No users found in database</p>";
}

// Also show what payment success page might be showing incorrectly
echo "<h3>🔍 Debugging Payment Success Page Issue</h3>";

$email = '<EMAIL>'; // The email from recent purchase

// Check what the old logic would find
$old_logic_query = "SELECT username, email, password FROM payment_temp WHERE email = ? ORDER BY id DESC LIMIT 1";
$old_logic_stmt = $conn->prepare($old_logic_query);
$old_logic_stmt->bind_param("s", $email);
$old_logic_stmt->execute();
$old_logic_result = $old_logic_stmt->get_result();

if ($old_logic_result->num_rows > 0) {
    $old_logic_data = $old_logic_result->fetch_assoc();
    
    echo "<div style='border: 2px solid #dc3545; padding: 20px; margin: 20px 0; border-radius: 10px; background: #fff8f8;'>";
    echo "<h4>❌ What Payment Success Page Was Showing (WRONG):</h4>";
    echo "<p><strong>Username:</strong> " . $old_logic_data['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $old_logic_data['email'] . "</p>";
    echo "<p><strong>Password:</strong> " . $old_logic_data['password'] . "</p>";
    echo "<p style='color: red;'><strong>Problem:</strong> This is an OLD record, not the current purchase!</p>";
    echo "</div>";
}

// Show all payment_temp records for this email
echo "<h3>📋 All Payment Temp Records for " . $email . "</h3>";
$all_temp_query = "SELECT id, session_id, username, password, created_at FROM payment_temp WHERE email = ? ORDER BY id DESC";
$all_temp_stmt = $conn->prepare($all_temp_query);
$all_temp_stmt->bind_param("s", $email);
$all_temp_stmt->execute();
$all_temp_result = $all_temp_stmt->get_result();

if ($all_temp_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>Session ID</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Password</th>";
    echo "<th style='padding: 10px;'>User Exists?</th>";
    echo "</tr>";
    
    while ($temp_row = mysqli_fetch_assoc($all_temp_result)) {
        // Check if user exists in main table
        $user_exists_query = "SELECT id FROM user WHERE username = ?";
        $user_exists_stmt = $conn->prepare($user_exists_query);
        $user_exists_stmt->bind_param("s", $temp_row['username']);
        $user_exists_stmt->execute();
        $user_exists_result = $user_exists_stmt->get_result();
        $user_exists = $user_exists_result->num_rows > 0;
        
        $row_color = $user_exists ? '#d4edda' : '#f8d7da';
        
        echo "<tr style='background: $row_color;'>";
        echo "<td style='padding: 10px;'>" . $temp_row['id'] . "</td>";
        echo "<td style='padding: 10px;'>" . substr($temp_row['session_id'], 0, 20) . "...</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['username'] . "</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['password'] . "</td>";
        echo "<td style='padding: 10px;'>" . ($user_exists ? '✅ YES' : '❌ NO') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p><strong>Legend:</strong></p>";
    echo "<ul>";
    echo "<li style='background: #d4edda; padding: 5px;'>✅ Green = User exists in main table (can sign in)</li>";
    echo "<li style='background: #f8d7da; padding: 5px;'>❌ Red = User only in payment_temp (webhook pending)</li>";
    echo "</ul>";
}
?>

<h3>🧪 Test Sign-In</h3>
<p>Use the correct credentials from above to test sign-in:</p>
<p><a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Go to Sign-In Page</a></p>

<h3>🔧 Tools</h3>
<p><a href="process-pending-users.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Process Pending Users</a></p>
<p><a href="quick-debug.php" style="background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Quick Debug</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
</style>
