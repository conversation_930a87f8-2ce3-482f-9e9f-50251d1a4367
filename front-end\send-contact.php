<?php
session_start();

// MANUAL OVERRIDE: Force production paths for helloit.io
$base_path = '/';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: ' . $base_path . 'support-ticket/contact/');
    exit;
}

// Honeypot spam protection
if (!empty($_POST['website'])) {
    // This is likely spam, redirect silently
    header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
    exit;
}

// Validate required fields
$required_fields = ['first_name', 'last_name', 'email', 'subject', 'message'];
$errors = [];

foreach ($required_fields as $field) {
    if (empty($_POST[$field])) {
        $errors[] = ucfirst(str_replace('_', ' ', $field)) . ' is required';
    }
}

// Validate email format
if (!empty($_POST['email']) && !filter_var($_POST['email'], FILTER_VALIDATE_EMAIL)) {
    $errors[] = 'Please enter a valid email address';
}

// If there are validation errors, redirect back with error
if (!empty($errors)) {
    $_SESSION['contact_errors'] = $errors;
    $_SESSION['contact_data'] = $_POST;
    header('Location: ' . $base_path . 'support-ticket/contact/?status=error');
    exit;
}

// Load email configuration
$email_config = include 'email-config.php';

// Sanitize input data
$first_name = htmlspecialchars(trim($_POST['first_name']));
$last_name = htmlspecialchars(trim($_POST['last_name']));
$email = htmlspecialchars(trim($_POST['email']));
$phone = htmlspecialchars(trim($_POST['phone'] ?? ''));
$company = htmlspecialchars(trim($_POST['company'] ?? ''));
$subject = htmlspecialchars(trim($_POST['subject']));
$message = htmlspecialchars(trim($_POST['message']));

// Use PHP mail() function (simple and reliable)
$to = $email_config['to_email'];
$email_subject = $email_config['subject_prefix'] . $subject;

$email_body = "New contact form submission:\n\n";
$email_body .= "Name: $first_name $last_name\n";
$email_body .= "Email: $email\n";
$email_body .= "Phone: " . ($phone ?: 'Not provided') . "\n";
$email_body .= "Company: " . ($company ?: 'Not provided') . "\n";
$email_body .= "Subject: $subject\n\n";
$email_body .= "Message:\n$message\n\n";
$email_body .= "Submitted on: " . date('Y-m-d H:i:s') . "\n";
$email_body .= "IP Address: " . $_SERVER['REMOTE_ADDR'] . "\n";
$email_body .= "User Agent: " . $_SERVER['HTTP_USER_AGENT'] . "\n";

$headers = "From: $email\r\n";
$headers .= "Reply-To: $email\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";
$headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

// Try to send email
$email_sent = mail($to, $email_subject, $email_body, $headers);

// If email fails, save to file as backup (for development)
if (!$email_sent) {
    $log_file = 'contact-submissions.log';
    $log_entry = "\n" . str_repeat("=", 50) . "\n";
    $log_entry .= "Date: " . date('Y-m-d H:i:s') . "\n";
    $log_entry .= "To: $to\n";
    $log_entry .= "Subject: $email_subject\n";
    $log_entry .= "Content:\n$email_body\n";
    $log_entry .= str_repeat("=", 50) . "\n";

    if (file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX)) {
        $_SESSION['contact_success'] = 'Thank you for your message! We have received it and will get back to you soon. (Note: Email server not configured - message saved locally)';
        header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
        exit;
    }
}

if ($email_sent) {
    $_SESSION['contact_success'] = 'Thank you for your message! We will get back to you soon.';
    header('Location: ' . $base_path . 'support-ticket/contact/?status=success');
} else {
    $_SESSION['contact_errors'] = ['Failed to send email. Please check your email configuration. For development, check contact-submissions.log file.'];
    header('Location: ' . $base_path . 'support-ticket/contact/?status=error');
}
?>
