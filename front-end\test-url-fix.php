<?php
/**
 * Test URL environment detection
 */

// Auto-detect environment for URL paths (same logic as payment-success.php)
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
$url_base = $is_localhost ? '/helloit' : '';

echo "<h2>URL Environment Detection Test</h2>";

echo "<p><strong>HTTP_HOST:</strong> " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p><strong>Is Localhost:</strong> " . ($is_localhost ? 'Yes' : 'No') . "</p>";
echo "<p><strong>URL Base:</strong> " . ($url_base ?: '(empty)') . "</p>";

echo "<hr>";
echo "<h3>Generated URLs:</h3>";

$test_urls = [
    'Sign-in' => $url_base . '/front-end/sign-in.php',
    'Cart' => $url_base . '/front-end/cart.php',
    'My Tickets' => $url_base . '/front-end/my-ticket.php',
    'Purchase History' => $url_base . '/front-end/purchase-history.php'
];

foreach ($test_urls as $name => $url) {
    echo "<p><strong>$name:</strong> <code>$url</code></p>";
}

echo "<hr>";
echo "<h3>Expected Results:</h3>";

if ($is_localhost) {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px;'>";
    echo "<strong>✓ Localhost Environment</strong><br>";
    echo "URLs should include '/helloit' prefix<br>";
    echo "Example: /helloit/front-end/sign-in.php";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 10px; border-radius: 5px;'>";
    echo "<strong>✓ Production Environment</strong><br>";
    echo "URLs should NOT include '/helloit' prefix<br>";
    echo "Example: /front-end/sign-in.php";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Test Links:</h3>";
echo "<p>Click these links to test if they work correctly:</p>";

foreach ($test_urls as $name => $url) {
    echo "<p><a href='$url' target='_blank'>Test $name Link</a></p>";
}
?>
