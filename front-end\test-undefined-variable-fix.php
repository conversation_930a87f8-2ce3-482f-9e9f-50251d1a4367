<?php
/**
 * Test Undefined Variable Fix
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Undefined Variable Fix Test</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Issue Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ The Problem:</h4>";
echo "<pre style='background: #ffe6e6; padding: 10px; border-radius: 5px;'>";
echo "Warning: Undefined variable \$checkout_session in payment-success.php on line 263\n";
echo "Warning: Attempt to read property \"customer_details\" on null in payment-success.php on line 263\n";
echo "Warning: Undefined variable \$checkout_session in payment-success.php on line 314\n";
echo "Warning: Attempt to read property \"metadata\" on null in payment-success.php on line 314";
echo "</pre>";

echo "<h4>✅ The Solution:</h4>";
echo "<ul>";
echo "<li><strong>Moved Stripe session retrieval</strong> to the top of the file</li>";
echo "<li><strong>Retrieved \$checkout_session early</strong> before using it in fallback</li>";
echo "<li><strong>Removed duplicate retrieval</strong> later in the code</li>";
echo "<li><strong>All variables now defined</strong> before use</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Code Changes:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Before (Caused Errors):</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// Line 263: Using \$checkout_session before it's defined\n";
echo "if (!\$guest_credentials) {\n";
echo "    \$customer_details = \$checkout_session->customer_details; // ❌ UNDEFINED\n";
echo "}\n\n";
echo "// Line 361: Stripe session retrieved here (too late)\n";
echo "\$checkout_session = \\Stripe\\Checkout\\Session::retrieve(\$session_id);";
echo "</pre>";

echo "<h4>🔧 After (Fixed):</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// Line 259: Retrieve Stripe session early\n";
echo "\$checkout_session = \\Stripe\\Checkout\\Session::retrieve(\$session_id);\n\n";
echo "// Line 272: Now \$checkout_session is defined\n";
echo "if (!\$guest_credentials) {\n";
echo "    \$customer_details = \$checkout_session->customer_details; // ✅ DEFINED\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Flow Explanation:</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📋 New Execution Order:</h4>";
echo "<ol>";
echo "<li><strong>Session validation</strong> → Check if session_id exists</li>";
echo "<li><strong>Stripe session retrieval</strong> → Get checkout session from Stripe</li>";
echo "<li><strong>Guest credentials check</strong> → Look for webhook data</li>";
echo "<li><strong>Fallback processing</strong> → Use checkout session if no webhook data</li>";
echo "<li><strong>Payment details</strong> → Extract amount, currency, status</li>";
echo "<li><strong>Display page</strong> → Show results to user</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Benefits:</h3>";

echo "<ul>";
echo "<li>✅ <strong>No more warnings:</strong> All variables defined before use</li>";
echo "<li>✅ <strong>Cleaner code:</strong> Single Stripe session retrieval</li>";
echo "<li>✅ <strong>Better error handling:</strong> Early exit if session invalid</li>";
echo "<li>✅ <strong>Consistent behavior:</strong> Same data available throughout</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

if ($is_localhost) {
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Complete a purchase</strong> → Should work without warnings</li>";
    echo "<li><strong>Check PHP error log</strong> → No undefined variable warnings</li>";
    echo "<li><strong>Check payment success page</strong> → Should display correctly</li>";
    echo "<li><strong>Check browser console</strong> → No JavaScript errors</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>Same fix applies to production - no more undefined variable warnings.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Error Log Check:</h3>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📝 What to Look For:</h4>";
echo "<ul>";
echo "<li><strong>Before fix:</strong> Multiple 'Undefined variable' warnings</li>";
echo "<li><strong>After fix:</strong> Clean error log, no variable warnings</li>";
echo "<li><strong>Check files:</strong> XAMPP error logs, server error logs</li>";
echo "</ul>";

echo "<h4>🔍 Common Log Locations:</h4>";
echo "<ul>";
echo "<li><strong>XAMPP:</strong> <code>C:\\xampp\\apache\\logs\\error.log</code></li>";
echo "<li><strong>Server:</strong> cPanel Error Logs or <code>/var/log/apache2/error.log</code></li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>No PHP warnings:</strong> Clean execution without undefined variable errors</li>";
echo "<li>✅ <strong>Payment success page:</strong> Displays correctly with all data</li>";
echo "<li>✅ <strong>Fallback processing:</strong> Works when webhook data missing</li>";
echo "<li>✅ <strong>Both environments:</strong> Localhost and server work without errors</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Summary:</h3>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>The undefined variable issue is now fixed!</strong></p>";
echo "<ul>";
echo "<li><strong>Root cause:</strong> Using \$checkout_session before it was defined</li>";
echo "<li><strong>Solution:</strong> Moved Stripe session retrieval to the top</li>";
echo "<li><strong>Result:</strong> Clean execution without warnings</li>";
echo "</ul>";
echo "<p><strong>Your payment success page should now work perfectly without any PHP warnings.</strong></p>";
echo "</div>";
?>
