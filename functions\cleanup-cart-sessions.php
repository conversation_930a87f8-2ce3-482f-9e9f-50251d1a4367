<?php
include('server.php');

echo "<h2>🧹 Cart Sessions Cleanup Utility</h2>";

echo "<h3>📊 **Current Cart Sessions Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Get cart sessions statistics
$stats_query = "
    SELECT 
        COUNT(*) as total_sessions,
        COUNT(CASE WHEN processed_at IS NOT NULL THEN 1 END) as processed_sessions,
        COUNT(CASE WHEN processed_at IS NULL THEN 1 END) as unprocessed_sessions,
        COUNT(CASE WHEN created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND processed_at IS NULL THEN 1 END) as old_unprocessed,
        COUNT(CASE WHEN created_at < DATE_SUB(NOW(), INTERVAL 7 DAYS) THEN 1 END) as very_old_sessions,
        ROUND(SUM(LENGTH(cart_data)) / 1024, 2) as total_size_kb
    FROM cart_sessions
";

$stats_result = mysqli_query($conn, $stats_query);
$stats = mysqli_fetch_assoc($stats_result);

echo "<h4>Storage Statistics:</h4>";
echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 14px;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 8px;'>Metric</th>";
echo "<th style='padding: 8px;'>Count</th>";
echo "<th style='padding: 8px;'>Description</th>";
echo "</tr>";

$metrics = [
    ['Total Sessions', $stats['total_sessions'], 'All cart sessions in database'],
    ['Processed Sessions', $stats['processed_sessions'], 'Sessions that completed payment'],
    ['Unprocessed Sessions', $stats['unprocessed_sessions'], 'Sessions without completed payment'],
    ['Old Unprocessed (24h+)', $stats['old_unprocessed'], 'Abandoned sessions older than 24 hours'],
    ['Very Old Sessions (7d+)', $stats['very_old_sessions'], 'Sessions older than 7 days'],
    ['Total Storage', $stats['total_size_kb'] . ' KB', 'Approximate storage used by cart data']
];

foreach ($metrics as $metric) {
    $color = '';
    if ($metric[0] == 'Old Unprocessed (24h+)' && $metric[1] > 0) $color = 'background: #fff3cd;';
    if ($metric[0] == 'Very Old Sessions (7d+)' && $metric[1] > 0) $color = 'background: #f8d7da;';
    
    echo "<tr style='$color'>";
    echo "<td style='padding: 8px; font-weight: bold;'>" . $metric[0] . "</td>";
    echo "<td style='padding: 8px; text-align: center;'>" . $metric[1] . "</td>";
    echo "<td style='padding: 8px;'>" . $metric[2] . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

echo "<h3>🔧 **Cleanup Options**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

if (isset($_POST['action'])) {
    $action = $_POST['action'];
    $deleted_count = 0;
    $cleanup_message = '';
    
    switch ($action) {
        case 'cleanup_processed':
            // Delete all processed cart sessions
            $cleanup_query = "DELETE FROM cart_sessions WHERE processed_at IS NOT NULL";
            $result = mysqli_query($conn, $cleanup_query);
            $deleted_count = mysqli_affected_rows($conn);
            $cleanup_message = "Deleted $deleted_count processed cart sessions";
            break;
            
        case 'cleanup_old_unprocessed':
            // Delete unprocessed sessions older than 24 hours
            $cleanup_query = "DELETE FROM cart_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND processed_at IS NULL";
            $result = mysqli_query($conn, $cleanup_query);
            $deleted_count = mysqli_affected_rows($conn);
            $cleanup_message = "Deleted $deleted_count old unprocessed cart sessions (24+ hours old)";
            break;
            
        case 'cleanup_very_old':
            // Delete all sessions older than 7 days
            $cleanup_query = "DELETE FROM cart_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $result = mysqli_query($conn, $cleanup_query);
            $deleted_count = mysqli_affected_rows($conn);
            $cleanup_message = "Deleted $deleted_count very old cart sessions (7+ days old)";
            break;
            
        case 'cleanup_all':
            // Delete all cart sessions (use with caution!)
            $cleanup_query = "DELETE FROM cart_sessions";
            $result = mysqli_query($conn, $cleanup_query);
            $deleted_count = mysqli_affected_rows($conn);
            $cleanup_message = "Deleted ALL $deleted_count cart sessions";
            break;
    }
    
    if ($deleted_count > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
        echo "<h4>✅ Cleanup Completed!</h4>";
        echo "<p>$cleanup_message</p>";
        echo "<p><strong>Storage Saved:</strong> Approximately " . round($deleted_count * 0.5, 2) . " KB</p>";
        echo "</div>";
        
        // Refresh stats
        $stats_result = mysqli_query($conn, $stats_query);
        $stats = mysqli_fetch_assoc($stats_result);
    } else {
        echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0; color: #0c5460;'>";
        echo "<h4>ℹ️ No Data to Clean</h4>";
        echo "<p>No cart sessions matched the cleanup criteria.</p>";
        echo "</div>";
    }
}

echo "<h4>Available Cleanup Actions:</h4>";

// Cleanup processed sessions
if ($stats['processed_sessions'] > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h5>🟢 Clean Processed Sessions</h5>";
    echo "<p><strong>Target:</strong> {$stats['processed_sessions']} processed cart sessions</p>";
    echo "<p><strong>Safe:</strong> ✅ These sessions completed payment and are no longer needed</p>";
    echo "<p><strong>Storage Saved:</strong> ~" . round($stats['processed_sessions'] * 0.5, 2) . " KB</p>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='cleanup_processed'>";
    echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Delete {$stats['processed_sessions']} processed cart sessions?\")'>Clean Processed Sessions</button>";
    echo "</form>";
    echo "</div>";
}

// Cleanup old unprocessed sessions
if ($stats['old_unprocessed'] > 0) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h5>🟡 Clean Old Unprocessed Sessions</h5>";
    echo "<p><strong>Target:</strong> {$stats['old_unprocessed']} unprocessed sessions older than 24 hours</p>";
    echo "<p><strong>Safe:</strong> ⚠️ These are likely abandoned carts (payment not completed)</p>";
    echo "<p><strong>Storage Saved:</strong> ~" . round($stats['old_unprocessed'] * 0.5, 2) . " KB</p>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='cleanup_old_unprocessed'>";
    echo "<button type='submit' style='background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Delete {$stats['old_unprocessed']} old unprocessed cart sessions?\")'>Clean Old Unprocessed</button>";
    echo "</form>";
    echo "</div>";
}

// Cleanup very old sessions
if ($stats['very_old_sessions'] > 0) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h5>🔴 Clean Very Old Sessions</h5>";
    echo "<p><strong>Target:</strong> {$stats['very_old_sessions']} sessions older than 7 days</p>";
    echo "<p><strong>Safe:</strong> ⚠️ Includes both processed and unprocessed sessions</p>";
    echo "<p><strong>Storage Saved:</strong> ~" . round($stats['very_old_sessions'] * 0.5, 2) . " KB</p>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='cleanup_very_old'>";
    echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Delete {$stats['very_old_sessions']} very old cart sessions?\")'>Clean Very Old Sessions</button>";
    echo "</form>";
    echo "</div>";
}

// Emergency cleanup all
if ($stats['total_sessions'] > 0) {
    echo "<div style='background: #343a40; color: white; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h5>⚫ Emergency: Clean ALL Sessions</h5>";
    echo "<p><strong>Target:</strong> ALL {$stats['total_sessions']} cart sessions</p>";
    echo "<p><strong>Safe:</strong> ❌ Use only in emergencies - will delete all cart session data</p>";
    echo "<p><strong>Storage Saved:</strong> ~{$stats['total_size_kb']} KB</p>";
    echo "<form method='POST' style='display: inline;'>";
    echo "<input type='hidden' name='action' value='cleanup_all'>";
    echo "<button type='submit' style='background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"⚠️ WARNING: This will delete ALL cart sessions!\\n\\nAre you absolutely sure?\")'>🚨 Delete ALL Sessions</button>";
    echo "</form>";
    echo "</div>";
}

if ($stats['total_sessions'] == 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
    echo "<h4>✅ No Cleanup Needed</h4>";
    echo "<p>The cart_sessions table is already clean - no sessions found.</p>";
    echo "</div>";
}

echo "</div>";

echo "<h3>📋 **Recent Cart Sessions**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Show recent cart sessions
$recent_query = "
    SELECT 
        session_id,
        user_id,
        created_at,
        processed_at,
        ROUND(LENGTH(cart_data) / 1024, 2) as size_kb,
        CASE 
            WHEN processed_at IS NOT NULL THEN 'Processed'
            WHEN created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 'Old Unprocessed'
            ELSE 'Recent Unprocessed'
        END as status
    FROM cart_sessions 
    ORDER BY created_at DESC 
    LIMIT 10
";

$recent_result = mysqli_query($conn, $recent_query);

if (mysqli_num_rows($recent_result) > 0) {
    echo "<h4>Last 10 Cart Sessions:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 6px;'>Session ID</th>";
    echo "<th style='padding: 6px;'>User ID</th>";
    echo "<th style='padding: 6px;'>Created</th>";
    echo "<th style='padding: 6px;'>Processed</th>";
    echo "<th style='padding: 6px;'>Size</th>";
    echo "<th style='padding: 6px;'>Status</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($recent_result)) {
        $status_color = '';
        switch ($row['status']) {
            case 'Processed': $status_color = 'background: #d4edda;'; break;
            case 'Old Unprocessed': $status_color = 'background: #f8d7da;'; break;
            case 'Recent Unprocessed': $status_color = 'background: #fff3cd;'; break;
        }
        
        echo "<tr style='$status_color'>";
        echo "<td style='padding: 6px; font-family: monospace;'>" . substr($row['session_id'], 0, 20) . "...</td>";
        echo "<td style='padding: 6px; text-align: center;'>" . ($row['user_id'] ?? 'Guest') . "</td>";
        echo "<td style='padding: 6px;'>" . date('M j, H:i', strtotime($row['created_at'])) . "</td>";
        echo "<td style='padding: 6px;'>" . ($row['processed_at'] ? date('M j, H:i', strtotime($row['processed_at'])) : '-') . "</td>";
        echo "<td style='padding: 6px; text-align: right;'>" . $row['size_kb'] . " KB</td>";
        echo "<td style='padding: 6px;'>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No cart sessions found.</p>";
}

echo "</div>";

echo "<h3>🔄 **Automated Cleanup Recommendations**</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";
echo "<h4>Best Practices for Cart Sessions Management:</h4>";
echo "<ul>";
echo "<li><strong>✅ Safe Daily Cleanup:</strong> Delete processed sessions (they're no longer needed)</li>";
echo "<li><strong>⚠️ Weekly Cleanup:</strong> Delete unprocessed sessions older than 24 hours (abandoned carts)</li>";
echo "<li><strong>🗓️ Monthly Cleanup:</strong> Delete all sessions older than 7 days for deep cleaning</li>";
echo "<li><strong>📊 Monitor Storage:</strong> Keep an eye on total storage usage</li>";
echo "</ul>";

echo "<h4>Automation Options:</h4>";
echo "<p><strong>Cron Job Example:</strong> Add this to your server's cron to run daily cleanup:</p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo "# Daily cleanup of processed cart sessions at 2 AM\n";
echo "0 2 * * * /usr/bin/php /path/to/your/helloit/functions/cleanup-cart-sessions.php?auto=processed\n";
echo "\n";
echo "# Weekly cleanup of old unprocessed sessions on Sundays at 3 AM\n";
echo "0 3 * * 0 /usr/bin/php /path/to/your/helloit/functions/cleanup-cart-sessions.php?auto=old_unprocessed";
echo "</pre>";

echo "<p><strong>Current Storage Impact:</strong></p>";
echo "<ul>";
echo "<li>Total cart sessions: {$stats['total_sessions']}</li>";
echo "<li>Storage used: ~{$stats['total_size_kb']} KB</li>";
echo "<li>Potential savings from processed cleanup: ~" . round($stats['processed_sessions'] * 0.5, 2) . " KB</li>";
echo "</ul>";
echo "</div>";

// Handle automated cleanup via URL parameter
if (isset($_GET['auto'])) {
    $auto_action = $_GET['auto'];
    $deleted = 0;
    
    switch ($auto_action) {
        case 'processed':
            $result = mysqli_query($conn, "DELETE FROM cart_sessions WHERE processed_at IS NOT NULL");
            $deleted = mysqli_affected_rows($conn);
            echo "<p>Automated cleanup: Deleted $deleted processed cart sessions</p>";
            break;
            
        case 'old_unprocessed':
            $result = mysqli_query($conn, "DELETE FROM cart_sessions WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) AND processed_at IS NULL");
            $deleted = mysqli_affected_rows($conn);
            echo "<p>Automated cleanup: Deleted $deleted old unprocessed cart sessions</p>";
            break;
    }
    
    if ($deleted > 0) {
        error_log("Cart sessions automated cleanup: Deleted $deleted sessions via $auto_action");
    }
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
pre { font-family: 'Courier New', monospace; }
</style>
