<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Photo Upload</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 Photo Upload Test</h2>
        
        <div class="row">
            <div class="col-md-6">
                <h4>Test 1: Simple Click Upload</h4>
                <div class="upload-test-1" style="border: 2px dashed #ddd; padding: 40px; text-align: center; cursor: pointer; margin: 20px 0;">
                    <i class="fas fa-camera fa-2x text-muted mb-2"></i>
                    <p>Click here to upload</p>
                </div>
                <input type="file" id="fileInput1" multiple accept="image/*" style="display: none;">
                <div id="result1" class="mt-2"></div>
            </div>
            
            <div class="col-md-6">
                <h4>Test 2: Button Upload</h4>
                <button type="button" class="btn btn-primary" id="uploadBtn">
                    <i class="fas fa-upload"></i> Choose Files
                </button>
                <input type="file" id="fileInput2" multiple accept="image/*" style="display: none;">
                <div id="result2" class="mt-2"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>Test 3: Direct File Input</h4>
                <input type="file" id="fileInput3" multiple accept="image/*" class="form-control">
                <div id="result3" class="mt-2"></div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <h4>Debug Console</h4>
                <div id="debugConsole" style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;"></div>
            </div>
        </div>
    </div>

    <script>
    function debugLog(message) {
        const console = document.getElementById('debugConsole');
        const timestamp = new Date().toLocaleTimeString();
        console.innerHTML += `[${timestamp}] ${message}<br>`;
        console.scrollTop = console.scrollHeight;
        console.log(message);
    }

    $(document).ready(function() {
        debugLog('Document ready');
        
        // Test 1: Click area upload
        $('.upload-test-1').on('click', function() {
            debugLog('Upload area clicked');
            $('#fileInput1')[0].click();
        });
        
        $('#fileInput1').on('change', function(e) {
            const files = e.target.files;
            debugLog(`Test 1: ${files.length} files selected`);
            $('#result1').html(`<p class="text-success">✅ ${files.length} files selected</p>`);
            for (let i = 0; i < files.length; i++) {
                debugLog(`File ${i+1}: ${files[i].name} (${files[i].type})`);
            }
        });
        
        // Test 2: Button upload
        $('#uploadBtn').on('click', function() {
            debugLog('Upload button clicked');
            $('#fileInput2')[0].click();
        });
        
        $('#fileInput2').on('change', function(e) {
            const files = e.target.files;
            debugLog(`Test 2: ${files.length} files selected`);
            $('#result2').html(`<p class="text-success">✅ ${files.length} files selected</p>`);
            for (let i = 0; i < files.length; i++) {
                debugLog(`File ${i+1}: ${files[i].name} (${files[i].type})`);
            }
        });
        
        // Test 3: Direct input
        $('#fileInput3').on('change', function(e) {
            const files = e.target.files;
            debugLog(`Test 3: ${files.length} files selected`);
            $('#result3').html(`<p class="text-success">✅ ${files.length} files selected</p>`);
            for (let i = 0; i < files.length; i++) {
                debugLog(`File ${i+1}: ${files[i].name} (${files[i].type})`);
            }
        });
        
        debugLog('All event handlers attached');
    });
    </script>
</body>
</html>
