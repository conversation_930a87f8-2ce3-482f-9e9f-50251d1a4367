<?php
include('server.php');

echo "<h2>🔧 Logged-In User Cleanup Fix</h2>";

echo "<h3>🎯 **Problem Identified & Fixed**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>The Issue:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Stripe webhook URL:</strong> Points to <code>front-end/stripe-webhook.php</code> (not main <code>webhook.php</code>)</li>";
echo "<li>❌ <strong>Metadata missing:</strong> <code>front-end/create-checkout-session.php</code> didn't include username</li>";
echo "<li>❌ <strong>Cleanup logic:</strong> Only cleaned up guest purchases, not logged-in users</li>";
echo "</ul>";

echo "<h4>The Solution:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Added username to metadata:</strong> <code>front-end/create-checkout-session.php</code> now includes username</li>";
echo "<li>✅ <strong>Enhanced cleanup logic:</strong> <code>front-end/stripe-webhook.php</code> now cleans up both guest and logged-in users</li>";
echo "<li>✅ <strong>Comprehensive deletion:</strong> Deletes by session_id OR username for logged-in users</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 **Technical Changes Made**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

echo "<h4>1. Fixed Metadata (front-end/create-checkout-session.php):</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Missing username):
$metadata = [
    "user_id" => $user_id ?? "guest",
    "total_items" => count($cart_metadata),
    "cart_session_id" => $cart_session_id,
    "total_amount" => array_sum(array_column($cart_metadata, "dollar_price_per_package")),
    "save_payment_method" => $save_payment_method ? "1" : "0"
];

// AFTER (Includes username):
$metadata = [
    "user_id" => $user_id ?? "guest",
    "username" => isset($_SESSION["username"]) ? $_SESSION["username"] : "",
    "total_items" => count($cart_metadata),
    "cart_session_id" => $cart_session_id,
    "total_amount" => array_sum(array_column($cart_metadata, "dollar_price_per_package")),
    "save_payment_method" => $save_payment_method ? "1" : "0"
];
');
echo "</pre>";

echo "<h4>2. Enhanced Cleanup Logic (front-end/stripe-webhook.php):</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Only guest cleanup):
if (!$user_exists) {
    // For new users, delete payment_temp data since user account is now created
    $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
    $cleanup_stmt->bind_param("s", $session->id);
    $cleanup_stmt->execute();
    file_put_contents(__DIR__ . "/webhook.log", date("c") . " - payment_temp data deleted for session: " . $session->id . " (user account created)" . PHP_EOL, FILE_APPEND);
}

// AFTER (Both guest and logged-in cleanup):
if (!$user_exists) {
    // For new users (guest purchases), delete payment_temp data since user account is now created
    $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ?");
    $cleanup_stmt->bind_param("s", $session->id);
    $cleanup_stmt->execute();
    file_put_contents(__DIR__ . "/webhook.log", date("c") . " - payment_temp data deleted for session: " . $session->id . " (guest user account created)" . PHP_EOL, FILE_APPEND);
} else {
    // For existing users (logged-in purchases), also delete payment_temp data after successful purchase
    $cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ? OR username = ?");
    $cleanup_stmt->bind_param("ss", $session->id, $username);
    $cleanup_stmt->execute();
    $deleted_rows = $cleanup_stmt->affected_rows;
    if ($deleted_rows > 0) {
        file_put_contents(__DIR__ . "/webhook.log", date("c") . " - payment_temp data deleted for logged-in user: $username (session: {$session->id}, rows: $deleted_rows)" . PHP_EOL, FILE_APPEND);
    }
}
');
echo "</pre>";
echo "</div>";

echo "<h3>📊 **Current Database Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check current payment_temp status
$total_query = "SELECT COUNT(*) as total FROM payment_temp";
$total_result = mysqli_query($conn, $total_query);
$total_count = mysqli_fetch_assoc($total_result)['total'];

// Check for logged-in user records (those with existing users)
$logged_in_query = "
    SELECT COUNT(*) as logged_in_count
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 0 OR pt.user_created IS NULL
";
$logged_in_result = mysqli_query($conn, $logged_in_query);
$logged_in_count = mysqli_fetch_assoc($logged_in_result)['logged_in_count'];

// Check for guest user records
$guest_query = "
    SELECT COUNT(*) as guest_count
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 1
";
$guest_result = mysqli_query($conn, $guest_query);
$guest_count = mysqli_fetch_assoc($guest_result)['guest_count'];

// Check recent records
$recent_query = "SELECT COUNT(*) as recent FROM payment_temp WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)";
$recent_result = mysqli_query($conn, $recent_query);
$recent_count = mysqli_fetch_assoc($recent_result)['recent_count'];

echo "<h4>Payment Temp Statistics:</h4>";
echo "<ul>";
echo "<li><strong>Total payment_temp records:</strong> $total_count</li>";
echo "<li><strong>Guest purchase records:</strong> $guest_count</li>";
echo "<li><strong>Logged-in purchase records:</strong> $logged_in_count</li>";
echo "<li><strong>Recent records (24h):</strong> $recent_count</li>";
echo "</ul>";

if ($logged_in_count > 0) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ <strong>Note:</strong> There are still $logged_in_count logged-in user payment_temp records. These should be cleaned up automatically after the next logged-in user purchase.</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>✅ <strong>Good:</strong> No logged-in user payment_temp records found. The cleanup is working!</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>🧪 **Testing the Fix**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>To test logged-in user cleanup:</h4>";
echo "<ol>";
echo "<li><strong>Login to your account</strong> → Make sure you're logged in</li>";
echo "<li><strong>Add tickets to cart</strong> → Go to cart page</li>";
echo "<li><strong>Check payment_temp before purchase:</strong></li>";
echo "<ul>";
echo "<li>Run: <code>SELECT * FROM payment_temp WHERE username = 'your_username'</code></li>";
echo "<li>Should show records if any exist</li>";
echo "</ul>";
echo "<li><strong>Complete a purchase</strong> → Go through Stripe checkout</li>";
echo "<li><strong>Wait for webhook processing</strong> → Check webhook.log</li>";
echo "<li><strong>Check payment_temp after purchase:</strong></li>";
echo "<ul>";
echo "<li>Run: <code>SELECT * FROM payment_temp WHERE username = 'your_username'</code></li>";
echo "<li>Should show NO records (cleaned up)</li>";
echo "</ul>";
echo "<li><strong>Check webhook.log for cleanup message:</strong></li>";
echo "<ul>";
echo "<li>Look for: <code>payment_temp data deleted for logged-in user: username</code></li>";
echo "</ul>";
echo "</ol>";

echo "<h4>Expected Webhook Log Messages:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px;'>";
echo "✅ For Guest Users:\n";
echo "payment_temp data deleted for session: cs_test_xxx (guest user account created)\n\n";
echo "✅ For Logged-In Users:\n";
echo "payment_temp data deleted for logged-in user: user31404 (session: cs_test_xxx, rows: 1)";
echo "</pre>";
echo "</div>";

echo "<h3>🔍 **Webhook Architecture**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Current Setup:</h4>";
echo "<ul>";
echo "<li><strong>Stripe Webhook URL:</strong> <code>/helloit/front-end/stripe-webhook.php</code></li>";
echo "<li><strong>Handles:</strong> Both guest and logged-in user purchases</li>";
echo "<li><strong>User Detection:</strong> Checks if email exists in user table</li>";
echo "<li><strong>Cleanup Logic:</strong> Different cleanup for guest vs logged-in users</li>";
echo "</ul>";

echo "<h4>Why This Approach Works:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Single webhook endpoint:</strong> Handles all purchase types</li>";
echo "<li>✅ <strong>User detection:</strong> Automatically determines guest vs logged-in</li>";
echo "<li>✅ <strong>Comprehensive cleanup:</strong> Cleans up both session_id and username records</li>";
echo "<li>✅ <strong>Detailed logging:</strong> Tracks all cleanup operations</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 **Summary**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>✅ Problem Solved!</h4>";
echo "<p style='color: #155724;'>The logged-in user payment_temp cleanup issue has been fixed by:</p>";
echo "<ul style='color: #155724;'>";
echo "<li><strong>Adding username to checkout session metadata</strong> for proper user identification</li>";
echo "<li><strong>Enhancing the webhook cleanup logic</strong> to handle both guest and logged-in users</li>";
echo "<li><strong>Using comprehensive deletion queries</strong> that clean up by session_id OR username</li>";
echo "<li><strong>Adding detailed logging</strong> to track cleanup operations</li>";
echo "</ul>";
echo "<p style='color: #155724; margin: 0;'><strong>Result:</strong> payment_temp data will now be automatically cleaned up for both guest purchases and logged-in user purchases immediately after successful webhook processing.</p>";
echo "</div>";

// Show recent webhook activity if available
if (file_exists('../front-end/webhook.log')) {
    echo "<h3>📝 **Recent Webhook Activity**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $log_content = file_get_contents('../front-end/webhook.log');
    $log_lines = array_slice(explode("\n", $log_content), -20); // Last 20 lines
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; max-height: 300px; overflow-y: auto;'>";
    foreach ($log_lines as $line) {
        if (trim($line)) {
            if (strpos($line, 'payment_temp data deleted for logged-in user') !== false) {
                echo "<span style='color: #28a745; font-weight: bold;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'payment_temp data deleted') !== false) {
                echo "<span style='color: #007bff;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'error') !== false || strpos($line, 'Error') !== false) {
                echo "<span style='color: #dc3545;'>" . htmlspecialchars($line) . "</span>\n";
            } else {
                echo htmlspecialchars($line) . "\n";
            }
        }
    }
    echo "</pre>";
    echo "<p><em>Green (bold) lines show logged-in user cleanup operations. Blue lines show guest user cleanup.</em></p>";
    echo "</div>";
}

echo "<h3>🔗 **Quick Access Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='cleanup-payment-temp.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Manual Cleanup Tool</a>";
echo "<a href='../front-end/cart.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Cart Purchase</a>";
echo "<a href='../front-end/sign-in.php' style='background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Login Page</a>";
echo "<a href='test-logged-in-cleanup.php' style='background: #fd7e14; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Original Test Page</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
