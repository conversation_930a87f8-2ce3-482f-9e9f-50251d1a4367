<?php
include('server.php');

echo "<h2>🔧 Final Fix: Guest User Card Saving</h2>";

echo "<h3>🎯 **Root Cause Identified**</h3>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
echo "<h4>The Real Problem:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Guest checkout sessions had NO setup_future_usage</strong></li>";
echo "<li>❌ <strong>Metadata was empty</strong> (no save_payment_method preference)</li>";
echo "<li>❌ <strong>Conditional logic was wrong</strong> - only added setup_future_usage if customer_id existed</li>";
echo "<li>❌ <strong>Guest users have no customer_id initially</strong> - <PERSON><PERSON> creates it during checkout</li>";
echo "</ul>";

echo "<h4>Evidence from Webhook Logs:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px;'>";
echo "Line 288: \"metadata\": {} ← EMPTY metadata\n";
echo "Line 736: \"setup_future_usage\": null ← NO setup_future_usage\n";
echo "Line 1421: \"setup_future_usage\": null ← Same issue";
echo "</pre>";
echo "</div>";

echo "<h3>✅ **Solution Implemented**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>Fixed in create-checkout-session.php:</h4>";
echo "<ol>";
echo "<li><strong>Moved setup_future_usage outside customer condition:</strong>";
echo "<ul>";
echo "<li>✅ Now applies to ALL users (guest and logged-in)</li>";
echo "<li>✅ Guest users get setup_future_usage when checkbox is checked</li>";
echo "<li>✅ Stripe will attach payment method to the customer it creates</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>Fixed Logic Flow:</strong>";
echo "<ul>";
echo "<li>✅ <strong>Before:</strong> Only add setup_future_usage if customer_id exists</li>";
echo "<li>✅ <strong>After:</strong> Add setup_future_usage if save_payment_method is true</li>";
echo "<li>✅ Works for both existing customers and new customers</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";

echo "<h4>Code Change:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// BEFORE (Broken):
if ($customer_id) {
    $session_params[\'customer\'] = $customer_id;
    
    if ($save_payment_method) {
        $session_params[\'payment_intent_data\'] = [
            \'setup_future_usage\' => \'off_session\'
        ];
    }
}

// AFTER (Fixed):
if ($customer_id) {
    $session_params[\'customer\'] = $customer_id;
}

// Works for both existing customers and new customers
if ($save_payment_method) {
    $session_params[\'payment_intent_data\'] = [
        \'setup_future_usage\' => \'off_session\'
    ];
}
');
echo "</pre>";
echo "</div>";

echo "<h3>🎯 **How It Works Now**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>Guest User Flow (Fixed):</h4>";
echo "<ol>";
echo "<li><strong>Cart Page:</strong> Guest checks 'Save my payment method' checkbox</li>";
echo "<li><strong>Checkout Session Creation:</strong>";
echo "<ul>";
echo "<li>save_payment_method = true (from checkbox)</li>";
echo "<li>setup_future_usage = 'off_session' added to session</li>";
echo "<li>save_payment_method added to metadata</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Stripe Processing:</strong>";
echo "<ul>";
echo "<li>Stripe creates new customer (customer_creation: 'if_required')</li>";
echo "<li>Processes payment with setup_future_usage</li>";
echo "<li>Automatically attaches payment method to new customer</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Webhook Processing:</strong>";
echo "<ul>";
echo "<li>Enhanced webhook processes guest purchase</li>";
echo "<li>Creates user account</li>";
echo "<li>Updates user with stripe_customer_id</li>";
echo "<li>Saves payment method to database</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Result:</strong> Payment method appears in Stripe dashboard and user's account</li>";
echo "</ol>";
echo "</div>";

echo "<h3>📋 **Before vs After Comparison**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Aspect</th>";
echo "<th style='padding: 10px;'>Before (Broken)</th>";
echo "<th style='padding: 10px;'>After (Fixed)</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Guest Checkout Session</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ No setup_future_usage<br>❌ Empty metadata</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ setup_future_usage: 'off_session'<br>✅ save_payment_method in metadata</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Stripe Payment Processing</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Payment method not attached to customer</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Payment method automatically attached</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Stripe Dashboard</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Customer exists but no payment methods</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Customer with attached payment methods</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>User Experience</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ Must re-enter card every time</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Can reuse saved payment methods</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Database Sync</strong></td>";
echo "<td style='padding: 10px; color: #dc3545;'>❌ No payment methods in database</td>";
echo "<td style='padding: 10px; color: #28a745;'>✅ Payment methods synced to database</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h3>🧪 **Testing Instructions**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Test Guest Card Saving (Should Work Now):</h4>";
echo "<ol>";
echo "<li><strong>Log out</strong> (or use incognito mode)</li>";
echo "<li><strong>Add items to cart</strong></li>";
echo "<li><strong>Keep checkbox checked</strong> (Save my payment method)</li>";
echo "<li><strong>Complete purchase</strong> with test card</li>";
echo "<li><strong>Check Stripe Dashboard:</strong>";
echo "<ul>";
echo "<li>Go to Customers section</li>";
echo "<li>Find the newly created customer by email</li>";
echo "<li>Check Payment methods tab</li>";
echo "<li><strong>Expected:</strong> Payment method should be attached</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Log in with new credentials</strong></li>";
echo "<li><strong>Go to Payment Methods page</strong></li>";
echo "<li><strong>Expected:</strong> Saved card should appear</li>";
echo "</ol>";

echo "<h4>Test Not Saving Card:</h4>";
echo "<ol>";
echo "<li><strong>Log out</strong> (or use incognito mode)</li>";
echo "<li><strong>Add items to cart</strong></li>";
echo "<li><strong>Uncheck the checkbox</strong> (Don't save payment method)</li>";
echo "<li><strong>Complete purchase</strong></li>";
echo "<li><strong>Expected:</strong> Customer created but no payment methods attached</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔍 **What to Look For**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>In Next Webhook Logs (front-end/webhook.log):</h4>";
echo "<ul>";
echo "<li>✅ <code>\"setup_future_usage\": \"off_session\"</code> (not null)</li>";
echo "<li>✅ <code>\"metadata\": {\"save_payment_method\": \"1\"}</code> (not empty)</li>";
echo "<li>✅ <code>\"customer\": \"cus_xxx\"</code> (customer created by Stripe)</li>";
echo "</ul>";

echo "<h4>In Stripe Dashboard:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Customers:</strong> New customer created with email</li>";
echo "<li>✅ <strong>Payment methods:</strong> Card attached to customer</li>";
echo "<li>✅ <strong>Payment intents:</strong> Shows setup_future_usage: off_session</li>";
echo "</ul>";

echo "<h4>In Database:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo "-- Check if payment methods were saved\n";
echo "SELECT pm.*, u.username, u.email, u.stripe_customer_id \n";
echo "FROM payment_methods pm \n";
echo "JOIN user u ON pm.user_id = u.id \n";
echo "WHERE pm.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)\n";
echo "ORDER BY pm.created_at DESC;";
echo "</pre>";
echo "</div>";

echo "<h3>🎉 **Expected Results**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #bee5eb;'>";
echo "<h4 style='color: #0c5460;'>✅ Success Indicators:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li>✅ <strong>Webhook Logs:</strong> setup_future_usage and metadata present</li>";
echo "<li>✅ <strong>Stripe Dashboard:</strong> Payment methods attached to guest customers</li>";
echo "<li>✅ <strong>Payment Methods Page:</strong> Saved cards appear for new users</li>";
echo "<li>✅ <strong>Future Purchases:</strong> Users can select saved payment methods</li>";
echo "<li>✅ <strong>User Choice Respected:</strong> Unchecking checkbox prevents saving</li>";
echo "</ul>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🚀 Final Fix Complete!</h4>";
echo "<p style='color: #155724; margin: 0;'>The root cause has been identified and fixed. Guest users should now be able to save their credit cards just like logged-in users. The setup_future_usage parameter is now properly added to checkout sessions regardless of whether the user has an existing customer ID.</p>";
echo "</div>";

echo "<h3>🔗 **Quick Test Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/cart.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Guest Purchase</a>";
echo "<a href='../front-end/payment-methods.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Payment Methods</a>";
echo "<a href='../front-end/webhook.log' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;' target='_blank'>View Webhook Logs</a>";
echo "</div>";

// Show recent webhook entries if available
if (file_exists(__DIR__ . '/../front-end/webhook.log')) {
    echo "<h3>📄 **Recent Webhook Log Entries**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    $log_content = file_get_contents(__DIR__ . '/../front-end/webhook.log');
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -10); // Last 10 lines
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; max-height: 200px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            // Highlight important parts
            if (strpos($line, 'setup_future_usage') !== false) {
                echo "<span style='background: #fff3cd;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'metadata') !== false) {
                echo "<span style='background: #d4edda;'>" . htmlspecialchars($line) . "</span>\n";
            } else {
                echo htmlspecialchars($line) . "\n";
            }
        }
    }
    echo "</pre>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
