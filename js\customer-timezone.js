/**
 * Customer Timezone Detection and Management
 * Automatically detects customer's timezone and sends to server
 */

class CustomerTimezone {
    constructor() {
        this.customerTimezone = null;
        this.init();
    }
    
    init() {
        // Detect timezone when page loads
        document.addEventListener('DOMContentLoaded', () => {
            this.detectAndSaveCustomerTimezone();
            this.updateCustomerTimeElements();
            this.startAutoUpdate();
        });
    }
    
    /**
     * Detect customer's timezone using browser API
     */
    detectCustomerTimezone() {
        try {
            // Modern browsers support Intl.DateTimeFormat
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            console.log('Customer timezone detected:', timezone);
            return timezone;
        } catch (error) {
            console.warn('Customer timezone detection failed:', error);
            // Fallback to offset-based detection
            return this.detectTimezoneByOffset();
        }
    }
    
    /**
     * Fallback timezone detection using UTC offset
     */
    detectTimezoneByOffset() {
        const offset = new Date().getTimezoneOffset();
        const offsetHours = -offset / 60;
        
        // Common timezone mappings for customers
        const customerTimezoneMap = {
            8: 'Asia/Singapore',
            7: 'Asia/Bangkok',
            9: 'Asia/Tokyo',
            5.5: 'Asia/Kolkata',
            -5: 'America/New_York',
            -8: 'America/Los_Angeles',
            0: 'Europe/London',
            1: 'Europe/Paris'
        };
        
        return customerTimezoneMap[offsetHours] || 'Asia/Singapore';
    }
    
    /**
     * Detect and save customer timezone to server
     */
    async detectAndSaveCustomerTimezone() {
        try {
            this.customerTimezone = this.detectCustomerTimezone();
            
            // Save to localStorage for quick access
            localStorage.setItem('customerTimezone', this.customerTimezone);
            
            // Send to server to save in session/database
            const response = await fetch('save-user-timezone.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    timezone: this.customerTimezone
                })
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log('Customer timezone saved to server:', result);
                
                // Trigger custom event for other scripts
                document.dispatchEvent(new CustomEvent('customerTimezoneDetected', {
                    detail: { timezone: this.customerTimezone }
                }));
            } else {
                console.warn('Failed to save customer timezone to server');
            }
            
        } catch (error) {
            console.error('Error saving customer timezone:', error);
            // Use fallback timezone
            this.customerTimezone = 'Asia/Singapore';
            localStorage.setItem('customerTimezone', this.customerTimezone);
        }
    }
    
    /**
     * Get customer's timezone
     */
    getCustomerTimezone() {
        return this.customerTimezone || localStorage.getItem('customerTimezone') || 'Asia/Singapore';
    }
    
    /**
     * Convert UTC time to customer's local time
     */
    convertUTCToCustomerTime(utcTimeString) {
        try {
            // Parse UTC time
            const utcDate = new Date(utcTimeString + 'Z'); // Add Z to indicate UTC
            
            // Convert to customer's timezone
            const customerTime = utcDate.toLocaleString('en-CA', {
                timeZone: this.getCustomerTimezone(),
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            
            return customerTime.replace(',', '');
        } catch (error) {
            console.error('Customer time conversion error:', error);
            return utcTimeString; // Return original if conversion fails
        }
    }
    
    /**
     * Format time for customer display
     */
    formatCustomerTime(utcTimeString, options = {}) {
        try {
            const utcDate = new Date(utcTimeString + 'Z');
            const timezone = this.getCustomerTimezone();
            
            const defaultOptions = {
                timeZone: timezone,
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            };
            
            const formatOptions = { ...defaultOptions, ...options };
            
            return utcDate.toLocaleString('en-US', formatOptions);
        } catch (error) {
            console.error('Customer time formatting error:', error);
            return utcTimeString;
        }
    }
    
    /**
     * Get relative time for customers (e.g., "2 hours ago")
     */
    getCustomerRelativeTime(utcTimeString) {
        try {
            const utcDate = new Date(utcTimeString + 'Z');
            const now = new Date();
            
            const diffMs = now - utcDate;
            const diffSeconds = Math.floor(diffMs / 1000);
            const diffMinutes = Math.floor(diffSeconds / 60);
            const diffHours = Math.floor(diffMinutes / 60);
            const diffDays = Math.floor(diffHours / 24);
            
            if (diffDays > 0) {
                return diffDays + ' day' + (diffDays > 1 ? 's' : '') + ' ago';
            } else if (diffHours > 0) {
                return diffHours + ' hour' + (diffHours > 1 ? 's' : '') + ' ago';
            } else if (diffMinutes > 0) {
                return diffMinutes + ' minute' + (diffMinutes > 1 ? 's' : '') + ' ago';
            } else {
                return 'Just now';
            }
        } catch (error) {
            return this.formatCustomerTime(utcTimeString);
        }
    }
    
    /**
     * Update all customer time elements on the page
     */
    updateCustomerTimeElements() {
        // Update elements with class 'customer-time'
        const timeElements = document.querySelectorAll('.customer-time');
        
        timeElements.forEach(element => {
            const utcTime = element.dataset.utcTime || element.textContent;
            if (utcTime && utcTime.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
                const customerTime = this.convertUTCToCustomerTime(utcTime);
                element.textContent = customerTime;
                element.title = `UTC: ${utcTime} | Your time: ${customerTime}`;
            }
        });
        
        // Update elements with class 'customer-time-formatted'
        const formattedElements = document.querySelectorAll('.customer-time-formatted');
        
        formattedElements.forEach(element => {
            const utcTime = element.dataset.utcTime || element.textContent;
            if (utcTime && utcTime.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
                const formattedTime = this.formatCustomerTime(utcTime);
                element.textContent = formattedTime;
                element.title = `UTC: ${utcTime} | Your time: ${formattedTime}`;
            }
        });
        
        // Update elements with class 'customer-time-relative'
        const relativeElements = document.querySelectorAll('.customer-time-relative');
        
        relativeElements.forEach(element => {
            const utcTime = element.dataset.utcTime || element.textContent;
            if (utcTime && utcTime.match(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/)) {
                const relativeTime = this.getCustomerRelativeTime(utcTime);
                element.textContent = relativeTime;
                element.title = `UTC: ${utcTime}`;
            }
        });
    }
    
    /**
     * Start automatic updates for relative times
     */
    startAutoUpdate() {
        // Update relative times every minute
        setInterval(() => {
            this.updateCustomerTimeElements();
        }, 60000);
        
        // Initial update after 1 second
        setTimeout(() => {
            this.updateCustomerTimeElements();
        }, 1000);
    }
    
    /**
     * Get customer's current local time
     */
    getCustomerCurrentTime() {
        const timezone = this.getCustomerTimezone();
        const now = new Date();
        return now.toLocaleString('en-CA', {
            timeZone: timezone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }).replace(',', '');
    }
    
    /**
     * Display customer timezone info
     */
    getCustomerTimezoneInfo() {
        const timezone = this.getCustomerTimezone();
        const now = new Date();
        const offset = now.toLocaleString('en-US', {
            timeZone: timezone,
            timeZoneName: 'short'
        }).split(' ').pop();
        
        return {
            timezone: timezone,
            offset: offset,
            currentTime: this.getCustomerCurrentTime()
        };
    }
}

// Initialize customer timezone manager
const customerTimezone = new CustomerTimezone();

// Make it globally available for customer pages
window.CustomerTimezone = customerTimezone;

// Helper functions for customer pages
window.getCustomerTimezone = () => customerTimezone.getCustomerTimezone();
window.convertUTCToCustomerTime = (utcTime) => customerTimezone.convertUTCToCustomerTime(utcTime);
window.formatCustomerTime = (utcTime, options) => customerTimezone.formatCustomerTime(utcTime, options);
window.getCustomerRelativeTime = (utcTime) => customerTimezone.getCustomerRelativeTime(utcTime);
window.getCustomerCurrentTime = () => customerTimezone.getCustomerCurrentTime();

// Auto-update time elements when timezone is detected
document.addEventListener('customerTimezoneDetected', () => {
    customerTimezone.updateCustomerTimeElements();
});

console.log('Customer Timezone System Loaded - Ready for customer time display');
