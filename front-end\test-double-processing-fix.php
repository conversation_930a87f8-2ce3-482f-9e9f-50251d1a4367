<?php
/**
 * Test Double Processing Fix
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Double Processing Fix Test</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Double Processing Issue Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ The Problem:</h4>";
echo "<ul>";
echo "<li><strong>User purchased:</strong> XS (5 tickets) + S (10 tickets) = 15 tickets total</li>";
echo "<li><strong>But user got:</strong> 30 tickets (double)</li>";
echo "<li><strong>Purchase history:</strong> Showed double entries</li>";
echo "<li><strong>Root cause:</strong> Both simulate-webhook.php AND real webhook processed the same session</li>";
echo "</ul>";

echo "<h4>✅ The Solution:</h4>";
echo "<ul>";
echo "<li><strong>stripe-webhook.php:</strong> Checks if session already processed by simulate-webhook.php</li>";
echo "<li><strong>simulate-webhook.php:</strong> Checks if session already processed by real webhook</li>";
echo "<li><strong>Result:</strong> Only one processing happens, no duplicates</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>How the Fix Works:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Protection in stripe-webhook.php:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// Check if session already processed by simulate-webhook.php\n";
echo "\$check_processed = \$conn->prepare(\"SELECT COUNT(*) FROM payment_temp WHERE session_id = ? AND user_created = 1\");\n";
echo "if (\$already_processed > 0 && \$environment === 'localhost') {\n";
echo "    // Skip real webhook processing\n";
echo "    exit();\n";
echo "}";
echo "</pre>";

echo "<h4>🔧 Protection in simulate-webhook.php:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// Check if session already processed by real webhook\n";
echo "\$check_real_webhook = \$conn->prepare(\"SELECT COUNT(*) FROM purchasetickets WHERE transactionid = ?\");\n";
echo "if (\$already_processed_by_webhook > 0) {\n";
echo "    // Skip simulation processing\n";
echo "    return true;\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Processing Flow:</h3>";

if ($is_localhost) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Localhost Flow (Fixed):</h4>";
    echo "<ol>";
    echo "<li><strong>Purchase completed</strong> → Stripe redirects to payment-success.php</li>";
    echo "<li><strong>payment-success.php</strong> → Calls simulate-webhook.php</li>";
    echo "<li><strong>simulate-webhook.php</strong> → Checks if real webhook already processed → Processes tickets</li>";
    echo "<li><strong>Real webhook arrives</strong> → Checks if simulate-webhook already processed → SKIPS processing</li>";
    echo "<li><strong>Result:</strong> Tickets processed only once ✅</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>✅ Production Flow:</h4>";
    echo "<ol>";
    echo "<li><strong>Purchase completed</strong> → Stripe sends webhook + redirects</li>";
    echo "<li><strong>Real webhook</strong> → Processes tickets (no simulation)</li>";
    echo "<li><strong>payment-success.php</strong> → Shows results</li>";
    echo "<li><strong>Result:</strong> Tickets processed only once ✅</li>";
    echo "</ol>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🧪 To Test the Fix:</h4>";
echo "<ol>";
echo "<li><strong>Add items to cart:</strong> e.g., XS (5 tickets) + S (10 tickets)</li>";
echo "<li><strong>Complete purchase</strong> → Should process normally</li>";
echo "<li><strong>Check user tickets:</strong> Should show 15 tickets (not 30)</li>";
echo "<li><strong>Check purchase history:</strong> Should show single entry (not double)</li>";
echo "<li><strong>Check database:</strong> purchasetickets table should have one entry per item</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Database Checks:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📊 What to Check:</h4>";
echo "<ul>";
echo "<li><strong>user table:</strong> starter_tickets, premium_tickets, ultimate_tickets (correct totals)</li>";
echo "<li><strong>purchasetickets table:</strong> One entry per purchased item (no duplicates)</li>";
echo "<li><strong>payment_temp table:</strong> Should be cleaned up after processing</li>";
echo "</ul>";

echo "<h4>📝 Example Expected Results:</h4>";
echo "<ul>";
echo "<li><strong>Purchase:</strong> XS STARTER (5 tickets) + S STARTER (10 tickets)</li>";
echo "<li><strong>user.starter_tickets:</strong> 15 (not 30)</li>";
echo "<li><strong>purchasetickets entries:</strong> 2 rows (one for XS, one for S)</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Files Modified:</h3>";

$modified_files = [
    'stripe-webhook.php' => 'Added localhost protection against double processing',
    'simulate-webhook.php' => 'Added protection against real webhook double processing'
];

foreach ($modified_files as $file => $change) {
    echo "<p><strong>$file:</strong> $change</p>";
}

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>Correct ticket counts:</strong> User gets exactly what they purchased</li>";
echo "<li>✅ <strong>Single purchase history:</strong> No duplicate entries</li>";
echo "<li>✅ <strong>Localhost works:</strong> No double processing</li>";
echo "<li>✅ <strong>Production works:</strong> Normal webhook processing</li>";
echo "</ul>";
?>
