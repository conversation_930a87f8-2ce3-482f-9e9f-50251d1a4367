<?php
// Include database connection
include('../functions/server.php');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];

// Get user ID
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;

if (!$user_id) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'No user ID provided']);
    exit();
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Get messages for the selected user
$messages = [];
$messages_query = "SELECT cm.*,
                  CASE
                      WHEN cm.sender_type = 'admin' THEN a.username
                      WHEN cm.sender_type = 'user' THEN u.username
                  END as sender_name,
                  DATE_FORMAT(cm.created_at, '%M %d, %H:%i') as formatted_time
                  FROM chat_messages cm
                  LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                  LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                  WHERE ";

if ($ticket_id) {
    $messages_query .= "cm.ticket_id = $ticket_id";

    // Mark messages as read for this ticket
    $update_query = "UPDATE chat_messages
                    SET is_read = 1
                    WHERE ticket_id = $ticket_id
                    AND sender_type = 'user'
                    AND is_read = 0";
    mysqli_query($conn, $update_query);
} else {
    // Get all tickets for this user
    $tickets_query = "SELECT id FROM support_tickets WHERE user_id = $user_id";
    $tickets_result = mysqli_query($conn, $tickets_query);
    $ticket_ids = [];

    if ($tickets_result && mysqli_num_rows($tickets_result) > 0) {
        while ($ticket = mysqli_fetch_assoc($tickets_result)) {
            $ticket_ids[] = $ticket['id'];
        }
    }

    if (!empty($ticket_ids)) {
        $ticket_ids_str = implode(',', $ticket_ids);
        $messages_query .= "cm.ticket_id IN ($ticket_ids_str)";

        // Mark all messages from this user as read
        $update_query = "UPDATE chat_messages
                        SET is_read = 1
                        WHERE ticket_id IN ($ticket_ids_str)
                        AND sender_type = 'user'
                        AND is_read = 0";
        mysqli_query($conn, $update_query);
    } else {
        // Fallback if no tickets found
        $messages_query .= "(cm.sender_id = $user_id AND cm.sender_type = 'user')
                          OR (cm.sender_id = $admin_id AND cm.sender_type = 'admin' AND cm.ticket_id IN
                              (SELECT id FROM support_tickets WHERE user_id = $user_id))";

        // Mark all messages from this user as read
        $update_query = "UPDATE chat_messages
                        SET is_read = 1
                        WHERE sender_id = $user_id
                        AND sender_type = 'user'
                        AND is_read = 0";
        mysqli_query($conn, $update_query);
    }
}

$messages_query .= " ORDER BY cm.created_at ASC";
$messages_result = mysqli_query($conn, $messages_query);

if ($messages_result) {
    while ($message = mysqli_fetch_assoc($messages_result)) {
        $messages[] = $message;
    }
}

// Return messages as JSON
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'messages' => $messages
]);
