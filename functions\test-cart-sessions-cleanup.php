<?php
include('server.php');

echo "<h2>🧪 Test Cart Sessions Cleanup</h2>";

echo "<h3>📊 **Current Cart Sessions Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Get current cart sessions
$query = "SELECT * FROM cart_sessions ORDER BY created_at DESC LIMIT 10";
$result = mysqli_query($conn, $query);

if (mysqli_num_rows($result) > 0) {
    echo "<h4>Recent Cart Sessions:</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #e9ecef;'>";
    echo "<th style='padding: 6px;'>ID</th>";
    echo "<th style='padding: 6px;'>Session ID</th>";
    echo "<th style='padding: 6px;'>User ID</th>";
    echo "<th style='padding: 6px;'>Created</th>";
    echo "<th style='padding: 6px;'>Processed</th>";
    echo "<th style='padding: 6px;'>Data Size</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($result)) {
        $status_color = $row['processed_at'] ? 'background: #d4edda;' : 'background: #fff3cd;';
        echo "<tr style='$status_color'>";
        echo "<td style='padding: 6px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 6px; font-family: monospace;'>" . substr($row['session_id'], 0, 25) . "...</td>";
        echo "<td style='padding: 6px;'>" . ($row['user_id'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 6px;'>" . date('M j H:i', strtotime($row['created_at'])) . "</td>";
        echo "<td style='padding: 6px;'>" . ($row['processed_at'] ? date('M j H:i', strtotime($row['processed_at'])) : 'No') . "</td>";
        echo "<td style='padding: 6px;'>" . strlen($row['cart_data']) . " chars</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>✅ No cart sessions found - table is clean!</p>";
}

echo "</div>";

echo "<h3>🔧 **Test Cleanup Functions**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    switch ($action) {
        case 'create_test_session':
            // Create a test cart session
            $test_session_id = 'test_cart_' . uniqid() . '_' . time();
            $test_cart_data = json_encode([
                ['ticket_type' => 'STARTER', 'package_size' => 'XS', 'quantity' => 1, 'price' => 20]
            ]);
            $test_user_id = 1; // Test user
            
            $insert_query = "INSERT INTO cart_sessions (session_id, cart_data, user_id) VALUES (?, ?, ?)";
            $stmt = $conn->prepare($insert_query);
            $stmt->bind_param("ssi", $test_session_id, $test_cart_data, $test_user_id);
            
            if ($stmt->execute()) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                echo "<h4>✅ Test Session Created</h4>";
                echo "<p>Session ID: $test_session_id</p>";
                echo "<p>User ID: $test_user_id</p>";
                echo "<p>Data: $test_cart_data</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; color: #721c24;'>";
                echo "<h4>❌ Failed to Create Test Session</h4>";
                echo "<p>Error: " . $stmt->error . "</p>";
                echo "</div>";
            }
            break;
            
        case 'mark_processed':
            // Mark all unprocessed sessions as processed
            $update_query = "UPDATE cart_sessions SET processed_at = NOW() WHERE processed_at IS NULL";
            $result = mysqli_query($conn, $update_query);
            $affected = mysqli_affected_rows($conn);
            
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; color: #0c5460;'>";
            echo "<h4>ℹ️ Sessions Marked as Processed</h4>";
            echo "<p>Updated $affected sessions</p>";
            echo "</div>";
            break;
            
        case 'test_cleanup_processed':
            // Test cleanup of processed sessions
            $cleanup_query = "DELETE FROM cart_sessions WHERE processed_at IS NOT NULL";
            $result = mysqli_query($conn, $cleanup_query);
            $deleted = mysqli_affected_rows($conn);
            
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
            echo "<h4>✅ Processed Sessions Cleaned</h4>";
            echo "<p>Deleted $deleted processed sessions</p>";
            echo "</div>";
            break;
            
        case 'test_cleanup_specific':
            // Test cleanup of specific session
            $session_to_delete = $_POST['session_id'] ?? '';
            if ($session_to_delete) {
                $cleanup_query = "DELETE FROM cart_sessions WHERE session_id = ?";
                $stmt = $conn->prepare($cleanup_query);
                $stmt->bind_param("s", $session_to_delete);
                $stmt->execute();
                $deleted = $stmt->affected_rows;
                
                if ($deleted > 0) {
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724;'>";
                    echo "<h4>✅ Specific Session Deleted</h4>";
                    echo "<p>Deleted session: $session_to_delete</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; color: #856404;'>";
                    echo "<h4>⚠️ Session Not Found</h4>";
                    echo "<p>Session ID: $session_to_delete</p>";
                    echo "</div>";
                }
            }
            break;
    }
}

echo "<h4>Test Actions:</h4>";

// Create test session
echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>1. Create Test Cart Session</h5>";
echo "<p>Creates a sample cart session for testing cleanup</p>";
echo "<form method='POST' style='display: inline;'>";
echo "<input type='hidden' name='action' value='create_test_session'>";
echo "<button type='submit' style='background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Create Test Session</button>";
echo "</form>";
echo "</div>";

// Mark as processed
echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>2. Mark Sessions as Processed</h5>";
echo "<p>Marks all unprocessed sessions as processed (simulates completed payment)</p>";
echo "<form method='POST' style='display: inline;'>";
echo "<input type='hidden' name='action' value='mark_processed'>";
echo "<button type='submit' style='background: #17a2b8; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Mark as Processed</button>";
echo "</form>";
echo "</div>";

// Test cleanup processed
echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>3. Test Cleanup Processed Sessions</h5>";
echo "<p>Deletes all processed cart sessions (simulates webhook cleanup)</p>";
echo "<form method='POST' style='display: inline;'>";
echo "<input type='hidden' name='action' value='test_cleanup_processed'>";
echo "<button type='submit' style='background: #ffc107; color: black; padding: 8px 16px; border: none; border-radius: 4px;' onclick='return confirm(\"Delete all processed sessions?\")'>Test Cleanup</button>";
echo "</form>";
echo "</div>";

// Test specific session cleanup
echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
echo "<h5>4. Test Specific Session Cleanup</h5>";
echo "<p>Deletes a specific cart session by session_id</p>";
echo "<form method='POST' style='display: inline;'>";
echo "<input type='hidden' name='action' value='test_cleanup_specific'>";
echo "<input type='text' name='session_id' placeholder='Enter session_id' style='padding: 6px; margin-right: 10px; width: 200px;'>";
echo "<button type='submit' style='background: #dc3545; color: white; padding: 8px 16px; border: none; border-radius: 4px;'>Delete Specific</button>";
echo "</form>";
echo "</div>";

echo "</div>";

echo "<h3>📋 **Webhook Simulation Test**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

if (isset($_POST['simulate_webhook'])) {
    echo "<h4>🔄 Simulating Webhook Cleanup Process...</h4>";
    
    // Simulate the exact cleanup logic from webhook
    $test_cart_session_id = $_POST['test_cart_session_id'] ?? '';
    $test_user_id = $_POST['test_user_id'] ?? null;
    
    if ($test_cart_session_id) {
        $deleted_sessions = 0;
        
        // Simulate exact webhook cleanup logic
        $cleanup_specific_query = "DELETE FROM cart_sessions WHERE session_id = ?";
        $cleanup_specific_stmt = $conn->prepare($cleanup_specific_query);
        $cleanup_specific_stmt->bind_param("s", $test_cart_session_id);
        $cleanup_specific_stmt->execute();
        $deleted_specific = $cleanup_specific_stmt->affected_rows;
        $deleted_sessions += $deleted_specific;
        
        if ($deleted_specific > 0) {
            echo "<p>✅ Deleted specific cart session: $test_cart_session_id</p>";
        } else {
            echo "<p>⚠️ Cart session not found: $test_cart_session_id</p>";
        }
        
        // If user_id provided, also clean up processed sessions for that user
        if ($test_user_id) {
            $cleanup_user_query = "DELETE FROM cart_sessions WHERE user_id = ? AND processed_at IS NOT NULL";
            $cleanup_user_stmt = $conn->prepare($cleanup_user_query);
            $cleanup_user_stmt->bind_param("i", $test_user_id);
            $cleanup_user_stmt->execute();
            $deleted_user_sessions = $cleanup_user_stmt->affected_rows;
            $deleted_sessions += $deleted_user_sessions;
            
            if ($deleted_user_sessions > 0) {
                echo "<p>✅ Deleted $deleted_user_sessions additional processed sessions for user_id: $test_user_id</p>";
            }
        }
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; color: #155724; margin: 10px 0;'>";
        echo "<h5>📊 Webhook Simulation Results:</h5>";
        echo "<p><strong>Total sessions deleted:</strong> $deleted_sessions</p>";
        echo "<p><strong>Specific session cleanup:</strong> " . ($deleted_specific > 0 ? 'Success' : 'Not found') . "</p>";
        if ($test_user_id) {
            echo "<p><strong>User processed sessions cleanup:</strong> $deleted_user_sessions deleted</p>";
        }
        echo "</div>";
    }
}

echo "<h4>Simulate Webhook Cleanup:</h4>";
echo "<form method='POST'>";
echo "<input type='hidden' name='simulate_webhook' value='1'>";
echo "<p>";
echo "<label>Cart Session ID:</label><br>";
echo "<input type='text' name='test_cart_session_id' placeholder='cart_683b657e37dca_1747723070' style='padding: 6px; width: 300px;'>";
echo "</p>";
echo "<p>";
echo "<label>User ID (optional):</label><br>";
echo "<input type='number' name='test_user_id' placeholder='1' style='padding: 6px; width: 100px;'>";
echo "</p>";
echo "<button type='submit' style='background: #473BF0; color: white; padding: 10px 20px; border: none; border-radius: 5px;'>Simulate Webhook Cleanup</button>";
echo "</form>";

echo "</div>";

echo "<h3>💡 **How to Test the Fix**</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Testing Steps:</h4>";
echo "<ol>";
echo "<li><strong>Create Test Session:</strong> Click 'Create Test Session' to add sample data</li>";
echo "<li><strong>Mark as Processed:</strong> Simulate a completed payment</li>";
echo "<li><strong>Test Cleanup:</strong> Verify that processed sessions are deleted</li>";
echo "<li><strong>Real Test:</strong> Make an actual guest purchase and check if cart session is cleaned up</li>";
echo "</ol>";

echo "<h4>Expected Behavior:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Guest Users:</strong> Cart session should be deleted immediately after payment completion</li>";
echo "<li>✅ <strong>Logged-in Users:</strong> Processed cart sessions should be cleaned up</li>";
echo "<li>✅ <strong>Storage:</strong> No accumulation of processed cart session data</li>";
echo "<li>✅ <strong>Webhook Logs:</strong> Should show successful cart session cleanup</li>";
echo "</ul>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
input[type="text"], input[type="number"] { border: 1px solid #ddd; border-radius: 4px; }
</style>
