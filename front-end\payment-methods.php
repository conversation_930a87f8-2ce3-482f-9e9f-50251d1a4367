<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Payment Methods</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    .bg-default-2.pb-17.pb-md-29 {
        margin-top: -26px;
    }

    body {
        margin-top: -110px !important;

    }

    @media (max-width: 767px) {
        body {
            margin-top: -115px !important;
        }
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }

    /* Custom modal width */
    .custom-width-modal {
        max-width: 1200px;
        /* You can adjust this value as needed */
        width: 90%;
        /* This makes it responsive */
    }

    /* Make sure the modal content has enough space */
    .custom-width-modal .modal-content {
        padding: 20px;
    }

    /* Payment method card styling */
    .payment-method-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .payment-method-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .payment-method-card .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .payment-method-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #333;
    }

    .payment-method-card .card-body {
        color: #666;
    }

    .payment-method-card .card-footer {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        flex-wrap: wrap;
        gap: 8px;
    }

    .payment-method-card .btn-sm {
        padding: 5px 10px;
        font-size: 0.85rem;
        white-space: nowrap;
        margin-right: 0;
    }

    /* Responsive button layout for medium screens */
    @media (max-width: 768px) {
        .payment-method-card .card-footer {
            justify-content: center;
        }

        .payment-method-card .btn-sm {
            flex: 0 1 auto;
            min-width: 80px;
        }
    }

    .payment-method-icon {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }

    .add-payment-method {
        background-color: #f8f9fa;
        border: 2px dashed #ddd;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .add-payment-method:hover {
        border-color: #473BF0;
        background-color: #f0f0ff;
    }

    .add-payment-method i {
        font-size: 2rem;
        color: #473BF0;
        margin-bottom: 10px;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .custom-width-modal {
            max-width: 90%;
        }
    }

    @media (max-width: 576px) {
        .custom-width-modal {
            max-width: 95%;
            margin: 0.5rem auto;
        }

        .custom-width-modal .modal-content {
            padding: 15px;
        }

        .payment-method-card {
            padding: 15px;
        }

        /* Stack buttons vertically on mobile */
        .payment-method-card .card-footer {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .payment-method-card .card-footer .btn {
            margin-right: 0 !important;
            margin-bottom: 0;
            width: 100%;
            flex: none;
        }
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    /* Modal styling */
    #removeCardModal .modal-header {
        background-color: #dc3545;
        color: white;
    }

    #removeCardModal .close {
        color: white;
        opacity: 0.8;
    }

    #removeCardModal .close:hover {
        opacity: 1;
    }

    #removeCardModal .btn-danger {
        background-color: #dc3545;
        border-color: #dc3545;
    }

    #removeCardModal .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        color: white;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        background-color: #473BF0;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }

    span.badge.badge-success {
        color: white;
    }

    button.btn.btn-secondary {
        background-color: red;
        color: white;
    }

    /* Pagination Styling */
    .pagination-container {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .pagination {
        display: flex;
        justify-content: center;
        list-style: none;
        padding: 0;
    }

    .page-item {
        margin: 0 2px;
    }

    .page-link {
        color: #473BF0;
        background-color: #fff;
        border: 1px solid #dee2e6;
        padding: 0.375rem 0.75rem;
        border-radius: 0.25rem;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .page-link:hover {
        color: #3129b5;
        background-color: #e9ecef;
        border-color: #dee2e6;
        text-decoration: none;
    }

    .page-item.active .page-link {
        color: #fff;
        background-color: #473BF0;
        border-color: #473BF0;
    }

    .page-item.disabled .page-link {
        color: #6c757d;
        pointer-events: none;
        background-color: #fff;
        border-color: #dee2e6;
    }

    /* Edit button styling */
    .edit-payment-btn,
    .edit-stripe-payment-btn {
        background-color: #fff;
        border-color: #473BF0;
        color: #473BF0;
        transition: all 0.2s ease;
    }

    .edit-payment-btn:hover,
    .edit-stripe-payment-btn:hover {
        background-color: #473BF0;
        border-color: #473BF0;
        color: #fff;
    }

    .edit-payment-btn i,
    .edit-stripe-payment-btn i {
        margin-right: 5px;
    }

    /* Modal form styling */
    #editPaymentModal .form-control[readonly] {
        background-color: #f8f9fa;
        opacity: 1;
    }

    #editPaymentModal .alert-info {
        border-left: 4px solid #473BF0;
        background-color: #f0f0ff;
        border-color: #d1ecf1;
    }

    /* Card icon in modal */
    #editCardIcon img,
    #modalCardIcon img {
        max-height: 32px;
        width: auto;
    }

    /* Remove modal styling */
    #removeCardModal .alert-warning {
        border-left: 4px solid #ffc107;
        background-color: #fff3cd;
        border-color: #ffeaa7;
    }

    #removeCardModal .form-control[readonly] {
        background-color: #f8f9fa;
        opacity: 1;
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];
            $sqlshow = "SELECT id, username, email, starter_tickets, premium_tickets, ultimate_tickets, tell, company_name, tax_id, address, district, city, postal_code, country FROM user WHERE username = '$username'";
            $resultshow = mysqli_query($conn, $sqlshow);
            ?>
        <?php if ($resultshow && mysqli_num_rows($resultshow) > 0) :
                $user = mysqli_fetch_assoc($resultshow);
            ?>
        <!-- Display user data here -->
        <!-- Page Banner Area -->
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container"></div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">
                <div class="row justify-content-md-between pt-9">
                    <div class="col-12 text-center mb-8">
                        <!-- <h2 class="text-center mb-4"
                            style="font-size: 36px; color: #333; font-weight: 600; text-align: center;">💳 Payment
                            Methods</h2> -->
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                            <h2 class="text-center mb-4"
                                style="font-size: 36px; color: #333; font-weight: 600; text-align: center;">Payment
                                Methods</h2>
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h4 class="mb-3">Your Saved Payment Methods</h4>
                                    <p class="text-muted">Manage your payment methods for future purchases.</p>
                                </div>

                                <?php
                                // Get user's payment methods from database
                                $user_id = $user['id'];

                                // Get user's Stripe customer ID
                                $stripe_customer_id = '';
                                $user_stripe_query = "SELECT stripe_customer_id FROM user WHERE id = $user_id";
                                $user_stripe_result = mysqli_query($conn, $user_stripe_query);

                                if ($user_stripe_result && mysqli_num_rows($user_stripe_result) > 0) {
                                    $user_stripe_data = mysqli_fetch_assoc($user_stripe_result);
                                    $stripe_customer_id = $user_stripe_data['stripe_customer_id'];
                                }

                                // Get payment methods from database
                                $payment_methods_query = "SELECT * FROM payment_methods WHERE user_id = $user_id ORDER BY is_default DESC, created_at DESC";
                                $payment_methods_result = mysqli_query($conn, $payment_methods_query);

                                // Get payment methods directly from Stripe
                                $stripe_payment_methods = [];
                                if ($stripe_customer_id) {
                                    try {
                                        require_once '../vendor/autoload.php';
                                        \Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

                                        $stripe_payment_methods = \Stripe\PaymentMethod::all([
                                            'customer' => $stripe_customer_id,
                                            'type' => 'card',
                                        ]);
                                    } catch (Exception $e) {
                                        error_log("Error retrieving payment methods from Stripe: " . $e->getMessage());
                                    }
                                }

                                // Track displayed payment method IDs to avoid duplicates
                                $displayed_payment_method_ids = [];

                                // Display payment methods from database
                                $has_payment_methods = false;

                                if ($payment_methods_result && mysqli_num_rows($payment_methods_result) > 0) {
                                    $has_payment_methods = true;

                                    // Display each payment method
                                    while ($payment_method = mysqli_fetch_assoc($payment_methods_result)) {
                                        $card_brand = ucfirst($payment_method['card_brand']);
                                        $card_last4 = $payment_method['card_last4'];
                                        $card_exp_month = str_pad($payment_method['card_exp_month'], 2, '0', STR_PAD_LEFT);
                                        $card_exp_year = $payment_method['card_exp_year'];
                                        $is_default = $payment_method['is_default'];
                                        $payment_id = $payment_method['id'];
                                        $payment_method_id = $payment_method['payment_method_id'];

                                        // Add to displayed list
                                        $displayed_payment_method_ids[] = $payment_method_id;

                                        // Get card brand image
                                        $brand_image = '../image/card-logo/generic-card.svg'; // Default
                                        if ($card_brand == 'Visa') {
                                            $brand_image = '../image/card-logo/visa.webp';
                                        } else if ($card_brand == 'Mastercard') {
                                            $brand_image = '../image/card-logo/mastercard.svg';
                                        } else if ($card_brand == 'Maestro') {
                                            $brand_image = '../image/card-logo/Maestro.svg';
                                        } else if ($card_brand == 'American Express' || $card_brand == 'Amex') {
                                            $brand_image = '../image/card-logo/AE.svg';
                                        } else if ($card_brand == 'Discover') {
                                            $brand_image = '../image/card-logo/discover.png';
                                        } else if ($card_brand == 'Diners Club') {
                                            $brand_image = '../image/card-logo/diners.svg';
                                        } else if ($card_brand == 'Jcb') {
                                            $brand_image = '../image/card-logo/jcb.svg';
                                        } else if ($card_brand == 'UnionPay') {
                                            $brand_image = '../image/card-logo/unionpay.svg';
                                        } else if ($card_brand == 'BCcard') {
                                            $brand_image = '../image/card-logo/bccard.png';
                                        } else if ($card_brand == 'DinaCard') {
                                            $brand_image = '../image/card-logo/dinacard.svg';
                                        }
                                ?>
                                <!-- Credit Card -->
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="payment-method-card">
                                        <div class="card-header">
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $brand_image; ?>"
                                                    alt="<?php echo htmlspecialchars($card_brand); ?>"
                                                    class="payment-method-icon">
                                                <h5><?php echo htmlspecialchars($card_brand); ?> Card</h5>
                                            </div>
                                            <?php if ($is_default) : ?>
                                            <span class="badge badge-success">Default</span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Card Number:</strong> **** **** ****
                                                <?php echo htmlspecialchars($card_last4); ?></p>
                                            <p><strong>Expiry:</strong>
                                                <?php echo htmlspecialchars($card_exp_month . '/' . substr($card_exp_year, -2)); ?>
                                            </p>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-sm btn-outline-primary mr-2 edit-payment-btn"
                                                data-payment-id="<?php echo $payment_id; ?>"
                                                data-payment-method-id="<?php echo htmlspecialchars($payment_method_id); ?>"
                                                data-card-brand="<?php echo htmlspecialchars($card_brand); ?>"
                                                data-card-last4="<?php echo htmlspecialchars($card_last4); ?>"
                                                data-card-exp-month="<?php echo htmlspecialchars($card_exp_month); ?>"
                                                data-card-exp-year="<?php echo htmlspecialchars($card_exp_year); ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger mr-2"
                                                data-payment-id="<?php echo $payment_id; ?>"
                                                data-payment-method-id="<?php echo htmlspecialchars($payment_method_id); ?>">Remove</button>
                                            <?php if (!$is_default) : ?>
                                            <button class="btn btn-sm btn-primary make-default-btn"
                                                data-payment-id="<?php echo $payment_id; ?>">Make Default</button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                    }
                                }

                                // Display payment methods from Stripe that aren't in the database
                                if (!empty($stripe_payment_methods) && !empty($stripe_payment_methods->data)) {
                                    foreach ($stripe_payment_methods->data as $stripe_method) {
                                        // Skip if already displayed
                                        if (in_array($stripe_method->id, $displayed_payment_method_ids)) {
                                            continue;
                                        }

                                        $has_payment_methods = true;

                                        $card = $stripe_method->card;
                                        $card_brand = ucfirst($card->brand);
                                        $card_last4 = $card->last4;
                                        $card_exp_month = str_pad($card->exp_month, 2, '0', STR_PAD_LEFT);
                                        $card_exp_year = $card->exp_year;
                                        $payment_method_id = $stripe_method->id;

                                        // Get card brand image
                                        $brand_image = '../image/card-logo/generic-card.svg'; // Default
                                        if ($card_brand == 'Visa') {
                                            $brand_image = '../image/card-logo/visa.webp';
                                        } else if ($card_brand == 'Mastercard') {
                                            $brand_image = '../image/card-logo/mastercard.svg';
                                        } else if ($card_brand == 'Maestro') {
                                            $brand_image = '../image/card-logo/Maestro.svg';
                                        } else if ($card_brand == 'American Express' || $card_brand == 'Amex') {
                                            $brand_image = '../image/card-logo/AE.svg';
                                        } else if ($card_brand == 'Discover') {
                                            $brand_image = '../image/card-logo/discover.png';
                                        } else if ($card_brand == 'Diners Club') {
                                            $brand_image = '../image/card-logo/diners.svg';
                                        } else if ($card_brand == 'Jcb') {
                                            $brand_image = '../image/card-logo/jcb.svg';
                                        } else if ($card_brand == 'UnionPay') {
                                            $brand_image = '../image/card-logo/unionpay.svg';
                                        } else if ($card_brand == 'BCcard') {
                                            $brand_image = '../image/card-logo/bccard.png';
                                        } else if ($card_brand == 'DinaCard') {
                                            $brand_image = '../image/card-logo/dinacard.svg';
                                        }
                                ?>
                                <!-- Credit Card from Stripe (not in database) -->
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="payment-method-card">
                                        <div class="card-header">
                                            <div class="d-flex align-items-center">
                                                <img src="<?php echo $brand_image; ?>"
                                                    alt="<?php echo htmlspecialchars($card_brand); ?>"
                                                    class="payment-method-icon">
                                                <h5><?php echo htmlspecialchars($card_brand); ?> Card</h5>
                                            </div>
                                            <span class="badge badge-warning">Stripe Only</span>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Card Number:</strong> **** **** ****
                                                <?php echo htmlspecialchars($card_last4); ?></p>
                                            <p><strong>Expiry:</strong>
                                                <?php echo htmlspecialchars($card_exp_month . '/' . substr($card_exp_year, -2)); ?>
                                            </p>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-sm btn-outline-primary mr-2 edit-stripe-payment-btn"
                                                data-payment-method-id="<?php echo htmlspecialchars($payment_method_id); ?>"
                                                data-card-brand="<?php echo htmlspecialchars($card_brand); ?>"
                                                data-card-last4="<?php echo htmlspecialchars($card_last4); ?>"
                                                data-card-exp-month="<?php echo htmlspecialchars($card_exp_month); ?>"
                                                data-card-exp-year="<?php echo htmlspecialchars($card_exp_year); ?>">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger mr-2 stripe-remove-btn"
                                                data-payment-method-id="<?php echo htmlspecialchars($payment_method_id); ?>">Remove
                                                from Stripe</button>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                    }
                                }

                                // Display message if no payment methods found
                                if (!$has_payment_methods) {
                                    echo '<div class="col-12 mb-4"><p>You don\'t have any saved payment methods yet. When you make a purchase, you can choose to save your payment method for future use.</p></div>';
                                }
                                ?>

                                <!-- Add New Payment Method -->
                                <!-- <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="payment-method-card add-payment-method" data-toggle="modal"
                                        data-target="#addPaymentModal">
                                        <i class="fas fa-plus-circle"></i>
                                        <p>Add New Payment Method</p>
                                    </div>
                                </div> -->
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Edit Payment Method Modal -->
        <div class="modal fade" id="editPaymentModal" tabindex="-1" role="dialog"
            aria-labelledby="editPaymentModalLabel" aria-hidden="true">
            <div class="modal-dialog custom-width-modal" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="editPaymentModalLabel">Edit Payment Method</h5>
                        <button type="button" class="close" onclick="closeEditModal()" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note:</strong> You can only update the expiry date for security reasons. To change
                            other card details, please remove this card and add a new one.
                        </div>
                        <form id="editPaymentForm">
                            <input type="hidden" id="editPaymentId" name="payment_id">
                            <input type="hidden" id="editPaymentMethodId" name="payment_method_id">
                            <input type="hidden" id="editIsStripeOnly" name="is_stripe_only" value="0">

                            <div class="form-group">
                                <label>Card Brand</label>
                                <div class="d-flex align-items-center">
                                    <div id="editCardIcon" class="mr-3"></div>
                                    <input type="text" class="form-control" id="editCardBrand" readonly>
                                </div>
                            </div>

                            <div class="form-group">
                                <label>Card Number</label>
                                <input type="text" class="form-control" id="editCardNumber" readonly>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editExpiryMonth">Expiry Month</label>
                                        <select class="form-control" id="editExpiryMonth" name="exp_month">
                                            <option value="">Select Month</option>
                                            <option value="01">01 - January</option>
                                            <option value="02">02 - February</option>
                                            <option value="03">03 - March</option>
                                            <option value="04">04 - April</option>
                                            <option value="05">05 - May</option>
                                            <option value="06">06 - June</option>
                                            <option value="07">07 - July</option>
                                            <option value="08">08 - August</option>
                                            <option value="09">09 - September</option>
                                            <option value="10">10 - October</option>
                                            <option value="11">11 - November</option>
                                            <option value="12">12 - December</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="editExpiryYear">Expiry Year</label>
                                        <select class="form-control" id="editExpiryYear" name="exp_year">
                                            <option value="">Select Year</option>
                                            <?php
                                            $currentYear = date('Y');
                                            for ($i = $currentYear; $i <= $currentYear + 20; $i++) {
                                                echo "<option value='$i'>$i</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeEditModal()">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="updatePaymentMethod()">Update Payment
                            Method</button>
                    </div>
                </div>
            </div>
        </div>

        <?php else : ?>
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2>User data not found.</h2>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php else : ?>
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2>Please log in to view your payment methods.</h2>
                    <a href="../front-end/sign-in.php" class="btn btn-primary mt-3">Log In</a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Footer section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <script>
    // Function to handle editing a payment method
    function editPaymentMethod(paymentId, paymentMethodId, cardBrand, cardLast4, expMonth, expYear) {
        // Set hidden fields
        document.getElementById('editPaymentId').value = paymentId;
        document.getElementById('editPaymentMethodId').value = paymentMethodId;
        document.getElementById('editIsStripeOnly').value = '0';

        // Set card brand and icon
        document.getElementById('editCardBrand').value = cardBrand + ' Card';
        setCardIcon('editCardIcon', cardBrand);

        // Set card number (masked)
        document.getElementById('editCardNumber').value = '**** **** **** ' + cardLast4;

        // Set current expiry values
        document.getElementById('editExpiryMonth').value = expMonth.padStart(2, '0');
        document.getElementById('editExpiryYear').value = expYear;

        // Show the modal
        $('#editPaymentModal').modal('show');
    }

    // Function to handle editing a Stripe-only payment method
    function editStripePaymentMethod(paymentMethodId, cardBrand, cardLast4, expMonth, expYear) {
        // Set hidden fields
        document.getElementById('editPaymentId').value = '';
        document.getElementById('editPaymentMethodId').value = paymentMethodId;
        document.getElementById('editIsStripeOnly').value = '1';

        // Set card brand and icon
        document.getElementById('editCardBrand').value = cardBrand + ' Card';
        setCardIcon('editCardIcon', cardBrand);

        // Set card number (masked)
        document.getElementById('editCardNumber').value = '**** **** **** ' + cardLast4;

        // Set current expiry values
        document.getElementById('editExpiryMonth').value = expMonth.toString().padStart(2, '0');
        document.getElementById('editExpiryYear').value = expYear.toString();

        // Show the modal
        $('#editPaymentModal').modal('show');
    }

    // Function to set card icon
    function setCardIcon(elementId, cardBrand) {
        const iconElement = document.getElementById(elementId);
        iconElement.innerHTML = '';

        const brand = cardBrand.toLowerCase();
        let iconHtml = '';

        if (brand === 'visa') {
            iconHtml = '<img src="../image/card-logo/visa.webp" alt="Visa" style="height: 32px; width: auto;">';
        } else if (brand === 'mastercard') {
            iconHtml =
                '<img src="../image/card-logo/mastercard.svg" alt="Mastercard" style="height: 32px; width: auto;">';
        } else if (brand === 'amex' || brand === 'american express') {
            iconHtml =
                '<img src="../image/card-logo/AE.svg" alt="American Express" style="height: 32px; width: auto;">';
        } else if (brand === 'discover') {
            iconHtml = '<img src="../image/card-logo/discover.png" alt="Discover" style="height: 32px; width: auto;">';
        } else if (brand === 'diners club') {
            iconHtml = '<img src="../image/card-logo/diners.svg" alt="Diners Club" style="height: 32px; width: auto;">';
        } else if (brand === 'jcb') {
            iconHtml = '<img src="../image/card-logo/jcb.svg" alt="JCB" style="height: 32px; width: auto;">';
        } else if (brand === 'maestro') {
            iconHtml = '<img src="../image/card-logo/Maestro.svg" alt="Maestro" style="height: 32px; width: auto;">';
        } else if (brand === 'unionpay') {
            iconHtml = '<img src="../image/card-logo/unionpay.svg" alt="UnionPay" style="height: 32px; width: auto;">';
        } else if (brand === 'bccard') {
            iconHtml = '<img src="../image/card-logo/bccard.png" alt="BCcard" style="height: 32px; width: auto;">';
        } else if (brand === 'dinacard') {
            iconHtml = '<img src="../image/card-logo/dinacard.svg" alt="DinaCard" style="height: 32px; width: auto;">';
        } else {
            iconHtml =
                '<img src="../image/card-logo/generic-card.svg" alt="Credit Card" style="height: 32px; width: auto;">';
        }

        iconElement.innerHTML = iconHtml;
    }

    // Function to close edit modal
    function closeEditModal() {
        try {
            // Try multiple methods to ensure the modal closes
            $('#editPaymentModal').modal('hide');

            // Force removal of modal classes and elements
            $('#editPaymentModal').removeClass('in');
            $('#editPaymentModal').removeClass('show');
            $('#editPaymentModal').css('display', 'none');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');

            // Reset form
            document.getElementById('editPaymentForm').reset();

            console.log("Edit modal closed successfully");
        } catch (e) {
            console.error("Error closing edit modal:", e);
            // Force hide the modal
            document.getElementById('editPaymentModal').style.display = 'none';
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        }
    }

    // Function to update payment method
    function updatePaymentMethod() {
        const expMonth = document.getElementById('editExpiryMonth').value;
        const expYear = document.getElementById('editExpiryYear').value;
        const paymentId = document.getElementById('editPaymentId').value;
        const paymentMethodId = document.getElementById('editPaymentMethodId').value;
        const isStripeOnly = document.getElementById('editIsStripeOnly').value === '1';

        // Validate required fields
        if (!expMonth || !expYear) {
            showAlert('Please select both expiry month and year.', 'Validation Error', 'warning');
            return;
        }

        // Validate expiry date is not in the past
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth() + 1;

        if (parseInt(expYear) < currentYear || (parseInt(expYear) === currentYear && parseInt(expMonth) <
                currentMonth)) {
            showAlert('Expiry date cannot be in the past.', 'Validation Error', 'warning');
            return;
        }

        // Send update request
        $.ajax({
            url: '../functions/payment-methods-api.php',
            type: 'POST',
            data: {
                action: 'update',
                payment_id: paymentId,
                payment_method_id: paymentMethodId,
                exp_month: expMonth,
                exp_year: expYear,
                is_stripe_only: isStripeOnly ? '1' : '0'
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    closeEditModal();
                    showAlert(response.message, 'Success', 'success');

                    // Reload page after a short delay to reflect the changes
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert(response.message, 'Error', 'warning');
                }
            },
            error: function() {
                showAlert('An error occurred while updating the payment method. Please try again.', 'Error',
                    'warning');
            }
        });
    }

    // Variables to store payment method information for removal
    let currentPaymentId = null;
    let currentPaymentMethodId = null;
    let currentCardBrand = null;
    let currentCardLast4 = null;
    let isStripeOnly = false;

    // Function to show the remove card confirmation modal
    function showRemoveCardModal(paymentId, paymentMethodId, cardBrand, cardLast4, stripeOnly = false) {
        // Store the payment method information
        currentPaymentId = paymentId;
        currentPaymentMethodId = paymentMethodId;
        currentCardBrand = cardBrand;
        currentCardLast4 = cardLast4;
        isStripeOnly = stripeOnly;

        // Update the modal with card information
        document.getElementById('modalCardBrand').value = cardBrand + ' Card';
        document.getElementById('modalCardLast4').value = '**** **** **** ' + cardLast4;

        // Set card icon using the same function as edit modal
        setCardIcon('modalCardIcon', cardBrand);

        // Update modal title based on whether it's a Stripe-only card
        if (stripeOnly) {
            document.getElementById('removeCardModalLabel').textContent = 'Remove Card from Stripe';
        } else {
            document.getElementById('removeCardModalLabel').textContent = 'Remove Payment Method';
        }

        // Show the modal using jQuery (compatible with Bootstrap 4)
        $('#removeCardModal').modal('show');
    }

    // Function to handle removing a payment method
    function removePaymentMethod(paymentId, paymentMethodId) {
        // Get card information from the DOM
        const cardElement = document.querySelector(`[data-payment-id="${paymentId}"]`).closest('.payment-method-card');
        const cardBrand = cardElement.querySelector('.card-header h5').textContent.split(' ')[0];
        const cardLast4 = cardElement.querySelector('.card-body p:first-child').textContent.split('****')[1].trim();

        // Show the confirmation modal
        showRemoveCardModal(paymentId, paymentMethodId, cardBrand, cardLast4, false);
    }

    function makeDefaultPaymentMethod(paymentId) {
        // Send request to the server to make this payment method the default
        $.ajax({
            url: '../functions/payment-methods-api.php',
            type: 'POST',
            data: {
                action: 'make_default',
                payment_id: paymentId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert(response.message, 'Success', 'success');

                    // Reload page after a short delay to reflect the changes
                    setTimeout(function() {
                        location.reload();
                    }, 2000);
                } else {
                    showAlert(response.message, 'Error', 'warning');
                }
            },
            error: function() {
                showAlert('An error occurred while updating the default payment method. Please try again.',
                    'Error', 'warning');
            }
        });
    }

    function showAlert(message, title = 'Warning', type = 'warning') {
        var alertElement = document.getElementById('customAlert');
        var alertOverlay = document.getElementById('alertOverlay');
        var alertTitle = document.getElementById('alertTitle');
        var alertMessage = document.getElementById('alertMessage');

        // Set the title and message
        alertTitle.textContent = title;
        alertMessage.textContent = message;

        // Change header color based on type
        var headerElement = alertElement.querySelector('.custom-alert-header');
        if (type === 'success') {
            headerElement.style.backgroundColor = '#d4edda';
            headerElement.style.color = '#155724';
        } else if (type === 'warning') {
            headerElement.style.backgroundColor = '#f8d7da';
            headerElement.style.color = '#721c24';
        }

        // Show the alert
        alertElement.classList.add('show');
        alertOverlay.classList.add('show');
    }

    function closeAlert() {
        var alertElement = document.getElementById('customAlert');
        var alertOverlay = document.getElementById('alertOverlay');

        alertElement.classList.remove('show');
        alertOverlay.classList.remove('show');
    }

    // Function to handle removing a payment method directly from Stripe
    function removeStripePaymentMethod(paymentMethodId) {
        // Get card information from the DOM
        const cardElement = document.querySelector(`[data-payment-method-id="${paymentMethodId}"]`).closest(
            '.payment-method-card');
        const cardBrand = cardElement.querySelector('.card-header h5').textContent.split(' ')[0];
        const cardLast4 = cardElement.querySelector('.card-body p:first-child').textContent.split('****')[1].trim();

        // Show the confirmation modal
        showRemoveCardModal(null, paymentMethodId, cardBrand, cardLast4, true);
    }

    // Function to execute the removal after confirmation
    function executeRemoval() {
        // Hide the modal using our improved closeRemoveModal function
        closeRemoveModal();

        if (isStripeOnly) {
            // Remove from Stripe only
            $.ajax({
                url: '../functions/payment-methods-api.php',
                type: 'POST',
                data: {
                    action: 'remove_stripe',
                    payment_method_id: currentPaymentMethodId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'Success', 'success');

                        // Reload page after a short delay to reflect the changes
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        showAlert(response.message, 'Error', 'warning');
                    }
                },
                error: function() {
                    showAlert('An error occurred while removing the payment method. Please try again.',
                        'Error', 'warning');
                }
            });
        } else {
            // Remove from both database and Stripe
            $.ajax({
                url: '../functions/payment-methods-api.php',
                type: 'POST',
                data: {
                    action: 'remove',
                    payment_id: currentPaymentId,
                    payment_method_id: currentPaymentMethodId
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showAlert(response.message, 'Success', 'success');

                        // Reload page after a short delay to reflect the changes
                        setTimeout(function() {
                            location.reload();
                        }, 2000);
                    } else {
                        showAlert(response.message, 'Error', 'warning');
                    }
                },
                error: function() {
                    showAlert('An error occurred while removing the payment method. Please try again.',
                        'Error', 'warning');
                }
            });
        }
    }

    // Function to close remove modal
    function closeRemoveModal() {
        try {
            // Try multiple methods to ensure the modal closes
            $('#removeCardModal').modal('hide');

            // Force removal of modal classes and elements
            $('#removeCardModal').removeClass('in');
            $('#removeCardModal').removeClass('show');
            $('#removeCardModal').css('display', 'none');
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');

            console.log("Remove modal closed successfully");
        } catch (e) {
            console.error("Error closing remove modal:", e);
            // Force hide the modal
            document.getElementById('removeCardModal').style.display = 'none';
            $('.modal-backdrop').remove();
            $('body').removeClass('modal-open');
            $('body').css('padding-right', '');
        }
    }

    // Function to manually close the modal (legacy support)
    function closeModal() {
        closeRemoveModal();
    }

    // Add event listeners to all buttons when the page loads
    document.addEventListener('DOMContentLoaded', function() {
        // Add event listeners to edit buttons
        const editButtons = document.querySelectorAll('.edit-payment-btn');
        editButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const paymentId = this.getAttribute('data-payment-id');
                const paymentMethodId = this.getAttribute('data-payment-method-id');
                const cardBrand = this.getAttribute('data-card-brand');
                const cardLast4 = this.getAttribute('data-card-last4');
                const expMonth = this.getAttribute('data-card-exp-month');
                const expYear = this.getAttribute('data-card-exp-year');

                editPaymentMethod(paymentId, paymentMethodId, cardBrand, cardLast4, expMonth,
                    expYear);
            });
        });

        // Add event listeners to edit Stripe-only buttons
        const editStripeButtons = document.querySelectorAll('.edit-stripe-payment-btn');
        editStripeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const paymentMethodId = this.getAttribute('data-payment-method-id');
                const cardBrand = this.getAttribute('data-card-brand');
                const cardLast4 = this.getAttribute('data-card-last4');
                const expMonth = this.getAttribute('data-card-exp-month');
                const expYear = this.getAttribute('data-card-exp-year');

                editStripePaymentMethod(paymentMethodId, cardBrand, cardLast4, expMonth,
                    expYear);
            });
        });

        // Add event listeners to make default buttons
        const defaultButtons = document.querySelectorAll('.make-default-btn');
        defaultButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const paymentId = this.getAttribute('data-payment-id');
                makeDefaultPaymentMethod(paymentId);
            });
        });

        // Add event listeners to remove buttons
        const removeButtons = document.querySelectorAll(
            '.payment-method-card .btn-outline-danger:not(.stripe-remove-btn)');
        removeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const paymentId = this.getAttribute('data-payment-id');
                const paymentMethodId = this.getAttribute('data-payment-method-id');
                removePaymentMethod(paymentId, paymentMethodId);
            });
        });

        // Add event listeners to Stripe-only remove buttons
        const stripeRemoveButtons = document.querySelectorAll('.stripe-remove-btn');
        stripeRemoveButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const paymentMethodId = this.getAttribute('data-payment-method-id');
                removeStripePaymentMethod(paymentMethodId);
            });
        });

        // Add event listener to the confirm remove button in the modal
        const confirmRemoveBtn = document.getElementById('confirmRemoveBtn');
        if (confirmRemoveBtn) {
            confirmRemoveBtn.addEventListener('click', executeRemoval);
        }

        // Add event listener to the cancel button in the modal
        const cancelRemoveBtn = document.getElementById('cancelRemoveBtn');
        if (cancelRemoveBtn) {
            cancelRemoveBtn.addEventListener('click', closeRemoveModal);
        }

        // Add event listener to the close button in the modal header
        const closeModalBtn = document.querySelector('#removeCardModal .close');
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', closeRemoveModal);
        }

        // Add keyboard event listener for Escape key to close modals
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                // Check if edit modal is open
                if ($('#editPaymentModal').hasClass('show') || $('#editPaymentModal').is(':visible')) {
                    closeEditModal();
                }
                // Check if remove modal is open
                if ($('#removeCardModal').hasClass('show') || $('#removeCardModal').is(':visible')) {
                    closeRemoveModal();
                }
            }
        });

        // Add click outside modal to close edit modal
        $('#editPaymentModal').on('click', function(event) {
            if (event.target === this) {
                closeEditModal();
            }
        });

        // Add click outside modal to close remove modal
        $('#removeCardModal').on('click', function(event) {
            if (event.target === this) {
                closeRemoveModal();
            }
        });
    });
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>

    <!-- Remove Card Confirmation Modal -->
    <div class="modal fade" id="removeCardModal" tabindex="-1" role="dialog" aria-labelledby="removeCardModalLabel"
        aria-hidden="true">
        <div class="modal-dialog custom-width-modal" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="removeCardModalLabel" style="color: white;">Remove Payment Method</h5>
                    <button type="button" class="close" onclick="closeRemoveModal()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning:</strong> Are you sure you want to remove this payment method? This action
                        cannot be undone.
                    </div>

                    <div class="form-group">
                        <label>Card Brand</label>
                        <div class="d-flex align-items-center">
                            <div id="modalCardIcon" class="mr-3"></div>
                            <input type="text" class="form-control" id="modalCardBrand" readonly>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Card Number</label>
                        <input type="text" class="form-control" id="modalCardLast4" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="cancelRemoveBtn"
                        onclick="closeRemoveModal()">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmRemoveBtn">Remove Payment Method</button>
                </div>
            </div>
        </div>
    </div>
</body>

</html>