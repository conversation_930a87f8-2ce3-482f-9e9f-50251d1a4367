<?php
/**
 * Test Complete Purchase Processing Fix
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

echo "<h2>Complete Purchase Processing Fix</h2>";

// Check environment
$is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
echo "<p><strong>Environment:</strong> " . ($is_localhost ? 'Localhost (XAMPP)' : 'Production') . "</p>";

echo "<hr>";
echo "<h3>Issues Fixed:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>❌ Previous Problems:</h4>";
echo "<ul>";
echo "<li>✅ <strong>User created</strong> but got 0 tickets</li>";
echo "<li>✅ <strong>No purchase history</strong> saved</li>";
echo "<li>✅ <strong>No Stripe customer</strong> created</li>";
echo "<li>✅ <strong>buyprocess() function</strong> had ticket update code commented out</li>";
echo "<li>✅ <strong>Undefined variables</strong> causing PHP warnings</li>";
echo "</ul>";

echo "<h4>✅ Complete Solution Applied:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Replaced buyprocess()</strong> with webhook logic</li>";
echo "<li>✅ <strong>Ticket updates</strong> now work correctly</li>";
echo "<li>✅ <strong>Purchase history</strong> saved to database</li>";
echo "<li>✅ <strong>Stripe customer</strong> created automatically</li>";
echo "<li>✅ <strong>Transaction processing</strong> with rollback on errors</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>How the New Processing Works:</h3>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔧 Complete Purchase Flow:</h4>";
echo "<ol>";
echo "<li><strong>User Creation:</strong> Create account with credentials</li>";
echo "<li><strong>Get Line Items:</strong> Retrieve purchased items from Stripe</li>";
echo "<li><strong>Process Each Item:</strong>";
echo "<ul>";
echo "<li>Get product metadata (ticket_type, package_size, numbers_per_package)</li>";
echo "<li>Calculate total tickets (numbers_per_package × quantity)</li>";
echo "<li>Update user ticket count in database</li>";
echo "<li>Save purchase record to purchasetickets table</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Create Stripe Customer:</strong> Link user to Stripe for future purchases</li>";
echo "<li><strong>Transaction Commit:</strong> Save all changes or rollback on error</li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<h3>Database Updates:</h3>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📊 What Gets Updated:</h4>";
echo "<ul>";
echo "<li><strong>user table:</strong>";
echo "<ul>";
echo "<li>starter_tickets, premium_tickets, ultimate_tickets (ticket counts)</li>";
echo "<li>stripe_customer_id (for future purchases)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>purchasetickets table:</strong>";
echo "<ul>";
echo "<li>username, ticket_type, package_size</li>";
echo "<li>numbers_per_package, dollar_price_per_package</li>";
echo "<li>purchase_time, transactionid, remaining_tickets</li>";
echo "</ul>";
echo "</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Example Purchase Processing:</h3>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>📝 Example: XS STARTER (5 tickets) + S STARTER (10 tickets)</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "1. User created: user12345\n";
echo "2. Process XS STARTER:\n";
echo "   - numbers_per_package: 5\n";
echo "   - quantity: 1\n";
echo "   - total_tickets: 5\n";
echo "   - UPDATE user SET starter_tickets = starter_tickets + 5\n";
echo "   - INSERT INTO purchasetickets (...)\n\n";
echo "3. Process S STARTER:\n";
echo "   - numbers_per_package: 10\n";
echo "   - quantity: 1\n";
echo "   - total_tickets: 10\n";
echo "   - UPDATE user SET starter_tickets = starter_tickets + 10\n";
echo "   - INSERT INTO purchasetickets (...)\n\n";
echo "4. Final result:\n";
echo "   - user.starter_tickets: 15\n";
echo "   - purchasetickets: 2 records\n";
echo "   - Stripe customer created";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<h3>Error Handling:</h3>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🛡️ Transaction Safety:</h4>";
echo "<ul>";
echo "<li><strong>Begin transaction:</strong> All updates happen together</li>";
echo "<li><strong>Error detection:</strong> Any failure triggers rollback</li>";
echo "<li><strong>Duplicate prevention:</strong> Check if already processed</li>";
echo "<li><strong>Detailed logging:</strong> Track all operations</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Test Instructions:</h3>";

if ($is_localhost) {
    echo "<div style='background: #ffe6e6; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>🧪 Test Complete Purchase on Localhost:</h4>";
    echo "<ol>";
    echo "<li><strong>Add items to cart:</strong> e.g., XS + S packages</li>";
    echo "<li><strong>Complete purchase</strong> → Should process without errors</li>";
    echo "<li><strong>Check user tickets:</strong> Should show correct total (e.g., 15)</li>";
    echo "<li><strong>Check purchase history:</strong> Should show all purchased items</li>";
    echo "<li><strong>Check Stripe dashboard:</strong> Should show new customer</li>";
    echo "<li><strong>Check credentials:</strong> Should display and work for login</li>";
    echo "</ol>";
    echo "</div>";
} else {
    echo "<div style='background: #cce5ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h4>ℹ️ Production Environment:</h4>";
    echo "<p>Production uses webhook processing, but fallback logic is the same.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Database Verification:</h3>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 Check These Tables:</h4>";
echo "<ul>";
echo "<li><strong>user table:</strong>";
echo "<ul>";
echo "<li>Check starter_tickets, premium_tickets, ultimate_tickets</li>";
echo "<li>Check stripe_customer_id is populated</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>purchasetickets table:</strong>";
echo "<ul>";
echo "<li>Check entries exist for each purchased item</li>";
echo "<li>Check transactionid matches Stripe session</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>payment_temp table:</strong>";
echo "<ul>";
echo "<li>Should be cleaned up after processing</li>";
echo "</ul>";
echo "</li>";
echo "</ul>";
echo "</div>";

echo "<hr>";
echo "<h3>Expected Results:</h3>";

echo "<ul>";
echo "<li>✅ <strong>User gets correct tickets:</strong> Matches purchased packages</li>";
echo "<li>✅ <strong>Purchase history populated:</strong> Shows all items</li>";
echo "<li>✅ <strong>Stripe customer created:</strong> Ready for future purchases</li>";
echo "<li>✅ <strong>Credentials work:</strong> Login successful</li>";
echo "<li>✅ <strong>No PHP errors:</strong> Clean execution</li>";
echo "</ul>";

echo "<hr>";
echo "<h3>Summary:</h3>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<p><strong>Complete purchase processing is now fixed!</strong></p>";
echo "<ul>";
echo "<li><strong>Root cause:</strong> buyprocess() function was incomplete</li>";
echo "<li><strong>Solution:</strong> Replaced with full webhook logic</li>";
echo "<li><strong>Result:</strong> Complete purchase processing with tickets, history, and Stripe customer</li>";
echo "</ul>";
echo "<p><strong>Your localhost purchases should now work exactly like the webhook processing!</strong></p>";
echo "</div>";
?>
