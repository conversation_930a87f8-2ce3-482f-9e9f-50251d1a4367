<?php
include('server.php');

echo "<h2>🧹 Logged-In User Payment Temp Cleanup</h2>";

echo "<h3>✨ **New Feature Overview**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>What was implemented:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Automatic cleanup for logged-in users:</strong> Deletes payment_temp after successful purchase</li>";
echo "<li>✅ <strong>Enhanced webhook processing:</strong> Cleans up both session_id and username records</li>";
echo "<li>✅ <strong>Updated manual cleanup:</strong> Handles both guest and logged-in purchase cleanup</li>";
echo "<li>✅ <strong>Improved logging:</strong> Tracks cleanup operations for both user types</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔄 **How It Works**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Logged-In User Purchase Flow:</h4>";
echo "<ol>";
echo "<li><strong>User logs in</strong> → Adds tickets to cart</li>";
echo "<li><strong>Clicks 'New Purchase'</strong> → Creates payment_temp record</li>";
echo "<li><strong>Completes Stripe checkout</strong> → Webhook triggered</li>";
echo "<li><strong>Webhook processes purchase</strong> → Updates user tickets</li>";
echo "<li><strong>payment_temp data deleted</strong> automatically</li>";
echo "</ol>";

echo "<h4>Cleanup Logic:</h4>";
echo "<ul>";
echo "<li><strong>Session-based cleanup:</strong> Deletes by session_id (current purchase)</li>";
echo "<li><strong>Username-based cleanup:</strong> Deletes any old records for the user</li>";
echo "<li><strong>Comprehensive removal:</strong> Ensures no leftover data</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 **Technical Implementation**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Webhook Changes (processLoggedInUserPurchase):</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// After successful purchase processing:
$cleanup_stmt = $conn->prepare("DELETE FROM payment_temp WHERE session_id = ? OR username = ?");
$cleanup_stmt->bind_param("ss", $session_id, $username);
$cleanup_stmt->execute();
$deleted_rows = $cleanup_stmt->affected_rows;

// Log the cleanup operation
file_put_contents(__DIR__ . "/webhook.log", 
    date("c") . " - payment_temp data deleted for logged-in user: $username (session: $session_id, rows: $deleted_rows)" . PHP_EOL, 
    FILE_APPEND);
');
echo "</pre>";

echo "<h4>Manual Cleanup Enhancement:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Enhanced query to handle both guest and logged-in purchases:
SELECT pt.id, pt.session_id, pt.username, pt.email, pt.user_created, pt.created_at,
       u.id as user_id, u.registration_time,
       CASE 
           WHEN pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) 
           THEN "guest_purchase"
           WHEN pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR) 
           THEN "logged_in_purchase"
           ELSE "not_eligible"
       END as cleanup_reason
FROM payment_temp pt
INNER JOIN user u ON pt.username = u.username
WHERE (
    (pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
    OR 
    (pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
)
');
echo "</pre>";
echo "</div>";

echo "<h3>📊 **Current Database Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check current payment_temp status
$total_query = "SELECT COUNT(*) as total FROM payment_temp";
$total_result = mysqli_query($conn, $total_query);
$total_count = mysqli_fetch_assoc($total_result)['total'];

// Check guest purchase records
$guest_query = "
    SELECT COUNT(*) as guest_count
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 1
";
$guest_result = mysqli_query($conn, $guest_query);
$guest_count = mysqli_fetch_assoc($guest_result)['guest_count'];

// Check logged-in purchase records (estimated)
$logged_in_query = "
    SELECT COUNT(*) as logged_in_count
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 0 OR pt.user_created IS NULL
";
$logged_in_result = mysqli_query($conn, $logged_in_query);
$logged_in_count = mysqli_fetch_assoc($logged_in_result)['logged_in_count'];

// Check eligible for cleanup
$eligible_guest_query = "
    SELECT COUNT(*) as eligible_guest
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.user_created = 1 
    AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR)
";
$eligible_guest_result = mysqli_query($conn, $eligible_guest_query);
$eligible_guest_count = mysqli_fetch_assoc($eligible_guest_result)['eligible_guest'];

$eligible_logged_in_query = "
    SELECT COUNT(*) as eligible_logged_in
    FROM payment_temp pt
    INNER JOIN user u ON pt.username = u.username
    WHERE pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
    AND NOT (pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
";
$eligible_logged_in_result = mysqli_query($conn, $eligible_logged_in_query);
$eligible_logged_in_count = mysqli_fetch_assoc($eligible_logged_in_result)['eligible_logged_in'];

echo "<h4>Payment Temp Statistics:</h4>";
echo "<ul>";
echo "<li><strong>Total payment_temp records:</strong> $total_count</li>";
echo "<li><strong>Guest purchase records:</strong> $guest_count</li>";
echo "<li><strong>Logged-in purchase records:</strong> $logged_in_count</li>";
echo "<li><strong>Eligible for cleanup:</strong></li>";
echo "<ul>";
echo "<li><strong>Guest purchases (>1h old):</strong> $eligible_guest_count</li>";
echo "<li><strong>Logged-in purchases (>1h old):</strong> $eligible_logged_in_count</li>";
echo "</ul>";
echo "</ul>";

$total_eligible = $eligible_guest_count + $eligible_logged_in_count;
if ($total_eligible > 0) {
    echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p>⚠️ <strong>Note:</strong> There are $total_eligible payment_temp records that can be safely cleaned up ($eligible_guest_count guest + $eligible_logged_in_count logged-in).</p>";
    echo "</div>";
}
echo "</div>";

echo "<h3>🎯 **Benefits of This Implementation**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";
echo "<h4>For Logged-In Users:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Automatic cleanup:</strong> No manual intervention needed</li>";
echo "<li>✅ <strong>Immediate cleanup:</strong> Happens right after purchase completion</li>";
echo "<li>✅ <strong>Complete removal:</strong> Cleans both session and user records</li>";
echo "<li>✅ <strong>No data accumulation:</strong> Prevents payment_temp growth</li>";
echo "</ul>";

echo "<h4>For System Administration:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Reduced maintenance:</strong> Less manual cleanup needed</li>";
echo "<li>✅ <strong>Better performance:</strong> Smaller payment_temp table</li>";
echo "<li>✅ <strong>Improved security:</strong> Less sensitive data stored</li>";
echo "<li>✅ <strong>Clear logging:</strong> Track all cleanup operations</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 **Cleanup Comparison**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Purchase Type</th>";
echo "<th style='padding: 10px;'>Cleanup Trigger</th>";
echo "<th style='padding: 10px;'>Cleanup Timing</th>";
echo "<th style='padding: 10px;'>Cleanup Method</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Guest Purchase</strong></td>";
echo "<td style='padding: 10px;'>User account created</td>";
echo "<td style='padding: 10px;'>After webhook processing</td>";
echo "<td style='padding: 10px;'>DELETE by session_id</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Logged-In Purchase</strong></td>";
echo "<td style='padding: 10px;'>Purchase completed</td>";
echo "<td style='padding: 10px;'>After webhook processing</td>";
echo "<td style='padding: 10px;'>DELETE by session_id OR username</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>Manual Cleanup</strong></td>";
echo "<td style='padding: 10px;'>Admin action</td>";
echo "<td style='padding: 10px;'>Records >1 hour old</td>";
echo "<td style='padding: 10px;'>DELETE by criteria</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<h3>🧪 **Testing the Feature**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Test Logged-In User Cleanup:</h4>";
echo "<ol>";
echo "<li><strong>Log in to your account</strong></li>";
echo "<li><strong>Add tickets to cart</strong> → Check payment_temp (should have record)</li>";
echo "<li><strong>Complete purchase</strong> → Stripe checkout</li>";
echo "<li><strong>Wait for webhook</strong> → Check webhook.log for cleanup message</li>";
echo "<li><strong>Check payment_temp</strong> → Record should be deleted</li>";
echo "<li><strong>Verify tickets added</strong> → Check user account</li>";
echo "</ol>";

echo "<h4>Test Manual Cleanup:</h4>";
echo "<ol>";
echo "<li><strong>Go to cleanup tool</strong> → Check current status</li>";
echo "<li><strong>Look for logged-in records</strong> → Should show cleanup_reason</li>";
echo "<li><strong>Run dry run</strong> → Preview what would be cleaned</li>";
echo "<li><strong>Run actual cleanup</strong> → Clean up old records</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔗 **Quick Access Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='cleanup-payment-temp.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Manual Cleanup Tool</a>";
echo "<a href='../front-end/cart.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Cart Purchase</a>";
echo "<a href='../front-end/sign-in.php' style='background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Login Page</a>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🧹 Logged-In User Cleanup Complete!</h4>";
echo "<p style='color: #155724; margin: 0;'>The system now automatically cleans up payment_temp data for both guest and logged-in user purchases. Logged-in users will have their payment_temp records deleted immediately after successful purchase completion, maintaining database efficiency and security.</p>";
echo "</div>";

// Show recent webhook activity if available
if (file_exists('../webhook.log')) {
    echo "<h3>📝 **Recent Webhook Activity**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $log_content = file_get_contents('../webhook.log');
    $log_lines = array_slice(explode("\n", $log_content), -15); // Last 15 lines
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; max-height: 250px; overflow-y: auto;'>";
    foreach ($log_lines as $line) {
        if (trim($line)) {
            if (strpos($line, 'payment_temp data deleted for logged-in user') !== false) {
                echo "<span style='color: #28a745; font-weight: bold;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'payment_temp data deleted') !== false) {
                echo "<span style='color: #007bff;'>" . htmlspecialchars($line) . "</span>\n";
            } elseif (strpos($line, 'error') !== false || strpos($line, 'Error') !== false) {
                echo "<span style='color: #dc3545;'>" . htmlspecialchars($line) . "</span>\n";
            } else {
                echo htmlspecialchars($line) . "\n";
            }
        }
    }
    echo "</pre>";
    echo "<p><em>Green (bold) lines show logged-in user cleanup operations. Blue lines show guest user cleanup.</em></p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
