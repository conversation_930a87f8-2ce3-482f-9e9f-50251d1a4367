<?php
session_start();
include('../functions/server.php');

// Check if admin is logged in
if (!isset($_SESSION['admin_username'])) {
    header('location: admin-login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'];
$admin_id = $_SESSION['admin_id'];
$admin_role = $_SESSION['admin_role'];

// Get admin information
$admin_query = "SELECT * FROM admin_users WHERE id = $admin_id";
$admin_result = mysqli_query($conn, $admin_query);
$admin = mysqli_fetch_assoc($admin_result);

// Get counts for dashboard
$tickets_query = "SELECT COUNT(*) as total FROM support_tickets";
$tickets_result = mysqli_query($conn, $tickets_query);
$tickets_count = mysqli_fetch_assoc($tickets_result)['total'];

$open_tickets_query = "SELECT COUNT(*) as total FROM support_tickets WHERE status = 'open'";
$open_tickets_result = mysqli_query($conn, $open_tickets_query);
$open_tickets_count = mysqli_fetch_assoc($open_tickets_result)['total'];

$users_query = "SELECT COUNT(*) as total FROM user";
$users_result = mysqli_query($conn, $users_query);
$users_count = mysqli_fetch_assoc($users_result)['total'];

$purchases_query = "SELECT COUNT(*) as total FROM purchasetickets";
$purchases_result = mysqli_query($conn, $purchases_query);
$purchases_count = mysqli_fetch_assoc($purchases_result)['total'];

// Get recent tickets with pagination
$tickets_per_page = 10;
$tickets_page = isset($_GET['tickets_page']) ? (int)$_GET['tickets_page'] : 1;
if ($tickets_page < 1) $tickets_page = 1;
$tickets_offset = ($tickets_page - 1) * $tickets_per_page;

// Count total tickets for pagination
$tickets_count_query = "SELECT COUNT(*) as total FROM support_tickets";
$tickets_count_result = mysqli_query($conn, $tickets_count_query);
$total_tickets = mysqli_fetch_assoc($tickets_count_result)['total'];
$total_tickets_pages = ceil($total_tickets / $tickets_per_page);

// Get recent tickets with pagination
$recent_tickets_query = "SELECT st.*, u.username, st.ticket_type
                        FROM support_tickets st
                        JOIN user u ON st.user_id = u.id
                        ORDER BY st.created_at DESC LIMIT $tickets_per_page OFFSET $tickets_offset";
$recent_tickets_result = mysqli_query($conn, $recent_tickets_query);

// Get recent purchases with pagination
$purchases_per_page = 10;
$purchases_page = isset($_GET['purchases_page']) ? (int)$_GET['purchases_page'] : 1;
if ($purchases_page < 1) $purchases_page = 1;
$purchases_offset = ($purchases_page - 1) * $purchases_per_page;

// Count total purchases for pagination
$purchases_count_query = "SELECT COUNT(*) as total FROM purchasetickets";
$purchases_count_result = mysqli_query($conn, $purchases_count_query);
$total_purchases = mysqli_fetch_assoc($purchases_count_result)['total'];
$total_purchases_pages = ceil($total_purchases / $purchases_per_page);

// Get recent purchases with pagination
$recent_purchases_query = "SELECT * FROM purchasetickets ORDER BY purchase_time DESC LIMIT $purchases_per_page OFFSET $purchases_offset";
$recent_purchases_result = mysqli_query($conn, $recent_purchases_query);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../css/main.css">
    <style>
    body {
        background-color: #f8f9fa;
    }

    .admin-container {
        padding: 20px;
        max-width: 100%;
        overflow-x: hidden;
    }

    .admin-header {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        width: 100%;
    }

    .admin-header h1 {
        margin: 0;
        font-size: 24px;
        color: #333;
        transition: all 0.3s ease;
    }

    .admin-user {
        display: flex;
        align-items: center;
    }

    .user-dropdown {
        position: relative;
        display: inline-block;
    }

    .user-info {
        margin-right: 10px;
        font-size: 14px;
        cursor: pointer;
        display: flex;
        align-items: center;
    }

    .mobile-menu-toggle {
        margin-left: 5px;
        transition: transform 0.3s ease;
        display: none;
    }

    .mobile-menu-toggle.active {
        transform: rotate(180deg);
    }

    .dropdown-content {
        display: block;
    }

    /* Tablet Styles */
    @media (max-width: 991px) {
        .admin-header {
            padding: 12px 15px;
        }

        .admin-header h1 {
            font-size: 22px;
        }

        .user-info {
            font-size: 13px;
        }
    }

    /* Mobile Styles */
    @media (max-width: 767px) {
        .admin-header {
            flex-direction: column;
            align-items: flex-start;
            padding: 12px;
        }

        .admin-header h1 {
            font-size: 20px;
            margin-bottom: 10px;
        }

        .admin-user {
            width: 100%;
        }

        .user-dropdown {
            width: 100%;
        }

        .user-info {
            font-size: 16px;
            padding: 8px 0;
            display: flex;
            justify-content: space-between;
            width: 100%;
        }

        .mobile-menu-toggle {
            display: inline-block;
        }

        .dropdown-content {
            display: none;
            position: absolute;
            background-color: #f9f9f9;
            width: 100%;
            left: 0;
            box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
            z-index: 1;
            border-radius: 4px;
            margin-top: 5px;
            padding: 8px;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease, padding 0.3s ease;
        }

        .dropdown-content .btn {
            width: 100%;
            text-align: center;
        }

        .user-dropdown.active .dropdown-content {
            display: block;
            max-height: 200px;
        }
    }

    /* Small Mobile Styles */
    @media (max-width: 480px) {
        .admin-header h1 {
            font-size: 22px;
        }
    }

    .admin-sidebar {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
    }

    .admin-sidebar ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .admin-sidebar ul li {
        margin-bottom: 10px;
    }

    .admin-sidebar ul li a {
        display: block;
        padding: 10px 15px;
        color: #333;
        text-decoration: none;
        border-radius: 5px;
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .admin-sidebar ul li a:hover,
    .admin-sidebar ul li a.active {
        background-color: #473BF0;
        color: #fff;
    }

    .admin-sidebar ul li a i {
        margin-right: 10px;
        width: 20px;
        text-align: center;
    }

    /* Responsive sidebar */
    @media (max-width: 991px) {
        .admin-sidebar {
            padding: 15px;
        }

        .admin-sidebar ul li a {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 767px) {
        .admin-sidebar {
            height: auto;
            margin-bottom: 20px;
        }

        .admin-sidebar ul li a {
            padding: 8px 10px;
            font-size: 13px;
        }

        .admin-sidebar ul li {
            margin-bottom: 5px;
        }
    }

    .admin-content {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        height: calc(100vh - 100px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .dashboard-card {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
        width: 100%;
    }

    /* Responsive dashboard cards */
    @media (max-width: 991px) {
        .dashboard-card {
            padding: 15px;
        }

        .dashboard-card .number {
            font-size: 30px !important;
        }
    }

    @media (max-width: 767px) {
        .dashboard-card {
            padding: 12px;
            margin-bottom: 15px;
        }

        .dashboard-card .number {
            font-size: 24px !important;
            margin: 8px 0;
        }

        .dashboard-card h3 {
            font-size: 14px !important;
        }

        .dashboard-card .btn {
            font-size: 12px;
            padding: 0.25rem 0.5rem;
        }
    }

    .dashboard-card h3 {
        margin: 0;
        font-size: 16px;
        color: #666;
    }

    .dashboard-card .number {
        font-size: 36px;
        font-weight: 600;
        color: #473BF0;
        margin: 10px 0;
    }

    .dashboard-card.tickets {
        border-top: 3px solid #473BF0;
    }

    .dashboard-card.open-tickets {
        border-top: 3px solid #4B0082;
    }

    .dashboard-card.users {
        border-top: 3px solid #014421;
    }

    .dashboard-card.purchases {
        border-top: 3px solid #dc3545;
    }

    .recent-section {
        margin-top: 30px;
    }

    .recent-section h2 {
        font-size: 20px;
        margin-bottom: 15px;
        color: #333;
    }

    .table th {
        background-color: #f8f9fa;
    }

    .table-responsive {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        margin-bottom: 15px;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
    }

    /* Table cell styles */
    .table td,
    .table th {
        vertical-align: middle;
        padding: 0.75rem;
    }

    /* Subject column styles */
    .table td:nth-child(2) {
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .table td:nth-child(2) a {
        display: block;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .badge {
        font-size: 16px;
        padding: 6.8px;
    }

    /* Responsive table styles */
    @media (max-width: 991px) {
        .table {
            font-size: 14px;
        }

        .table td,
        .table th {
            padding: 0.5rem;
        }

        .badge {
            font-size: 14px;
            padding: 5px;
        }
    }

    @media (max-width: 767px) {
        .table {
            font-size: 13px;
        }

        .table td,
        .table th {
            padding: 0.4rem 0.3rem;
        }

        .badge {
            font-size: 12px;
            padding: 4px;
        }

        .table-responsive {
            margin: 0 -15px;
            width: calc(100% + 30px);
            max-width: none;
        }
    }

    @media (max-width: 480px) {
        .table {
            font-size: 12px;
        }

        .table td,
        .table th {
            padding: 0.3rem 0.2rem;
        }

        .pagination {
            font-size: 12px;
        }

        .pagination .page-link {
            padding: 0.25rem 0.5rem;
        }

        .recent-section h2 {
            font-size: 18px;
        }
    }

    /* Pagination styles */
    .pagination {
        margin-top: 15px;
    }

    .pagination .page-link {
        color: #473BF0;
        border-color: #dee2e6;
    }

    .pagination .page-item.active .page-link {
        background-color: #473BF0;
        border-color: #473BF0;
    }

    @media (max-width: 767px) {
        .pagination {
            font-size: 14px;
        }

        .pagination .page-link {
            padding: 0.3rem 0.6rem;
        }
    }

    /* Ticket Status badges */
    .badge-open {
        background-color: #4CAF50;
        color: #000;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket Log Status badges */
    .badge-success {
        background-color: #28a745;
        color: #fff;
    }

    .badge-pending {
        background-color: #ffc107;
        color: #212529;
    }

    .badge-fail {
        background-color: #dc3545;
        color: #fff;
    }

    .badge-cancel {
        background-color: #6c757d;
        color: #fff;
    }

    /* Ticket Type badges */
    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #000;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }
    </style>
</head>

<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1>Admin Dashboard</h1>
            <div class="admin-user">
                <div class="user-dropdown">
                    <span class="user-info">
                        Welcome, <?php echo htmlspecialchars($admin_username); ?>
                        (<?php echo htmlspecialchars($admin_role); ?>)
                        <i class="mobile-menu-toggle fas fa-chevron-down"></i>
                    </span>
                    <div class="dropdown-content">
                        <a href="admin-logout.php" class="btn btn-sm btn-outline-danger">Logout</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-2 col-md-3 col-sm-12">
                <?php include('admin-menu.php'); ?>
            </div>

            <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Mobile menu functionality
                const initAdminMenu = function() {
                    if (window.innerWidth <= 767) {
                        const menuTitle = document.querySelector('.admin-sidebar .menu-title');
                        const menuToggle = document.querySelector('.mobile-menu-toggle');
                        const menuItems = document.querySelector('.admin-sidebar .menu-items');

                        if (!menuTitle || !menuToggle || !menuItems) return;

                        // Check if menu should be expanded by default (if there's an active item)
                        const hasActiveItem = document.querySelector('.admin-sidebar .menu-items a.active');

                        if (hasActiveItem) {
                            menuItems.classList.add('expanded');
                            menuToggle.classList.add('active');
                        }

                        // Remove existing event listener to prevent duplicates
                        menuTitle.onclick = null;

                        // Add click event
                        menuTitle.addEventListener('click', function() {
                            menuItems.classList.toggle('expanded');
                            menuToggle.classList.toggle('active');
                        });
                    }
                };

                // Run on page load
                initAdminMenu();

                // Also run on window resize
                window.addEventListener('resize', function() {
                    initAdminMenu();
                });
            });
            </script>

            <div class="col-lg-10 col-md-9 col-sm-12">
                <div class="admin-content">
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="dashboard-card tickets">
                                <h3>Total Tickets</h3>
                                <div class="number"><?php echo $tickets_count; ?></div>
                                <a href="admin-tickets.php" class="btn btn-sm btn-primary">View All</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="dashboard-card open-tickets">
                                <h3>Open Tickets</h3>
                                <div class="number"><?php echo $open_tickets_count; ?></div>
                                <a href="admin-tickets.php?status=open" class="btn btn-sm btn-success"
                                    style="background-color: #4B0082; color: #fff; border-color: #4B0082;">View
                                    Tickets</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="dashboard-card users">
                                <h3>Total Users</h3>
                                <div class="number"><?php echo $users_count; ?></div>
                                <a href="admin-users" class="btn btn-sm btn-warning"
                                    style="background-color: #014421; color: #fff; border-color: #014421;">View
                                    Users</a>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="dashboard-card purchases">
                                <h3>Total Purchases</h3>
                                <div class="number"><?php echo $purchases_count; ?></div>
                                <a href="admin-purchases.php" class="btn btn-sm btn-danger">View Purchases</a>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-md-12">
                            <div class="recent-section">
                                <h2>Recent Tickets</h2>
                                <div class="table-responsive">
                                    <table class="table table-striped tickets-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">ID</th>
                                                <th style="width: 35%;">Subject</th>
                                                <th style="width: 15%;">User</th>
                                                <th style="width: 15%;">Type</th>
                                                <th style="width: 15%;">Status</th>
                                                <th style="width: 15%;">Created</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (mysqli_num_rows($recent_tickets_result) > 0): ?>
                                            <?php while ($ticket = mysqli_fetch_assoc($recent_tickets_result)): ?>
                                            <tr>
                                                <td><?php echo $ticket['id']; ?></td>
                                                <td><a
                                                        href="admin-ticket-detail.php?id=<?php echo $ticket['id']; ?>"><?php echo htmlspecialchars($ticket['subject']); ?></a>
                                                </td>
                                                <td><?php echo htmlspecialchars($ticket['username']); ?></td>
                                                <td>
                                                    <?php
                                                            // Display 'Business' for premium tickets
                                                            $ticketType = $ticket['ticket_type'];
                                                            $badgeClass = strtolower($ticketType);
                                                            $displayText = ucfirst($ticketType);

                                                            if (strtolower($ticketType) == 'premium') {
                                                                $displayText = 'Business';
                                                            }
                                                            ?>
                                                    <span class="badge badge-<?php echo $badgeClass; ?>">
                                                        <?php echo $displayText; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge badge-<?php echo $ticket['status']; ?>">
                                                        <?php echo ucfirst($ticket['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($ticket['created_at'])); ?></td>
                                            </tr>
                                            <?php endwhile; ?>
                                            <?php else: ?>
                                            <tr>
                                                <td colspan="6" class="text-center">No tickets found</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>

                                    <?php if ($total_tickets_pages > 1): ?>
                                    <nav aria-label="Tickets pagination">
                                        <ul class="pagination justify-content-center mt-3">
                                            <li class="page-item <?php echo ($tickets_page <= 1) ? 'disabled' : ''; ?>">
                                                <a class="page-link"
                                                    href="?tickets_page=<?php echo $tickets_page - 1; ?><?php echo isset($_GET['purchases_page']) ? '&purchases_page=' . $_GET['purchases_page'] : ''; ?>"
                                                    aria-label="Previous">
                                                    <span aria-hidden="true">&laquo; Previous</span>
                                                </a>
                                            </li>

                                            <?php for ($i = 1; $i <= $total_tickets_pages; $i++): ?>
                                            <li class="page-item <?php echo ($tickets_page == $i) ? 'active' : ''; ?>">
                                                <a class="page-link"
                                                    href="?tickets_page=<?php echo $i; ?><?php echo isset($_GET['purchases_page']) ? '&purchases_page=' . $_GET['purchases_page'] : ''; ?>"><?php echo $i; ?></a>
                                            </li>
                                            <?php endfor; ?>

                                            <li
                                                class="page-item <?php echo ($tickets_page >= $total_tickets_pages) ? 'disabled' : ''; ?>">
                                                <a class="page-link"
                                                    href="?tickets_page=<?php echo $tickets_page + 1; ?><?php echo isset($_GET['purchases_page']) ? '&purchases_page=' . $_GET['purchases_page'] : ''; ?>"
                                                    aria-label="Next">
                                                    <span aria-hidden="true">Next &raquo;</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </nav>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Purchases -->
                        <div class="col-lg-6 col-md-12">
                            <div class="recent-section">
                                <h2>Recent Purchases</h2>
                                <div class="table-responsive">
                                    <table class="table table-striped purchases-table">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">ID</th>
                                                <th style="width: 15%;">User</th>
                                                <th style="width: 15%;">Type</th>
                                                <th style="width: 15%;">Package Size</th>
                                                <th style="width: 10%;">Quantity</th>
                                                <th style="width: 15%;">Total</th>
                                                <th style="width: 15%;">Date</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (mysqli_num_rows($recent_purchases_result) > 0): ?>
                                            <?php while ($purchase = mysqli_fetch_assoc($recent_purchases_result)): ?>
                                            <tr>
                                                <td><?php echo $purchase['purchaseid']; ?></td>
                                                <td><?php echo htmlspecialchars($purchase['username']); ?></td>
                                                <td>
                                                    <?php
                                                            // Display 'Business' for premium tickets
                                                            $ticketType = $purchase['ticket_type'];
                                                            $badgeClass = strtolower($ticketType);
                                                            $displayText = ucfirst($ticketType);

                                                            if (strtolower($ticketType) == 'premium') {
                                                                $displayText = 'Business';
                                                            }
                                                            ?>
                                                    <span class="badge badge-<?php echo $badgeClass; ?>">
                                                        <?php echo $displayText; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($purchase['package_size']); ?></td>
                                                <td><?php echo htmlspecialchars($purchase['numbers_per_package']); ?>
                                                </td>
                                                <td>$<?php echo htmlspecialchars($purchase['dollar_price_per_package']); ?>
                                                </td>
                                                <td><?php echo date('Y-m-d', strtotime($purchase['purchase_time'])); ?>
                                                </td>
                                            </tr>
                                            <?php endwhile; ?>
                                            <?php else: ?>
                                            <tr>
                                                <td colspan="7" class="text-center">No purchases found</td>
                                            </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>

                                    <?php if ($total_purchases_pages > 1): ?>
                                    <nav aria-label="Purchases pagination">
                                        <ul class="pagination justify-content-center mt-3">
                                            <li
                                                class="page-item <?php echo ($purchases_page <= 1) ? 'disabled' : ''; ?>">
                                                <a class="page-link"
                                                    href="?purchases_page=<?php echo $purchases_page - 1; ?><?php echo isset($_GET['tickets_page']) ? '&tickets_page=' . $_GET['tickets_page'] : ''; ?>"
                                                    aria-label="Previous">
                                                    <span aria-hidden="true">&laquo; Previous</span>
                                                </a>
                                            </li>

                                            <?php for ($i = 1; $i <= $total_purchases_pages; $i++): ?>
                                            <li
                                                class="page-item <?php echo ($purchases_page == $i) ? 'active' : ''; ?>">
                                                <a class="page-link"
                                                    href="?purchases_page=<?php echo $i; ?><?php echo isset($_GET['tickets_page']) ? '&tickets_page=' . $_GET['tickets_page'] : ''; ?>"><?php echo $i; ?></a>
                                            </li>
                                            <?php endfor; ?>

                                            <li
                                                class="page-item <?php echo ($purchases_page >= $total_purchases_pages) ? 'disabled' : ''; ?>">
                                                <a class="page-link"
                                                    href="?purchases_page=<?php echo $purchases_page + 1; ?><?php echo isset($_GET['tickets_page']) ? '&tickets_page=' . $_GET['tickets_page'] : ''; ?>"
                                                    aria-label="Next">
                                                    <span aria-hidden="true">Next &raquo;</span>
                                                </a>
                                            </li>
                                        </ul>
                                    </nav>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const userDropdown = document.querySelector('.user-dropdown');
        const userInfo = document.querySelector('.user-info');
        const menuToggle = document.querySelector('.mobile-menu-toggle');

        // Only apply dropdown functionality on mobile
        if (window.innerWidth <= 767) {
            userInfo.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Also add direct click handler to the toggle icon for better mobile experience
            menuToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                userDropdown.classList.toggle('active');
                menuToggle.classList.toggle('active');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!userDropdown.contains(event.target)) {
                    userDropdown.classList.remove('active');
                    menuToggle.classList.remove('active');
                }
            });
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 767) {
                userDropdown.classList.remove('active');
                menuToggle.classList.remove('active');
            }
        });
    });
    </script>
</body>

</html>