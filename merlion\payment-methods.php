<?php
session_start();
include('../functions/server.php');
if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: index.php');
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Payment Methods</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    .container {
        width: 1500px;
        max-width: 95%;
    }

    /* Custom modal width */
    .custom-width-modal {
        max-width: 1200px;
        /* You can adjust this value as needed */
        width: 90%;
        /* This makes it responsive */
    }

    /* Make sure the modal content has enough space */
    .custom-width-modal .modal-content {
        padding: 20px;
    }

    /* Payment method card styling */
    .payment-method-card {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 20px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }

    .payment-method-card:hover {
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .payment-method-card .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #eee;
        padding-bottom: 15px;
        margin-bottom: 15px;
    }

    .payment-method-card .card-header h5 {
        margin: 0;
        font-weight: 600;
        color: #333;
    }

    .payment-method-card .card-body {
        color: #666;
    }

    .payment-method-card .card-footer {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
    }

    .payment-method-card .btn-sm {
        padding: 5px 10px;
        font-size: 0.85rem;
    }

    .payment-method-icon {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }

    .add-payment-method {
        background-color: #f8f9fa;
        border: 2px dashed #ddd;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .add-payment-method:hover {
        border-color: #473BF0;
        background-color: #f0f0ff;
    }

    .add-payment-method i {
        font-size: 2rem;
        color: #473BF0;
        margin-bottom: 10px;
    }

    /* Responsive adjustments */
    @media (max-width: 992px) {
        .custom-width-modal {
            max-width: 90%;
        }
    }

    @media (max-width: 576px) {
        .custom-width-modal {
            max-width: 95%;
            margin: 0.5rem auto;
        }

        .custom-width-modal .modal-content {
            padding: 15px;
        }

        .payment-method-card {
            padding: 15px;
        }
    }

    /* Custom Alert Popup Styling */
    .custom-alert {
        display: none;
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        min-width: 300px;
        max-width: 500px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        overflow: hidden;
        animation: alertFadeIn 0.3s ease-out;
    }

    .custom-alert.show {
        display: block;
    }

    .custom-alert-header {
        padding: 15px 20px;
        background-color: #f8d7da;
        color: #721c24;
        font-weight: bold;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .custom-alert-header i {
        margin-right: 10px;
        font-size: 20px;
    }

    .custom-alert-body {
        padding: 20px;
        color: #333;
    }

    .custom-alert-footer {
        padding: 10px 20px 15px;
        text-align: right;
    }

    .custom-alert-btn {
        background-color: #473BF0;
        color: white;
        border: none;
        padding: 8px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: 500;
    }

    .custom-alert-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 9998;
    }

    .custom-alert-overlay.show {
        display: block;
    }

    @keyframes alertFadeIn {
        from {
            opacity: 0;
            transform: translate(-50%, -20px);
        }

        to {
            opacity: 1;
            transform: translate(-50%, 0);
        }
    }
    </style>
</head>

<body data-theme="light">
    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];
            $sqlshow = "SELECT username, email, starter_tickets, premium_tickets, ultimate_tickets, tell, company_name, tax_id, address, district, city, postal_code, country FROM user WHERE username = '$username'";
            $resultshow = mysqli_query($conn, $sqlshow);
            ?>
        <?php if ($resultshow && mysqli_num_rows($resultshow) > 0) :
                $user = mysqli_fetch_assoc($resultshow);
            ?>
        <!-- Display user data here -->
        <!-- Page Banner Area -->
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container"></div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">
                <div class="row justify-content-md-between pt-9">
                    <div class="col-12 text-center mb-8">
                        <h2 class="text-center mb-4"
                            style="font-size: 36px; color: #333; font-weight: 600; text-align: center;">💳 Payment
                            Methods</h2>
                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                            <div class="row">
                                <div class="col-12 mb-4">
                                    <h4 class="mb-3">Your Saved Payment Methods</h4>
                                    <p class="text-muted">Manage your payment methods for future purchases.</p>
                                </div>

                                <!-- Credit Card -->
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="payment-method-card">
                                        <div class="card-header">
                                            <div class="d-flex align-items-center">
                                                <img src="https://cdn.iconscout.com/icon/free/png-256/free-stripe-2-498440.png"
                                                    alt="Stripe" class="payment-method-icon">
                                                <h5>Credit Card</h5>
                                            </div>
                                            <span class="badge badge-success">Default</span>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Card Number:</strong> **** **** **** 4242</p>
                                            <p><strong>Expiry:</strong> 12/25</p>
                                        </div>
                                        <div class="card-footer">
                                            <button class="btn btn-sm btn-outline-danger mr-2">Remove</button>
                                            <button class="btn btn-sm btn-primary">Edit</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Add New Payment Method -->
                                <div class="col-lg-6 col-md-12 mb-4">
                                    <div class="payment-method-card add-payment-method" data-toggle="modal"
                                        data-target="#addPaymentModal">
                                        <i class="fas fa-plus-circle"></i>
                                        <p>Add New Payment Method</p>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                <div class="col-12">
                                    <h4 class="mb-3">Payment History</h4>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Amount</th>
                                                    <th>Payment Method</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td>2023-05-15</td>
                                                    <td>$49.99</td>
                                                    <td>**** 4242</td>
                                                    <td><span class="badge badge-success">Completed</span></td>
                                                </tr>
                                                <tr>
                                                    <td>2023-04-10</td>
                                                    <td>$29.99</td>
                                                    <td>**** 4242</td>
                                                    <td><span class="badge badge-success">Completed</span></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Payment Method Modal -->
        <div class="modal fade" id="addPaymentModal" tabindex="-1" role="dialog" aria-labelledby="addPaymentModalLabel"
            aria-hidden="true">
            <div class="modal-dialog custom-width-modal" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="addPaymentModalLabel">Add New Payment Method</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="paymentForm">
                            <div class="form-group">
                                <label for="cardName">Name on Card</label>
                                <input type="text" class="form-control" id="cardName" placeholder="John Doe">
                            </div>
                            <div class="form-group">
                                <label for="cardNumber">Card Number</label>
                                <input type="text" class="form-control" id="cardNumber"
                                    placeholder="1234 5678 9012 3456">
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="expiryDate">Expiry Date</label>
                                        <input type="text" class="form-control" id="expiryDate" placeholder="MM/YY">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="cvv">CVV</label>
                                        <input type="text" class="form-control" id="cvv" placeholder="123">
                                    </div>
                                </div>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="defaultCard">
                                <label class="form-check-label" for="defaultCard">Make this my default payment
                                    method</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="savePaymentMethod()">Save Payment
                            Method</button>
                    </div>
                </div>
            </div>
        </div>

        <?php else : ?>
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2>User data not found.</h2>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <?php else : ?>
        <div class="container">
            <div class="row">
                <div class="col-12 text-center">
                    <h2>Please log in to view your payment methods.</h2>
                    <a href="../front-end/sign-in.php" class="btn btn-primary mt-3">Log In</a>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <!-- Footer section -->
        <?php include('../header-footer/footer.php'); ?>
    </div>

    <!-- Vendor Scripts -->
    <script src="../js/vendor.min.js"></script>
    <!-- Plugin's Scripts -->
    <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
    <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
    <script src="../plugins/aos/aos.min.js"></script>
    <script src="../plugins/slick/slick.min.js"></script>
    <script src="../plugins/date-picker/js/gijgo.min.js"></script>
    <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
    <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
    <!-- Activation Script -->
    <script src="../js/custom.js"></script>

    <script>
    function savePaymentMethod() {
        // Validate form
        var cardName = document.getElementById('cardName').value.trim();
        var cardNumber = document.getElementById('cardNumber').value.trim();
        var expiryDate = document.getElementById('expiryDate').value.trim();
        var cvv = document.getElementById('cvv').value.trim();

        if (!cardName || !cardNumber || !expiryDate || !cvv) {
            showAlert('Please fill in all required fields.');
            return;
        }

        // Here you would normally send the data to your server
        // For demo purposes, we'll just show a success message and close the modal
        $('#addPaymentModal').modal('hide');

        // Show success message
        showAlert('Payment method added successfully!', 'Success', 'success');

        // Reload page after a short delay to show the new payment method
        setTimeout(function() {
            location.reload();
        }, 2000);
    }

    function showAlert(message, title = 'Warning', type = 'warning') {
        var alertElement = document.getElementById('customAlert');
        var alertOverlay = document.getElementById('alertOverlay');
        var alertTitle = document.getElementById('alertTitle');
        var alertMessage = document.getElementById('alertMessage');

        // Set the title and message
        alertTitle.textContent = title;
        alertMessage.textContent = message;

        // Change header color based on type
        var headerElement = alertElement.querySelector('.custom-alert-header');
        if (type === 'success') {
            headerElement.style.backgroundColor = '#d4edda';
            headerElement.style.color = '#155724';
        } else if (type === 'warning') {
            headerElement.style.backgroundColor = '#f8d7da';
            headerElement.style.color = '#721c24';
        }

        // Show the alert
        alertElement.classList.add('show');
        alertOverlay.classList.add('show');
    }

    function closeAlert() {
        var alertElement = document.getElementById('customAlert');
        var alertOverlay = document.getElementById('alertOverlay');

        alertElement.classList.remove('show');
        alertOverlay.classList.remove('show');
    }
    </script>

    <!-- Custom Alert Popup -->
    <div class="custom-alert-overlay" id="alertOverlay"></div>
    <div class="custom-alert" id="customAlert">
        <div class="custom-alert-header">
            <div>
                <i class="fas fa-exclamation-triangle"></i>
                <span id="alertTitle">Warning</span>
            </div>
            <button type="button" class="close" onclick="closeAlert()">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="custom-alert-body" id="alertMessage">
            Please fill in all required fields.
        </div>
        <div class="custom-alert-footer">
            <button type="button" class="custom-alert-btn" onclick="closeAlert()">OK</button>
        </div>
    </div>
</body>

</html>