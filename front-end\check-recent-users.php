<?php
include('../functions/server.php');

echo "<h1>Recent Users and Stripe Customer IDs</h1>";

// Check recent users
$sql = "SELECT id, username, email, stripe_customer_id, registration_time FROM user ORDER BY id DESC LIMIT 10";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Stripe Customer ID</th><th>Registration Time</th></tr>";
    
    while($row = $result->fetch_assoc()) {
        $stripe_status = $row['stripe_customer_id'] ? 
            "<span style='color: green;'>✓ " . $row['stripe_customer_id'] . "</span>" : 
            "<span style='color: red;'>✗ No Stripe ID</span>";
        
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['username'] . "</td>";
        echo "<td>" . $row['email'] . "</td>";
        echo "<td>" . $stripe_status . "</td>";
        echo "<td>" . $row['registration_time'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No users found in database.</p>";
}

echo "<h2>Fix Missing Stripe Customer IDs</h2>";
echo "<p>Users without Stripe customer IDs will get them created automatically on their next purchase.</p>";
echo "<p>Or you can manually create them by running the update script below:</p>";

// Show users without stripe_customer_id
$sql = "SELECT id, username, email FROM user WHERE stripe_customer_id IS NULL OR stripe_customer_id = '' ORDER BY id DESC LIMIT 5";
$result = $conn->query($sql);

if ($result->num_rows > 0) {
    echo "<h3>Users Missing Stripe Customer IDs:</h3>";
    echo "<ul>";
    while($row = $result->fetch_assoc()) {
        echo "<li>ID: {$row['id']}, Username: {$row['username']}, Email: {$row['email']}</li>";
    }
    echo "</ul>";
} else {
    echo "<p style='color: green;'>✓ All users have Stripe customer IDs!</p>";
}

$conn->close();
?>
