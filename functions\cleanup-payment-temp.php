<?php
include('server.php');

echo "<h2>🧹 Payment Temp Cleanup Function</h2>";

/**
 * Clean up payment_temp data for users who have been successfully created
 * This function deletes payment_temp records where:
 * 1. User account exists in user table
 * 2. User was created more than 1 hour ago (to ensure payment success page has been viewed)
 */
function cleanupPaymentTemp($conn, $dry_run = true) {
    $results = [
        'total_checked' => 0,
        'eligible_for_cleanup' => 0,
        'cleaned_up' => 0,
        'errors' => 0,
        'details' => []
    ];

    try {
        // Find payment_temp records that can be cleaned up:
        // 1. Records where user exists and was created more than 1 hour ago (guest purchases)
        // 2. Records older than 1 hour for existing users (logged-in purchases)
        $query = "
            SELECT pt.id, pt.session_id, pt.username, pt.email, pt.user_created, pt.created_at,
                   u.id as user_id, u.registration_time,
                   CASE
                       WHEN pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'guest_purchase'
                       WHEN pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN 'logged_in_purchase'
                       ELSE 'not_eligible'
                   END as cleanup_reason
            FROM payment_temp pt
            INNER JOIN user u ON pt.username = u.username
            WHERE (
                (pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
                OR
                (pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
            )
            ORDER BY pt.created_at ASC
        ";

        $result = mysqli_query($conn, $query);

        if (!$result) {
            throw new Exception("Query failed: " . mysqli_error($conn));
        }

        $results['total_checked'] = mysqli_num_rows($result);

        while ($row = mysqli_fetch_assoc($result)) {
            $results['eligible_for_cleanup']++;

            $detail = [
                'payment_temp_id' => $row['id'],
                'session_id' => substr($row['session_id'], 0, 20) . '...',
                'username' => $row['username'],
                'email' => $row['email'],
                'user_id' => $row['user_id'],
                'registration_time' => $row['registration_time'],
                'cleanup_reason' => $row['cleanup_reason'],
                'action' => 'pending'
            ];

            if (!$dry_run) {
                // Actually delete the record
                $delete_stmt = $conn->prepare("DELETE FROM payment_temp WHERE id = ?");
                $delete_stmt->bind_param("i", $row['id']);

                if ($delete_stmt->execute()) {
                    $results['cleaned_up']++;
                    $detail['action'] = 'deleted';
                } else {
                    $results['errors']++;
                    $detail['action'] = 'error: ' . $delete_stmt->error;
                }
                $delete_stmt->close();
            } else {
                $detail['action'] = 'would_delete (dry_run)';
            }

            $results['details'][] = $detail;
        }

    } catch (Exception $e) {
        $results['error_message'] = $e->getMessage();
        $results['errors']++;
    }

    return $results;
}

// Check if this is a manual cleanup request
if (isset($_POST['cleanup_action'])) {
    $dry_run = ($_POST['cleanup_action'] === 'dry_run');

    echo "<h3>🔄 " . ($dry_run ? "Dry Run" : "Actual Cleanup") . " Results</h3>";

    $results = cleanupPaymentTemp($conn, $dry_run);

    echo "<div style='background: " . ($dry_run ? "#fff3cd" : "#d4edda") . "; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>Summary:</h4>";
    echo "<ul>";
    echo "<li><strong>Total Records Checked:</strong> " . $results['total_checked'] . "</li>";
    echo "<li><strong>Eligible for Cleanup:</strong> " . $results['eligible_for_cleanup'] . "</li>";
    echo "<li><strong>Successfully " . ($dry_run ? "Would Be " : "") . "Cleaned:</strong> " . $results['cleaned_up'] . "</li>";
    echo "<li><strong>Errors:</strong> " . $results['errors'] . "</li>";
    echo "</ul>";

    if (isset($results['error_message'])) {
        echo "<p style='color: #dc3545;'><strong>Error:</strong> " . htmlspecialchars($results['error_message']) . "</p>";
    }
    echo "</div>";

    if (!empty($results['details'])) {
        echo "<h4>📋 Detailed Results:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 15px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Payment Temp ID</th>";
        echo "<th style='padding: 8px;'>Session ID</th>";
        echo "<th style='padding: 8px;'>Username</th>";
        echo "<th style='padding: 8px;'>Email</th>";
        echo "<th style='padding: 8px;'>User ID</th>";
        echo "<th style='padding: 8px;'>Registration Time</th>";
        echo "<th style='padding: 8px;'>Cleanup Reason</th>";
        echo "<th style='padding: 8px;'>Action</th>";
        echo "</tr>";

        foreach ($results['details'] as $detail) {
            $action_color = '';
            if (strpos($detail['action'], 'deleted') !== false) {
                $action_color = 'color: #28a745;';
            } elseif (strpos($detail['action'], 'error') !== false) {
                $action_color = 'color: #dc3545;';
            } elseif (strpos($detail['action'], 'would_delete') !== false) {
                $action_color = 'color: #ffc107;';
            }

            $reason_color = '';
            if ($detail['cleanup_reason'] === 'guest_purchase') {
                $reason_color = 'color: #007bff;';
            } elseif ($detail['cleanup_reason'] === 'logged_in_purchase') {
                $reason_color = 'color: #28a745;';
            }

            echo "<tr>";
            echo "<td style='padding: 8px;'>" . $detail['payment_temp_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($detail['session_id']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($detail['username']) . "</td>";
            echo "<td style='padding: 8px;'>" . htmlspecialchars($detail['email']) . "</td>";
            echo "<td style='padding: 8px;'>" . $detail['user_id'] . "</td>";
            echo "<td style='padding: 8px;'>" . $detail['registration_time'] . "</td>";
            echo "<td style='padding: 8px; $reason_color'>" . htmlspecialchars($detail['cleanup_reason']) . "</td>";
            echo "<td style='padding: 8px; $action_color'>" . htmlspecialchars($detail['action']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    // Show current status and cleanup options
    echo "<h3>📊 Current Payment Temp Status</h3>";

    // Show total payment_temp records
    $total_query = "SELECT COUNT(*) as total FROM payment_temp";
    $total_result = mysqli_query($conn, $total_query);
    $total_count = mysqli_fetch_assoc($total_result)['total'];

    // Show records with created users
    $created_query = "
        SELECT COUNT(*) as created_users
        FROM payment_temp pt
        INNER JOIN user u ON pt.username = u.username
        WHERE pt.user_created = 1
    ";
    $created_result = mysqli_query($conn, $created_query);
    $created_count = mysqli_fetch_assoc($created_result)['created_users'];

    // Show records eligible for cleanup (guest purchases > 1 hour ago OR logged-in purchases > 1 hour ago)
    $eligible_query = "
        SELECT COUNT(*) as eligible
        FROM payment_temp pt
        INNER JOIN user u ON pt.username = u.username
        WHERE (
            (pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
            OR
            (pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR))
        )
    ";
    $eligible_result = mysqli_query($conn, $eligible_query);
    $eligible_count = mysqli_fetch_assoc($eligible_result)['eligible'];

    // Show breakdown by type
    $guest_eligible_query = "
        SELECT COUNT(*) as guest_eligible
        FROM payment_temp pt
        INNER JOIN user u ON pt.username = u.username
        WHERE pt.user_created = 1
        AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ";
    $guest_eligible_result = mysqli_query($conn, $guest_eligible_query);
    $guest_eligible_count = mysqli_fetch_assoc($guest_eligible_result)['guest_eligible'];

    $logged_in_eligible_query = "
        SELECT COUNT(*) as logged_in_eligible
        FROM payment_temp pt
        INNER JOIN user u ON pt.username = u.username
        WHERE pt.created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
        AND NOT (pt.user_created = 1 AND u.registration_time < DATE_SUB(NOW(), INTERVAL 1 HOUR))
    ";
    $logged_in_eligible_result = mysqli_query($conn, $logged_in_eligible_query);
    $logged_in_eligible_count = mysqli_fetch_assoc($logged_in_eligible_result)['logged_in_eligible'];

    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<ul>";
    echo "<li><strong>Total payment_temp records:</strong> $total_count</li>";
    echo "<li><strong>Records with created users:</strong> $created_count</li>";
    echo "<li><strong>Recent records (24h):</strong> $recent_count</li>";
    echo "<li><strong>Eligible for cleanup (>1 hour old):</strong> $eligible_count</li>";
    echo "<ul>";
    echo "<li><strong>Guest purchases:</strong> $guest_eligible_count</li>";
    echo "<li><strong>Logged-in purchases:</strong> $logged_in_eligible_count</li>";
    echo "</ul>";
    echo "</ul>";
    echo "</div>";

    if ($eligible_count > 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ Cleanup Recommended</h4>";
        echo "<p>There are <strong>$eligible_count</strong> payment_temp records that can be safely cleaned up.</p>";
        echo "<p>These records belong to users who have been successfully created and the payment success page has likely been viewed.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ No Cleanup Needed</h4>";
        echo "<p>All payment_temp records are either recent or belong to users that haven't been created yet.</p>";
        echo "</div>";
    }
}

echo "<h3>🛠️ Cleanup Actions</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<form method='post' style='margin: 10px 0;'>";
echo "<button type='submit' name='cleanup_action' value='dry_run' style='background: #ffc107; color: #212529; padding: 10px 15px; border: none; border-radius: 5px; margin-right: 10px;'>🔍 Dry Run (Preview)</button>";
echo "<button type='submit' name='cleanup_action' value='actual' style='background: #dc3545; color: white; padding: 10px 15px; border: none; border-radius: 5px;' onclick='return confirm(\"Are you sure you want to delete payment_temp records? This cannot be undone.\")'>🗑️ Actual Cleanup</button>";
echo "</form>";
echo "<p><strong>Dry Run:</strong> Shows what would be deleted without actually deleting anything.</p>";
echo "<p><strong>Actual Cleanup:</strong> Permanently deletes eligible payment_temp records.</p>";
echo "</div>";

echo "<h3>📋 How This Works</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Cleanup Criteria:</h4>";
echo "<ul>";
echo "<li>✅ <strong>User account exists</strong> in user table</li>";
echo "<li>✅ <strong>Guest purchases:</strong> User was created successfully (user_created = 1) AND registration is older than 1 hour</li>";
echo "<li>✅ <strong>Logged-in purchases:</strong> payment_temp record is older than 1 hour (purchase completed)</li>";
echo "</ul>";

echo "<h4>Safety Features:</h4>";
echo "<ul>";
echo "<li>🔒 <strong>Only deletes confirmed successful creations</strong></li>";
echo "<li>⏰ <strong>Waits 1 hour before cleanup</strong> (allows payment success page viewing)</li>";
echo "<li>🔍 <strong>Dry run option</strong> to preview changes</li>";
echo "<li>📝 <strong>Detailed logging</strong> of all actions</li>";
echo "</ul>";

echo "<h4>Benefits:</h4>";
echo "<ul>";
echo "<li>🧹 <strong>Reduces database clutter</strong></li>";
echo "<li>🔐 <strong>Removes sensitive temporary data</strong></li>";
echo "<li>⚡ <strong>Improves database performance</strong></li>";
echo "<li>📊 <strong>Maintains data integrity</strong></li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 Related Functions</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/stripe-webhook.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Webhook (Auto-cleanup)</a>";
echo "<a href='../front-end/payment-success.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Payment Success</a>";
echo "<a href='process-pending-users.php' style='background: #6f42c1; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Process Pending Users</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button { cursor: pointer; }
button:hover { opacity: 0.9; }
</style>
