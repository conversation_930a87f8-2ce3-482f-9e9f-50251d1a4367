<div class="dashboard-menu">
    <h5 class="menu-title">Account Menu <span class="mobile-menu-toggle"><i class="fa fa-chevron-down"></i></span></h5>
    <ul class="list-unstyled menu-items">
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'my-ticket.php' ? 'active' : '' ?>">
            <a href="my-ticket.php"><i class="fa fa-tachometer-alt"></i> Tickets</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'my-ticket-log.php' ? 'active' : '' ?>">
            <a href="my-ticket-log.php"><i class="fa fa-clipboard-list"></i> Ticket Logs</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'purchase-history.php' ? 'active' : '' ?>">
            <a href="purchase-history.php"><i class="fa fa-shopping-basket"></i> My Orders</a>
        </li>
        <!-- <li><a href="downloads.php"><i class="fa fa-download"></i> Downloads</a></li> -->
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'profile.php' ? 'active' : '' ?>">
            <a href="profile.php"><i class="fa fa-user"></i> Account Details</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'payment-methods.php' ? 'active' : '' ?>">
            <a href="payment-methods.php"><i class="fa fa-credit-card"></i> Payment Methods</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'create-ticket.php' ? 'active' : '' ?>">
            <a href="create-ticket.php"><i class="fa fa-headset"></i> Open a Support Ticket</a>
        </li>
        <li class="<?= basename($_SERVER['PHP_SELF']) == 'chat-support.php' ? 'active' : '' ?>">
            <a href="chat-support.php"><i class="fa fa-comments"></i> Live Chat Support</a>
        </li>

        <li>
            <hr>
        </li>
        <li><a href="../index.php?logout=1"><i class="fa fa-sign-out-alt"></i> Log Out</a></li>
    </ul>
</div>

<style>
/* Dashboard Menu */
.dashboard-menu {
    background-color: #fff !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
    padding: 15px !important;
    margin-bottom: 20px !important;
    border: 1px solid #e0e6ed !important;
    box-sizing: border-box !important;
    height: auto !important;
    margin-right: 30px !important;
}

.dashboard-menu .menu-title {
    font-size: 22px !important;
    font-weight: 600 !important;
    color: #2c3e50 !important;
    margin-bottom: 10px !important;
    padding-bottom: 8px !important;
    border-bottom: 1px solid #e0e6ed !important;
}

.mobile-menu-toggle {
    display: none !important;
    float: right !important;
    cursor: pointer !important;
    transition: transform 0.3s ease !important;
}

.mobile-menu-toggle.active {
    transform: rotate(90deg) !important;
}

.mobile-menu-toggle i {
    transition: transform 0.3s ease !important;
    display: inline-block !important;
}

.mobile-menu-toggle.active i {
    transform: rotate(180deg) !important;
}

.dashboard-menu ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.dashboard-menu ul li {
    margin-bottom: 8px !important;
}

.dashboard-menu ul li a {
    text-decoration: none !important;
    color: #555 !important;
    font-weight: 500 !important;
    display: flex !important;
    align-items: center !important;
    padding: 6px 10px !important;
    border-radius: 6px !important;
    transition: background-color 0.3s ease, color 0.3s ease !important;
    font-size: 14px !important;
}

.dashboard-menu ul li a i {
    margin-right: 8px !important;
    font-size: 14px !important;
    color: #777 !important;
}

.dashboard-menu ul li a:hover {
    background-color: #f0f2f5 !important;
    color: #007bff !important;
}

.dashboard-menu ul li.active a {
    background-color: #007bff !important;
    color: #fff !important;
    font-weight: bold !important;
}

.dashboard-menu ul li.active a i {
    color: #fff !important;
}

/* Mobile styles */
@media (max-width: 767px) {
    .dashboard-menu {
        margin-right: 0 !important;
        margin-bottom: 15px !important;
    }

    .mobile-menu-toggle {
        display: block !important;
    }

    .dashboard-menu .menu-title {
        cursor: pointer !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
    }

    .dashboard-menu ul.menu-items {
        max-height: 0 !important;
        overflow: hidden !important;
        transition: max-height 0.5s ease !important;
    }

    .dashboard-menu ul.menu-items.expanded {
        max-height: 500px !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Only apply this functionality on mobile
    if (window.innerWidth <= 767) {
        const menuTitle = document.querySelector('.menu-title');
        const menuToggle = document.querySelector('.mobile-menu-toggle');
        const menuItems = document.querySelector('.menu-items');

        // Check if menu should be expanded by default (if there's an active item)
        const hasActiveItem = document.querySelector('.menu-items .active');

        if (hasActiveItem) {
            menuItems.classList.add('expanded');
            menuToggle.classList.add('active');
        }

        menuTitle.addEventListener('click', function() {
            menuItems.classList.toggle('expanded');
            menuToggle.classList.toggle('active');
        });
    }
});
</script>