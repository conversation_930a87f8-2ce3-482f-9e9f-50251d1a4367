<?php
include('server.php');

echo "<h2>🔍 Debug user95404 Issue</h2>";

$username = 'user95404';
$email = '<EMAIL>';
$displayed_password = 'dc6fb0b1';

echo "<h3>What Payment Success Page Showed:</h3>";
echo "<p><strong>Username:</strong> $username</p>";
echo "<p><strong>Email:</strong> $email</p>";
echo "<p><strong>Password:</strong> $displayed_password</p>";

echo "<h3>What's in the Database:</h3>";

// Check user table
$user_query = "SELECT id, username, email, password, registration_time FROM user WHERE username = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $username);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows > 0) {
    $user_data = $user_result->fetch_assoc();
    echo "<h4>✅ User Table:</h4>";
    echo "<p><strong>ID:</strong> " . $user_data['id'] . "</p>";
    echo "<p><strong>Username:</strong> " . $user_data['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $user_data['email'] . "</p>";
    echo "<p><strong>Password Hash:</strong> " . $user_data['password'] . "</p>";
    echo "<p><strong>Hash Type:</strong> " . (strpos($user_data['password'], '$2y$') === 0 ? 'bcrypt' : 'md5') . "</p>";
    echo "<p><strong>Registration:</strong> " . $user_data['registration_time'] . "</p>";
    
    $stored_hash = $user_data['password'];
} else {
    echo "<p>❌ User not found in user table</p>";
    $stored_hash = null;
}

// Check payment_temp table
echo "<h4>Payment Temp Table:</h4>";
$temp_query = "SELECT id, session_id, username, email, password, created_at FROM payment_temp WHERE username = ? ORDER BY id DESC";
$temp_stmt = $conn->prepare($temp_query);
$temp_stmt->bind_param("s", $username);
$temp_stmt->execute();
$temp_result = $temp_stmt->get_result();

if ($temp_result->num_rows > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>ID</th>";
    echo "<th style='padding: 10px;'>Session ID</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Email</th>";
    echo "<th style='padding: 10px;'>Password</th>";
    echo "<th style='padding: 10px;'>Created</th>";
    echo "</tr>";
    
    while ($temp_row = mysqli_fetch_assoc($temp_result)) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>" . $temp_row['id'] . "</td>";
        echo "<td style='padding: 10px;'>" . substr($temp_row['session_id'], 0, 20) . "...</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['username'] . "</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['email'] . "</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['password'] . "</td>";
        echo "<td style='padding: 10px;'>" . $temp_row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ No records found in payment_temp table</p>";
}

// Test password verification
if ($stored_hash) {
    echo "<h3>🧪 Password Verification Test:</h3>";
    
    echo "<h4>Test 1: Direct Password Verification</h4>";
    if (strpos($stored_hash, '$2y$') === 0) {
        $direct_verify = password_verify($displayed_password, $stored_hash);
        echo "<p><strong>bcrypt verify('$displayed_password', hash):</strong> " . ($direct_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
    } else {
        $direct_verify = ($stored_hash === md5($displayed_password));
        echo "<p><strong>MD5 verify('$displayed_password'):</strong> " . ($direct_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
    }
    
    // Test if any password in payment_temp works
    echo "<h4>Test 2: Payment Temp Password Verification</h4>";
    $temp_passwords_query = "SELECT DISTINCT password FROM payment_temp WHERE username = ?";
    $temp_passwords_stmt = $conn->prepare($temp_passwords_query);
    $temp_passwords_stmt->bind_param("s", $username);
    $temp_passwords_stmt->execute();
    $temp_passwords_result = $temp_passwords_stmt->get_result();
    
    while ($temp_password_row = mysqli_fetch_assoc($temp_passwords_result)) {
        $temp_password = $temp_password_row['password'];
        
        if (strpos($stored_hash, '$2y$') === 0) {
            $temp_verify = password_verify($temp_password, $stored_hash);
            echo "<p><strong>bcrypt verify('$temp_password', hash):</strong> " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
        } else {
            $temp_verify = ($stored_hash === md5($temp_password));
            echo "<p><strong>MD5 verify('$temp_password'):</strong> " . ($temp_verify ? '✅ PASS' : '❌ FAIL') . "</p>";
        }
    }
}

// Check what the webhook actually processed
echo "<h3>📋 Recent Webhook Activity:</h3>";
echo "<p>Check the webhook.log file for recent processing of user95404</p>";

// Show what should work for sign-in
echo "<h3>🔧 Solution:</h3>";
if ($stored_hash) {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>⚠️ The Problem:</h4>";
    echo "<ul>";
    echo "<li>Payment success page shows: <strong>$displayed_password</strong></li>";
    echo "<li>Database has bcrypt hash that doesn't match this password</li>";
    echo "<li>Need to find the original password that was hashed</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>✅ Possible Solutions:</h4>";
    echo "<ol>";
    echo "<li><strong>Find the original password:</strong> Check payment_temp for the password that was actually hashed</li>";
    echo "<li><strong>Update payment_temp:</strong> Store the correct password that matches the hash</li>";
    echo "<li><strong>Fix the webhook:</strong> Ensure consistent password handling</li>";
    echo "</ol>";
    echo "</div>";
}
?>

<h3>🔗 Quick Actions:</h3>
<p><a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Sign-In</a></p>
<p><a href="check-recent-purchase.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Check Recent Purchase</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
</style>
