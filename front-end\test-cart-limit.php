<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

echo "<h1>Cart 2-Card Limit Test</h1>";

if (!isset($_SESSION['user_id'])) {
    echo "<p style='color: red;'>❌ Please <a href='sign-in.php'>login</a> to test the cart functionality.</p>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Get user's Stripe customer ID and saved payment methods
$stmt = $conn->prepare("SELECT stripe_customer_id, username FROM user WHERE id = ?");
$stmt->bind_param("i", $user_id);
$stmt->execute();
$stmt->bind_result($stripe_customer_id, $username);
$stmt->fetch();
$stmt->close();

echo "<h2>Current User Information</h2>";
echo "<p><strong>Username:</strong> " . $username . "</p>";
echo "<p><strong>User ID:</strong> " . $user_id . "</p>";
echo "<p><strong>Stripe Customer ID:</strong> " . ($stripe_customer_id ?: 'None') . "</p>";

$saved_payment_methods = [];
if ($stripe_customer_id) {
    try {
        $payment_methods = \Stripe\PaymentMethod::all([
            'customer' => $stripe_customer_id,
            'type' => 'card',
        ]);
        $saved_payment_methods = $payment_methods->data;
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error fetching payment methods: " . $e->getMessage() . "</p>";
    }
}

echo "<h2>Payment Methods Analysis</h2>";
echo "<p><strong>Number of saved cards:</strong> " . count($saved_payment_methods) . "</p>";

if (count($saved_payment_methods) > 0) {
    echo "<h3>Saved Payment Methods:</h3>";
    echo "<ul>";
    foreach ($saved_payment_methods as $index => $pm) {
        echo "<li>" . ucfirst($pm->card->brand) . " •••• " . $pm->card->last4 . " (Expires " . $pm->card->exp_month . "/" . $pm->card->exp_year . ")</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No saved payment methods found.</p>";
}

echo "<h2>Cart Behavior Test</h2>";

if (count($saved_payment_methods) < 2) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; color: #155724;'>";
    echo "<h4>✅ Expected Cart Behavior (Less than 2 cards):</h4>";
    echo "<ul>";
    if (count($saved_payment_methods) > 0) {
        echo "<li>Show " . count($saved_payment_methods) . " saved payment method(s)</li>";
    }
    echo "<li>Show 'Add New Card' option</li>";
    echo "<li>User can select any saved card or add a new one</li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; color: #856404;'>";
    echo "<h4>⚠️ Expected Cart Behavior (2 or more cards):</h4>";
    echo "<ul>";
    echo "<li>Show " . count($saved_payment_methods) . " saved payment method(s)</li>";
    echo "<li>Hide 'Add New Card' option</li>";
    echo "<li>Show warning message about maximum cards reached</li>";
    echo "<li>Show 'Manage Payment Methods' button</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<h2>Test Actions</h2>";
echo "<div style='background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h4>Testing Steps:</h4>";
echo "<ol>";
echo "<li><strong>Add items to cart:</strong> <a href='/helloit/support-ticket/buy-now' target='_blank'>Go to buy-now page</a> and add some items</li>";
echo "<li><strong>Test cart page:</strong> <a href='cart.php' target='_blank' style='background: #6754e2; color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px;'>Open Cart Page</a></li>";
echo "<li><strong>Verify behavior matches expectations above</strong></li>";
echo "</ol>";
echo "</div>";

echo "<h2>Additional Test Scenarios</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px;'>";
echo "<h4>To Test Different Scenarios:</h4>";

if (count($saved_payment_methods) < 2) {
    echo "<p><strong>To test 2-card limit:</strong></p>";
    echo "<ul>";
    echo "<li>Add " . (2 - count($saved_payment_methods)) . " more payment method(s) via <a href='payment-methods.php'>Payment Methods page</a></li>";
    echo "<li>Then test the cart page again to see the limit warning</li>";
    echo "</ul>";
} else {
    echo "<p><strong>To test under-limit behavior:</strong></p>";
    echo "<ul>";
    echo "<li>Remove some payment methods via <a href='payment-methods.php'>Payment Methods page</a></li>";
    echo "<li>Then test the cart page again to see 'Add New Card' option</li>";
    echo "</ul>";
}
echo "</div>";

echo "<h2>Quick Links</h2>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap;'>";
echo "<a href='cart.php' style='background: #6754e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Cart Page</a>";
echo "<a href='payment-methods.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Payment Methods</a>";
echo "<a href='/helloit/support-ticket/buy-now' style='background: #17a2b8; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Buy Now</a>";
echo "<a href='select-payment-method.php?cart_id=1' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Select Payment Method</a>";
echo "</div>";

// Show current cart items if any
echo "<h2>Current Cart Items</h2>";
$cart_query = "SELECT c.cart_id, ci.id AS cart_item_id, ci.ticket_id, ci.quantity, t.ticket_type, t.dollar_price_per_package
              FROM cart c
              JOIN cart_items ci ON c.cart_id = ci.cart_id
              JOIN tickets t ON ci.ticket_id = t.ticketid
              WHERE c.user_id = ? AND c.status = 'active'";

$stmt = $conn->prepare($cart_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
$cart_items = $result->fetch_all(MYSQLI_ASSOC);
$stmt->close();

if (!empty($cart_items)) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Product</th><th>Quantity</th><th>Price</th><th>Total</th></tr>";
    $total = 0;
    foreach ($cart_items as $item) {
        $item_total = $item['quantity'] * $item['dollar_price_per_package'];
        $total += $item_total;
        echo "<tr>";
        echo "<td>" . strtoupper($item['ticket_type']) . "</td>";
        echo "<td>" . $item['quantity'] . "</td>";
        echo "<td>$" . number_format($item['dollar_price_per_package'], 2) . "</td>";
        echo "<td>$" . number_format($item_total, 2) . "</td>";
        echo "</tr>";
    }
    echo "<tr style='font-weight: bold;'>";
    echo "<td colspan='3'>Total</td>";
    echo "<td>$" . number_format($total, 2) . "</td>";
    echo "</tr>";
    echo "</table>";
    echo "<p style='color: green;'>✅ You have items in cart - perfect for testing!</p>";
} else {
    echo "<p style='color: orange;'>⚠️ No items in cart. <a href='/helloit/support-ticket/buy-now'>Add some items</a> to test the cart functionality.</p>";
}

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

a {
    color: #6754e2;
}

a:hover {
    text-decoration: underline;
}

ul, ol {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
