!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(E,e){"use strict";function g(e){return null!=e&&e===e.window}var t=[],i=Object.getPrototypeOf,a=t.slice,m=t.flat?function(e){return t.flat.call(e)}:function(e){return t.concat.apply([],e)},l=t.push,r=t.indexOf,n={},o=n.toString,v=n.hasOwnProperty,s=v.toString,u=s.call(Object),y={},b=function(e){return"function"==typeof e&&"number"!=typeof e.nodeType},T=E.document,c={type:!0,src:!0,nonce:!0,noModule:!0};function _(e,t,n){var i,r,o=(n=n||T).createElement("script");if(o.text=e,t)for(i in c)(r=t[i]||t.getAttribute&&t.getAttribute(i))&&o.setAttribute(i,r);n.head.appendChild(o).parentNode.removeChild(o)}function w(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?n[o.call(e)]||"object":typeof e}var f="3.5.1",C=function(e,t){return new C.fn.init(e,t)};function d(e){var t=!!e&&"length"in e&&e.length,n=w(e);return!b(e)&&!g(e)&&("array"===n||0===t||"number"==typeof t&&0<t&&t-1 in e)}C.fn=C.prototype={jquery:f,constructor:C,length:0,toArray:function(){return a.call(this)},get:function(e){return null==e?a.call(this):e<0?this[e+this.length]:this[e]},pushStack:function(e){var t=C.merge(this.constructor(),e);return t.prevObject=this,t},each:function(e){return C.each(this,e)},map:function(n){return this.pushStack(C.map(this,function(e,t){return n.call(e,t,e)}))},slice:function(){return this.pushStack(a.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(C.grep(this,function(e,t){return(t+1)%2}))},odd:function(){return this.pushStack(C.grep(this,function(e,t){return t%2}))},eq:function(e){var t=this.length,n=+e+(e<0?t:0);return this.pushStack(0<=n&&n<t?[this[n]]:[])},end:function(){return this.prevObject||this.constructor()},push:l,sort:t.sort,splice:t.splice},C.extend=C.fn.extend=function(){var e,t,n,i,r,o,s=arguments[0]||{},a=1,l=arguments.length,u=!1;for("boolean"==typeof s&&(u=s,s=arguments[a]||{},a++),"object"==typeof s||b(s)||(s={}),a===l&&(s=this,a--);a<l;a++)if(null!=(e=arguments[a]))for(t in e)i=e[t],"__proto__"!==t&&s!==i&&(u&&i&&(C.isPlainObject(i)||(r=Array.isArray(i)))?(n=s[t],o=r&&!Array.isArray(n)?[]:r||C.isPlainObject(n)?n:{},r=!1,s[t]=C.extend(u,o,i)):void 0!==i&&(s[t]=i));return s},C.extend({expando:"jQuery"+(f+Math.random()).replace(/\D/g,""),isReady:!0,error:function(e){throw new Error(e)},noop:function(){},isPlainObject:function(e){var t,n;return!(!e||"[object Object]"!==o.call(e)||(t=i(e))&&("function"!=typeof(n=v.call(t,"constructor")&&t.constructor)||s.call(n)!==u))},isEmptyObject:function(e){for(var t in e)return!1;return!0},globalEval:function(e,t,n){_(e,{nonce:t&&t.nonce},n)},each:function(e,t){var n,i=0;if(d(e))for(n=e.length;i<n&&!1!==t.call(e[i],i,e[i]);i++);else for(i in e)if(!1===t.call(e[i],i,e[i]))break;return e},makeArray:function(e,t){var n=t||[];return null!=e&&(d(Object(e))?C.merge(n,"string"==typeof e?[e]:e):l.call(n,e)),n},inArray:function(e,t,n){return null==t?-1:r.call(t,e,n)},merge:function(e,t){for(var n=+t.length,i=0,r=e.length;i<n;i++)e[r++]=t[i];return e.length=r,e},grep:function(e,t,n){for(var i=[],r=0,o=e.length,s=!n;r<o;r++)!t(e[r],r)!=s&&i.push(e[r]);return i},map:function(e,t,n){var i,r,o=0,s=[];if(d(e))for(i=e.length;o<i;o++)null!=(r=t(e[o],o,n))&&s.push(r);else for(o in e)null!=(r=t(e[o],o,n))&&s.push(r);return m(s)},guid:1,support:y}),"function"==typeof Symbol&&(C.fn[Symbol.iterator]=t[Symbol.iterator]),C.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){n["[object "+t+"]"]=t.toLowerCase()});var h=function(n){function f(e,t){var n="0x"+e.slice(1)-65536;return t||(n<0?String.fromCharCode(65536+n):String.fromCharCode(n>>10|55296,1023&n|56320))}function r(){x()}var e,h,_,o,s,p,d,g,w,l,u,x,E,a,T,m,c,v,y,C="sizzle"+ +new Date,b=n.document,S=0,i=0,A=le(),N=le(),k=le(),D=le(),j=function(e,t){return e===t&&(u=!0),0},O={}.hasOwnProperty,t=[],L=t.pop,I=t.push,q=t.push,P=t.slice,H=function(e,t){for(var n=0,i=e.length;n<i;n++)if(e[n]===t)return n;return-1},R="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",M="[\\x20\\t\\r\\n\\f]",F="(?:\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",B="\\["+M+"*("+F+")(?:"+M+"*([*^$|!~]?=)"+M+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+M+"*\\]",Q=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+B+")*)|.*)\\)|)",W=new RegExp(M+"+","g"),U=new RegExp("^"+M+"+|((?:^|[^\\\\])(?:\\\\.)*)"+M+"+$","g"),$=new RegExp("^"+M+"*,"+M+"*"),z=new RegExp("^"+M+"*([>+~]|"+M+")"+M+"*"),V=new RegExp(M+"|>"),X=new RegExp(Q),Y=new RegExp("^"+F+"$"),K={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F+"|[*])"),ATTR:new RegExp("^"+B),PSEUDO:new RegExp("^"+Q),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+M+"*(even|odd|(([+-]|)(\\d*)n|)"+M+"*(?:([+-]|)"+M+"*(\\d+)|))"+M+"*\\)|)","i"),bool:new RegExp("^(?:"+R+")$","i"),needsContext:new RegExp("^"+M+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+M+"*((?:-\\d)?\\d*)"+M+"*\\)|)(?=[^-]|$)","i")},G=/HTML$/i,J=/^(?:input|select|textarea|button)$/i,Z=/^h\d$/i,ee=/^[^{]+\{\s*\[native \w/,te=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ne=/[+~]/,ie=new RegExp("\\\\[\\da-fA-F]{1,6}"+M+"?|\\\\([^\\r\\n\\f])","g"),re=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,oe=function(e,t){return t?"\0"===e?"�":e.slice(0,-1)+"\\"+e.charCodeAt(e.length-1).toString(16)+" ":"\\"+e},se=ye(function(e){return!0===e.disabled&&"fieldset"===e.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{q.apply(t=P.call(b.childNodes),b.childNodes),t[b.childNodes.length].nodeType}catch(e){q={apply:t.length?function(e,t){I.apply(e,P.call(t))}:function(e,t){for(var n=e.length,i=0;e[n++]=t[i++];);e.length=n-1}}}function ae(e,t,n,i){var r,o,s,a,l,u,c,f=t&&t.ownerDocument,d=t?t.nodeType:9;if(n=n||[],"string"!=typeof e||!e||1!==d&&9!==d&&11!==d)return n;if(!i&&(x(t),t=t||E,T)){if(11!==d&&(l=te.exec(e)))if(r=l[1]){if(9===d){if(!(s=t.getElementById(r)))return n;if(s.id===r)return n.push(s),n}else if(f&&(s=f.getElementById(r))&&y(t,s)&&s.id===r)return n.push(s),n}else{if(l[2])return q.apply(n,t.getElementsByTagName(e)),n;if((r=l[3])&&h.getElementsByClassName&&t.getElementsByClassName)return q.apply(n,t.getElementsByClassName(r)),n}if(h.qsa&&!D[e+" "]&&(!m||!m.test(e))&&(1!==d||"object"!==t.nodeName.toLowerCase())){if(c=e,f=t,1===d&&(V.test(e)||z.test(e))){for((f=ne.test(e)&&ge(t.parentNode)||t)===t&&h.scope||((a=t.getAttribute("id"))?a=a.replace(re,oe):t.setAttribute("id",a=C)),o=(u=p(e)).length;o--;)u[o]=(a?"#"+a:":scope")+" "+ve(u[o]);c=u.join(",")}try{return q.apply(n,f.querySelectorAll(c)),n}catch(t){D(e,!0)}finally{a===C&&t.removeAttribute("id")}}}return g(e.replace(U,"$1"),t,n,i)}function le(){var i=[];return function e(t,n){return i.push(t+" ")>_.cacheLength&&delete e[i.shift()],e[t+" "]=n}}function ue(e){return e[C]=!0,e}function ce(e){var t=E.createElement("fieldset");try{return!!e(t)}catch(e){return!1}finally{t.parentNode&&t.parentNode.removeChild(t),t=null}}function fe(e,t){for(var n=e.split("|"),i=n.length;i--;)_.attrHandle[n[i]]=t}function de(e,t){var n=t&&e,i=n&&1===e.nodeType&&1===t.nodeType&&e.sourceIndex-t.sourceIndex;if(i)return i;if(n)for(;n=n.nextSibling;)if(n===t)return-1;return e?1:-1}function he(t){return function(e){return"form"in e?e.parentNode&&!1===e.disabled?"label"in e?"label"in e.parentNode?e.parentNode.disabled===t:e.disabled===t:e.isDisabled===t||e.isDisabled!==!t&&se(e)===t:e.disabled===t:"label"in e&&e.disabled===t}}function pe(s){return ue(function(o){return o=+o,ue(function(e,t){for(var n,i=s([],e.length,o),r=i.length;r--;)e[n=i[r]]&&(e[n]=!(t[n]=e[n]))})})}function ge(e){return e&&void 0!==e.getElementsByTagName&&e}for(e in h=ae.support={},s=ae.isXML=function(e){var t=e.namespaceURI,n=(e.ownerDocument||e).documentElement;return!G.test(t||n&&n.nodeName||"HTML")},x=ae.setDocument=function(e){var t,n,i=e?e.ownerDocument||e:b;return i!=E&&9===i.nodeType&&i.documentElement&&(a=(E=i).documentElement,T=!s(E),b!=E&&(n=E.defaultView)&&n.top!==n&&(n.addEventListener?n.addEventListener("unload",r,!1):n.attachEvent&&n.attachEvent("onunload",r)),h.scope=ce(function(e){return a.appendChild(e).appendChild(E.createElement("div")),void 0!==e.querySelectorAll&&!e.querySelectorAll(":scope fieldset div").length}),h.attributes=ce(function(e){return e.className="i",!e.getAttribute("className")}),h.getElementsByTagName=ce(function(e){return e.appendChild(E.createComment("")),!e.getElementsByTagName("*").length}),h.getElementsByClassName=ee.test(E.getElementsByClassName),h.getById=ce(function(e){return a.appendChild(e).id=C,!E.getElementsByName||!E.getElementsByName(C).length}),h.getById?(_.filter.ID=function(e){var t=e.replace(ie,f);return function(e){return e.getAttribute("id")===t}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n=t.getElementById(e);return n?[n]:[]}}):(_.filter.ID=function(e){var n=e.replace(ie,f);return function(e){var t=void 0!==e.getAttributeNode&&e.getAttributeNode("id");return t&&t.value===n}},_.find.ID=function(e,t){if(void 0!==t.getElementById&&T){var n,i,r,o=t.getElementById(e);if(o){if((n=o.getAttributeNode("id"))&&n.value===e)return[o];for(r=t.getElementsByName(e),i=0;o=r[i++];)if((n=o.getAttributeNode("id"))&&n.value===e)return[o]}return[]}}),_.find.TAG=h.getElementsByTagName?function(e,t){return void 0!==t.getElementsByTagName?t.getElementsByTagName(e):h.qsa?t.querySelectorAll(e):void 0}:function(e,t){var n,i=[],r=0,o=t.getElementsByTagName(e);if("*"!==e)return o;for(;n=o[r++];)1===n.nodeType&&i.push(n);return i},_.find.CLASS=h.getElementsByClassName&&function(e,t){if(void 0!==t.getElementsByClassName&&T)return t.getElementsByClassName(e)},c=[],m=[],(h.qsa=ee.test(E.querySelectorAll))&&(ce(function(e){var t;a.appendChild(e).innerHTML="<a id='"+C+"'></a><select id='"+C+"-\r\\' msallowcapture=''><option selected=''></option></select>",e.querySelectorAll("[msallowcapture^='']").length&&m.push("[*^$]="+M+"*(?:''|\"\")"),e.querySelectorAll("[selected]").length||m.push("\\["+M+"*(?:value|"+R+")"),e.querySelectorAll("[id~="+C+"-]").length||m.push("~="),(t=E.createElement("input")).setAttribute("name",""),e.appendChild(t),e.querySelectorAll("[name='']").length||m.push("\\["+M+"*name"+M+"*="+M+"*(?:''|\"\")"),e.querySelectorAll(":checked").length||m.push(":checked"),e.querySelectorAll("a#"+C+"+*").length||m.push(".#.+[+~]"),e.querySelectorAll("\\\f"),m.push("[\\r\\n\\f]")}),ce(function(e){e.innerHTML="<a href='' disabled='disabled'></a><select disabled='disabled'><option/></select>";var t=E.createElement("input");t.setAttribute("type","hidden"),e.appendChild(t).setAttribute("name","D"),e.querySelectorAll("[name=d]").length&&m.push("name"+M+"*[*^$|!~]?="),2!==e.querySelectorAll(":enabled").length&&m.push(":enabled",":disabled"),a.appendChild(e).disabled=!0,2!==e.querySelectorAll(":disabled").length&&m.push(":enabled",":disabled"),e.querySelectorAll("*,:x"),m.push(",.*:")})),(h.matchesSelector=ee.test(v=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector))&&ce(function(e){h.disconnectedMatch=v.call(e,"*"),v.call(e,"[s!='']:x"),c.push("!=",Q)}),m=m.length&&new RegExp(m.join("|")),c=c.length&&new RegExp(c.join("|")),t=ee.test(a.compareDocumentPosition),y=t||ee.test(a.contains)?function(e,t){var n=9===e.nodeType?e.documentElement:e,i=t&&t.parentNode;return e===i||!(!i||1!==i.nodeType||!(n.contains?n.contains(i):e.compareDocumentPosition&&16&e.compareDocumentPosition(i)))}:function(e,t){if(t)for(;t=t.parentNode;)if(t===e)return!0;return!1},j=t?function(e,t){if(e===t)return u=!0,0;var n=!e.compareDocumentPosition-!t.compareDocumentPosition;return n||(1&(n=(e.ownerDocument||e)==(t.ownerDocument||t)?e.compareDocumentPosition(t):1)||!h.sortDetached&&t.compareDocumentPosition(e)===n?e==E||e.ownerDocument==b&&y(b,e)?-1:t==E||t.ownerDocument==b&&y(b,t)?1:l?H(l,e)-H(l,t):0:4&n?-1:1)}:function(e,t){if(e===t)return u=!0,0;var n,i=0,r=e.parentNode,o=t.parentNode,s=[e],a=[t];if(!r||!o)return e==E?-1:t==E?1:r?-1:o?1:l?H(l,e)-H(l,t):0;if(r===o)return de(e,t);for(n=e;n=n.parentNode;)s.unshift(n);for(n=t;n=n.parentNode;)a.unshift(n);for(;s[i]===a[i];)i++;return i?de(s[i],a[i]):s[i]==b?-1:a[i]==b?1:0}),E},ae.matches=function(e,t){return ae(e,null,null,t)},ae.matchesSelector=function(e,t){if(x(e),h.matchesSelector&&T&&!D[t+" "]&&(!c||!c.test(t))&&(!m||!m.test(t)))try{var n=v.call(e,t);if(n||h.disconnectedMatch||e.document&&11!==e.document.nodeType)return n}catch(e){D(t,!0)}return 0<ae(t,E,null,[e]).length},ae.contains=function(e,t){return(e.ownerDocument||e)!=E&&x(e),y(e,t)},ae.attr=function(e,t){(e.ownerDocument||e)!=E&&x(e);var n=_.attrHandle[t.toLowerCase()],i=n&&O.call(_.attrHandle,t.toLowerCase())?n(e,t,!T):void 0;return void 0!==i?i:h.attributes||!T?e.getAttribute(t):(i=e.getAttributeNode(t))&&i.specified?i.value:null},ae.escape=function(e){return(e+"").replace(re,oe)},ae.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},ae.uniqueSort=function(e){var t,n=[],i=0,r=0;if(u=!h.detectDuplicates,l=!h.sortStable&&e.slice(0),e.sort(j),u){for(;t=e[r++];)t===e[r]&&(i=n.push(r));for(;i--;)e.splice(n[i],1)}return l=null,e},o=ae.getText=function(e){var t,n="",i=0,r=e.nodeType;if(r){if(1===r||9===r||11===r){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=o(e)}else if(3===r||4===r)return e.nodeValue}else for(;t=e[i++];)n+=o(t);return n},(_=ae.selectors={cacheLength:50,createPseudo:ue,match:K,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace(ie,f),e[3]=(e[3]||e[4]||e[5]||"").replace(ie,f),"~="===e[2]&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),"nth"===e[1].slice(0,3)?(e[3]||ae.error(e[0]),e[4]=+(e[4]?e[5]+(e[6]||1):2*("even"===e[3]||"odd"===e[3])),e[5]=+(e[7]+e[8]||"odd"===e[3])):e[3]&&ae.error(e[0]),e},PSEUDO:function(e){var t,n=!e[6]&&e[2];return K.CHILD.test(e[0])?null:(e[3]?e[2]=e[4]||e[5]||"":n&&X.test(n)&&(t=p(n,!0))&&(t=n.indexOf(")",n.length-t)-n.length)&&(e[0]=e[0].slice(0,t),e[2]=n.slice(0,t)),e.slice(0,3))}},filter:{TAG:function(e){var t=e.replace(ie,f).toLowerCase();return"*"===e?function(){return!0}:function(e){return e.nodeName&&e.nodeName.toLowerCase()===t}},CLASS:function(e){var t=A[e+" "];return t||(t=new RegExp("(^|"+M+")"+e+"("+M+"|$)"))&&A(e,function(e){return t.test("string"==typeof e.className&&e.className||void 0!==e.getAttribute&&e.getAttribute("class")||"")})},ATTR:function(n,i,r){return function(e){var t=ae.attr(e,n);return null==t?"!="===i:!i||(t+="","="===i?t===r:"!="===i?t!==r:"^="===i?r&&0===t.indexOf(r):"*="===i?r&&-1<t.indexOf(r):"$="===i?r&&t.slice(-r.length)===r:"~="===i?-1<(" "+t.replace(W," ")+" ").indexOf(r):"|="===i&&(t===r||t.slice(0,r.length+1)===r+"-"))}},CHILD:function(p,e,t,g,m){var v="nth"!==p.slice(0,3),y="last"!==p.slice(-4),b="of-type"===e;return 1===g&&0===m?function(e){return!!e.parentNode}:function(e,t,n){var i,r,o,s,a,l,u=v!=y?"nextSibling":"previousSibling",c=e.parentNode,f=b&&e.nodeName.toLowerCase(),d=!n&&!b,h=!1;if(c){if(v){for(;u;){for(s=e;s=s[u];)if(b?s.nodeName.toLowerCase()===f:1===s.nodeType)return!1;l=u="only"===p&&!l&&"nextSibling"}return!0}if(l=[y?c.firstChild:c.lastChild],y&&d){for(h=(a=(i=(r=(o=(s=c)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]||[])[0]===S&&i[1])&&i[2],s=a&&c.childNodes[a];s=++a&&s&&s[u]||(h=a=0)||l.pop();)if(1===s.nodeType&&++h&&s===e){r[p]=[S,a,h];break}}else if(d&&(h=a=(i=(r=(o=(s=e)[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]||[])[0]===S&&i[1]),!1===h)for(;(s=++a&&s&&s[u]||(h=a=0)||l.pop())&&((b?s.nodeName.toLowerCase()!==f:1!==s.nodeType)||!++h||(d&&((r=(o=s[C]||(s[C]={}))[s.uniqueID]||(o[s.uniqueID]={}))[p]=[S,h]),s!==e)););return(h-=m)===g||h%g==0&&0<=h/g}}},PSEUDO:function(e,o){var t,s=_.pseudos[e]||_.setFilters[e.toLowerCase()]||ae.error("unsupported pseudo: "+e);return s[C]?s(o):1<s.length?(t=[e,e,"",o],_.setFilters.hasOwnProperty(e.toLowerCase())?ue(function(e,t){for(var n,i=s(e,o),r=i.length;r--;)e[n=H(e,i[r])]=!(t[n]=i[r])}):function(e){return s(e,0,t)}):s}},pseudos:{not:ue(function(e){var i=[],r=[],a=d(e.replace(U,"$1"));return a[C]?ue(function(e,t,n,i){for(var r,o=a(e,null,i,[]),s=e.length;s--;)(r=o[s])&&(e[s]=!(t[s]=r))}):function(e,t,n){return i[0]=e,a(i,null,n,r),i[0]=null,!r.pop()}}),has:ue(function(t){return function(e){return 0<ae(t,e).length}}),contains:ue(function(t){return t=t.replace(ie,f),function(e){return-1<(e.textContent||o(e)).indexOf(t)}}),lang:ue(function(n){return Y.test(n||"")||ae.error("unsupported lang: "+n),n=n.replace(ie,f).toLowerCase(),function(e){var t;do{if(t=T?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(t=t.toLowerCase())===n||0===t.indexOf(n+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}}),target:function(e){var t=n.location&&n.location.hash;return t&&t.slice(1)===e.id},root:function(e){return e===a},focus:function(e){return e===E.activeElement&&(!E.hasFocus||E.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},enabled:he(!1),disabled:he(!0),checked:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&!!e.checked||"option"===t&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,!0===e.selected},empty:function(e){for(e=e.firstChild;e;e=e.nextSibling)if(e.nodeType<6)return!1;return!0},parent:function(e){return!_.pseudos.empty(e)},header:function(e){return Z.test(e.nodeName)},input:function(e){return J.test(e.nodeName)},button:function(e){var t=e.nodeName.toLowerCase();return"input"===t&&"button"===e.type||"button"===t},text:function(e){var t;return"input"===e.nodeName.toLowerCase()&&"text"===e.type&&(null==(t=e.getAttribute("type"))||"text"===t.toLowerCase())},first:pe(function(){return[0]}),last:pe(function(e,t){return[t-1]}),eq:pe(function(e,t,n){return[n<0?n+t:n]}),even:pe(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:pe(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:pe(function(e,t,n){for(var i=n<0?n+t:t<n?t:n;0<=--i;)e.push(i);return e}),gt:pe(function(e,t,n){for(var i=n<0?n+t:n;++i<t;)e.push(i);return e})}}).pseudos.nth=_.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})_.pseudos[e]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(e);for(e in{submit:!0,reset:!0})_.pseudos[e]=function(n){return function(e){var t=e.nodeName.toLowerCase();return("input"===t||"button"===t)&&e.type===n}}(e);function me(){}function ve(e){for(var t=0,n=e.length,i="";t<n;t++)i+=e[t].value;return i}function ye(a,e,t){var l=e.dir,u=e.next,c=u||l,f=t&&"parentNode"===c,d=i++;return e.first?function(e,t,n){for(;e=e[l];)if(1===e.nodeType||f)return a(e,t,n);return!1}:function(e,t,n){var i,r,o,s=[S,d];if(n){for(;e=e[l];)if((1===e.nodeType||f)&&a(e,t,n))return!0}else for(;e=e[l];)if(1===e.nodeType||f)if(r=(o=e[C]||(e[C]={}))[e.uniqueID]||(o[e.uniqueID]={}),u&&u===e.nodeName.toLowerCase())e=e[l]||e;else{if((i=r[c])&&i[0]===S&&i[1]===d)return s[2]=i[2];if((r[c]=s)[2]=a(e,t,n))return!0}return!1}}function be(r){return 1<r.length?function(e,t,n){for(var i=r.length;i--;)if(!r[i](e,t,n))return!1;return!0}:r[0]}function _e(e,t,n,i,r){for(var o,s=[],a=0,l=e.length,u=null!=t;a<l;a++)(o=e[a])&&(n&&!n(o,i,r)||(s.push(o),u&&t.push(a)));return s}function we(e){for(var r,t,n,i=e.length,o=_.relative[e[0].type],s=o||_.relative[" "],a=o?1:0,l=ye(function(e){return e===r},s,!0),u=ye(function(e){return-1<H(r,e)},s,!0),c=[function(e,t,n){var i=!o&&(n||t!==w)||((r=t).nodeType?l:u)(e,t,n);return r=null,i}];a<i;a++)if(t=_.relative[e[a].type])c=[ye(be(c),t)];else{if((t=_.filter[e[a].type].apply(null,e[a].matches))[C]){for(n=++a;n<i&&!_.relative[e[n].type];n++);return function e(h,p,g,m,v,t){return m&&!m[C]&&(m=e(m)),v&&!v[C]&&(v=e(v,t)),ue(function(e,t,n,i){var r,o,s,a=[],l=[],u=t.length,c=e||function(e,t,n){for(var i=0,r=t.length;i<r;i++)ae(e,t[i],n);return n}(p||"*",n.nodeType?[n]:n,[]),f=!h||!e&&p?c:_e(c,a,h,n,i),d=g?v||(e?h:u||m)?[]:t:f;if(g&&g(f,d,n,i),m)for(r=_e(d,l),m(r,[],n,i),o=r.length;o--;)(s=r[o])&&(d[l[o]]=!(f[l[o]]=s));if(e){if(v||h){if(v){for(r=[],o=d.length;o--;)(s=d[o])&&r.push(f[o]=s);v(null,d=[],r,i)}for(o=d.length;o--;)(s=d[o])&&-1<(r=v?H(e,s):a[o])&&(e[r]=!(t[r]=s))}}else d=_e(d===t?d.splice(u,d.length):d),v?v(null,t,d,i):q.apply(t,d)})}(1<a&&be(c),1<a&&ve(e.slice(0,a-1).concat({value:" "===e[a-2].type?"*":""})).replace(U,"$1"),t,a<n&&we(e.slice(a,n)),n<i&&we(e=e.slice(n)),n<i&&ve(e))}c.push(t)}return be(c)}return me.prototype=_.filters=_.pseudos,_.setFilters=new me,p=ae.tokenize=function(e,t){var n,i,r,o,s,a,l,u=N[e+" "];if(u)return t?0:u.slice(0);for(s=e,a=[],l=_.preFilter;s;){for(o in n&&!(i=$.exec(s))||(i&&(s=s.slice(i[0].length)||s),a.push(r=[])),n=!1,(i=z.exec(s))&&(n=i.shift(),r.push({value:n,type:i[0].replace(U," ")}),s=s.slice(n.length)),_.filter)!(i=K[o].exec(s))||l[o]&&!(i=l[o](i))||(n=i.shift(),r.push({value:n,type:o,matches:i}),s=s.slice(n.length));if(!n)break}return t?s.length:s?ae.error(e):N(e,a).slice(0)},d=ae.compile=function(e,t){var n,m,v,y,b,i,r=[],o=[],s=k[e+" "];if(!s){for(n=(t=t||p(e)).length;n--;)(s=we(t[n]))[C]?r.push(s):o.push(s);(s=k(e,(m=o,y=0<(v=r).length,b=0<m.length,i=function(e,t,n,i,r){var o,s,a,l=0,u="0",c=e&&[],f=[],d=w,h=e||b&&_.find.TAG("*",r),p=S+=null==d?1:Math.random()||.1,g=h.length;for(r&&(w=t==E||t||r);u!==g&&null!=(o=h[u]);u++){if(b&&o){for(s=0,t||o.ownerDocument==E||(x(o),n=!T);a=m[s++];)if(a(o,t||E,n)){i.push(o);break}r&&(S=p)}y&&((o=!a&&o)&&l--,e&&c.push(o))}if(l+=u,y&&u!==l){for(s=0;a=v[s++];)a(c,f,t,n);if(e){if(0<l)for(;u--;)c[u]||f[u]||(f[u]=L.call(i));f=_e(f)}q.apply(i,f),r&&!e&&0<f.length&&1<l+v.length&&ae.uniqueSort(i)}return r&&(S=p,w=d),c},y?ue(i):i))).selector=e}return s},g=ae.select=function(e,t,n,i){var r,o,s,a,l,u="function"==typeof e&&e,c=!i&&p(e=u.selector||e);if(n=n||[],1===c.length){if(2<(o=c[0]=c[0].slice(0)).length&&"ID"===(s=o[0]).type&&9===t.nodeType&&T&&_.relative[o[1].type]){if(!(t=(_.find.ID(s.matches[0].replace(ie,f),t)||[])[0]))return n;u&&(t=t.parentNode),e=e.slice(o.shift().value.length)}for(r=K.needsContext.test(e)?0:o.length;r--&&(s=o[r],!_.relative[a=s.type]);)if((l=_.find[a])&&(i=l(s.matches[0].replace(ie,f),ne.test(o[0].type)&&ge(t.parentNode)||t))){if(o.splice(r,1),!(e=i.length&&ve(o)))return q.apply(n,i),n;break}}return(u||d(e,c))(i,t,!T,n,!t||ne.test(e)&&ge(t.parentNode)||t),n},h.sortStable=C.split("").sort(j).join("")===C,h.detectDuplicates=!!u,x(),h.sortDetached=ce(function(e){return 1&e.compareDocumentPosition(E.createElement("fieldset"))}),ce(function(e){return e.innerHTML="<a href='#'></a>","#"===e.firstChild.getAttribute("href")})||fe("type|href|height|width",function(e,t,n){if(!n)return e.getAttribute(t,"type"===t.toLowerCase()?1:2)}),h.attributes&&ce(function(e){return e.innerHTML="<input/>",e.firstChild.setAttribute("value",""),""===e.firstChild.getAttribute("value")})||fe("value",function(e,t,n){if(!n&&"input"===e.nodeName.toLowerCase())return e.defaultValue}),ce(function(e){return null==e.getAttribute("disabled")})||fe(R,function(e,t,n){var i;if(!n)return!0===e[t]?t.toLowerCase():(i=e.getAttributeNode(t))&&i.specified?i.value:null}),ae}(E);C.find=h,C.expr=h.selectors,C.expr[":"]=C.expr.pseudos,C.uniqueSort=C.unique=h.uniqueSort,C.text=h.getText,C.isXMLDoc=h.isXML,C.contains=h.contains,C.escapeSelector=h.escape;function p(e,t,n){for(var i=[],r=void 0!==n;(e=e[t])&&9!==e.nodeType;)if(1===e.nodeType){if(r&&C(e).is(n))break;i.push(e)}return i}function x(e,t){for(var n=[];e;e=e.nextSibling)1===e.nodeType&&e!==t&&n.push(e);return n}var S=C.expr.match.needsContext;function A(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()}var N=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;function k(e,n,i){return b(n)?C.grep(e,function(e,t){return!!n.call(e,t,e)!==i}):n.nodeType?C.grep(e,function(e){return e===n!==i}):"string"!=typeof n?C.grep(e,function(e){return-1<r.call(n,e)!==i}):C.filter(n,e,i)}C.filter=function(e,t,n){var i=t[0];return n&&(e=":not("+e+")"),1===t.length&&1===i.nodeType?C.find.matchesSelector(i,e)?[i]:[]:C.find.matches(e,C.grep(t,function(e){return 1===e.nodeType}))},C.fn.extend({find:function(e){var t,n,i=this.length,r=this;if("string"!=typeof e)return this.pushStack(C(e).filter(function(){for(t=0;t<i;t++)if(C.contains(r[t],this))return!0}));for(n=this.pushStack([]),t=0;t<i;t++)C.find(e,r[t],n);return 1<i?C.uniqueSort(n):n},filter:function(e){return this.pushStack(k(this,e||[],!1))},not:function(e){return this.pushStack(k(this,e||[],!0))},is:function(e){return!!k(this,"string"==typeof e&&S.test(e)?C(e):e||[],!1).length}});var D,j=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(C.fn.init=function(e,t,n){var i,r;if(!e)return this;if(n=n||D,"string"!=typeof e)return e.nodeType?(this[0]=e,this.length=1,this):b(e)?void 0!==n.ready?n.ready(e):e(C):C.makeArray(e,this);if(!(i="<"===e[0]&&">"===e[e.length-1]&&3<=e.length?[null,e,null]:j.exec(e))||!i[1]&&t)return!t||t.jquery?(t||n).find(e):this.constructor(t).find(e);if(i[1]){if(t=t instanceof C?t[0]:t,C.merge(this,C.parseHTML(i[1],t&&t.nodeType?t.ownerDocument||t:T,!0)),N.test(i[1])&&C.isPlainObject(t))for(i in t)b(this[i])?this[i](t[i]):this.attr(i,t[i]);return this}return(r=T.getElementById(i[2]))&&(this[0]=r,this.length=1),this}).prototype=C.fn,D=C(T);var O=/^(?:parents|prev(?:Until|All))/,L={children:!0,contents:!0,next:!0,prev:!0};function I(e,t){for(;(e=e[t])&&1!==e.nodeType;);return e}C.fn.extend({has:function(e){var t=C(e,this),n=t.length;return this.filter(function(){for(var e=0;e<n;e++)if(C.contains(this,t[e]))return!0})},closest:function(e,t){var n,i=0,r=this.length,o=[],s="string"!=typeof e&&C(e);if(!S.test(e))for(;i<r;i++)for(n=this[i];n&&n!==t;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&C.find.matchesSelector(n,e))){o.push(n);break}return this.pushStack(1<o.length?C.uniqueSort(o):o)},index:function(e){return e?"string"==typeof e?r.call(C(e),this[0]):r.call(this,e.jquery?e[0]:e):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(e,t){return this.pushStack(C.uniqueSort(C.merge(this.get(),C(e,t))))},addBack:function(e){return this.add(null==e?this.prevObject:this.prevObject.filter(e))}}),C.each({parent:function(e){var t=e.parentNode;return t&&11!==t.nodeType?t:null},parents:function(e){return p(e,"parentNode")},parentsUntil:function(e,t,n){return p(e,"parentNode",n)},next:function(e){return I(e,"nextSibling")},prev:function(e){return I(e,"previousSibling")},nextAll:function(e){return p(e,"nextSibling")},prevAll:function(e){return p(e,"previousSibling")},nextUntil:function(e,t,n){return p(e,"nextSibling",n)},prevUntil:function(e,t,n){return p(e,"previousSibling",n)},siblings:function(e){return x((e.parentNode||{}).firstChild,e)},children:function(e){return x(e.firstChild)},contents:function(e){return null!=e.contentDocument&&i(e.contentDocument)?e.contentDocument:(A(e,"template")&&(e=e.content||e),C.merge([],e.childNodes))}},function(i,r){C.fn[i]=function(e,t){var n=C.map(this,r,e);return"Until"!==i.slice(-5)&&(t=e),t&&"string"==typeof t&&(n=C.filter(t,n)),1<this.length&&(L[i]||C.uniqueSort(n),O.test(i)&&n.reverse()),this.pushStack(n)}});var q=/[^\x20\t\r\n\f]+/g;function P(e){return e}function H(e){throw e}function R(e,t,n,i){var r;try{e&&b(r=e.promise)?r.call(e).done(t).fail(n):e&&b(r=e.then)?r.call(e,t,n):t.apply(void 0,[e].slice(i))}catch(e){n.apply(void 0,[e])}}C.Callbacks=function(i){var n;i="string"==typeof i?(n={},C.each(i.match(q)||[],function(e,t){n[t]=!0}),n):C.extend({},i);function r(){for(s=s||i.once,t=o=!0;l.length;u=-1)for(e=l.shift();++u<a.length;)!1===a[u].apply(e[0],e[1])&&i.stopOnFalse&&(u=a.length,e=!1);i.memory||(e=!1),o=!1,s&&(a=e?[]:"")}var o,e,t,s,a=[],l=[],u=-1,c={add:function(){return a&&(e&&!o&&(u=a.length-1,l.push(e)),function n(e){C.each(e,function(e,t){b(t)?i.unique&&c.has(t)||a.push(t):t&&t.length&&"string"!==w(t)&&n(t)})}(arguments),e&&!o&&r()),this},remove:function(){return C.each(arguments,function(e,t){for(var n;-1<(n=C.inArray(t,a,n));)a.splice(n,1),n<=u&&u--}),this},has:function(e){return e?-1<C.inArray(e,a):0<a.length},empty:function(){return a=a&&[],this},disable:function(){return s=l=[],a=e="",this},disabled:function(){return!a},lock:function(){return s=l=[],e||o||(a=e=""),this},locked:function(){return!!s},fireWith:function(e,t){return s||(t=[e,(t=t||[]).slice?t.slice():t],l.push(t),o||r()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!t}};return c},C.extend({Deferred:function(e){var o=[["notify","progress",C.Callbacks("memory"),C.Callbacks("memory"),2],["resolve","done",C.Callbacks("once memory"),C.Callbacks("once memory"),0,"resolved"],["reject","fail",C.Callbacks("once memory"),C.Callbacks("once memory"),1,"rejected"]],r="pending",s={state:function(){return r},always:function(){return a.done(arguments).fail(arguments),this},catch:function(e){return s.then(null,e)},pipe:function(){var r=arguments;return C.Deferred(function(i){C.each(o,function(e,t){var n=b(r[t[4]])&&r[t[4]];a[t[1]](function(){var e=n&&n.apply(this,arguments);e&&b(e.promise)?e.promise().progress(i.notify).done(i.resolve).fail(i.reject):i[t[0]+"With"](this,n?[e]:arguments)})}),r=null}).promise()},then:function(t,n,i){var l=0;function u(r,o,s,a){return function(){function e(){var e,t;if(!(r<l)){if((e=s.apply(n,i))===o.promise())throw new TypeError("Thenable self-resolution");t=e&&("object"==typeof e||"function"==typeof e)&&e.then,b(t)?a?t.call(e,u(l,o,P,a),u(l,o,H,a)):(l++,t.call(e,u(l,o,P,a),u(l,o,H,a),u(l,o,P,o.notifyWith))):(s!==P&&(n=void 0,i=[e]),(a||o.resolveWith)(n,i))}}var n=this,i=arguments,t=a?e:function(){try{e()}catch(e){C.Deferred.exceptionHook&&C.Deferred.exceptionHook(e,t.stackTrace),l<=r+1&&(s!==H&&(n=void 0,i=[e]),o.rejectWith(n,i))}};r?t():(C.Deferred.getStackHook&&(t.stackTrace=C.Deferred.getStackHook()),E.setTimeout(t))}}return C.Deferred(function(e){o[0][3].add(u(0,e,b(i)?i:P,e.notifyWith)),o[1][3].add(u(0,e,b(t)?t:P)),o[2][3].add(u(0,e,b(n)?n:H))}).promise()},promise:function(e){return null!=e?C.extend(e,s):s}},a={};return C.each(o,function(e,t){var n=t[2],i=t[5];s[t[1]]=n.add,i&&n.add(function(){r=i},o[3-e][2].disable,o[3-e][3].disable,o[0][2].lock,o[0][3].lock),n.add(t[3].fire),a[t[0]]=function(){return a[t[0]+"With"](this===a?void 0:this,arguments),this},a[t[0]+"With"]=n.fireWith}),s.promise(a),e&&e.call(a,a),a},when:function(e){function t(t){return function(e){r[t]=this,o[t]=1<arguments.length?a.call(arguments):e,--n||s.resolveWith(r,o)}}var n=arguments.length,i=n,r=Array(i),o=a.call(arguments),s=C.Deferred();if(n<=1&&(R(e,s.done(t(i)).resolve,s.reject,!n),"pending"===s.state()||b(o[i]&&o[i].then)))return s.then();for(;i--;)R(o[i],t(i),s.reject);return s.promise()}});var M=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;C.Deferred.exceptionHook=function(e,t){E.console&&E.console.warn&&e&&M.test(e.name)&&E.console.warn("jQuery.Deferred exception: "+e.message,e.stack,t)},C.readyException=function(e){E.setTimeout(function(){throw e})};var F=C.Deferred();function B(){T.removeEventListener("DOMContentLoaded",B),E.removeEventListener("load",B),C.ready()}C.fn.ready=function(e){return F.then(e).catch(function(e){C.readyException(e)}),this},C.extend({isReady:!1,readyWait:1,ready:function(e){(!0===e?--C.readyWait:C.isReady)||(C.isReady=!0)!==e&&0<--C.readyWait||F.resolveWith(T,[C])}}),C.ready.then=F.then,"complete"===T.readyState||"loading"!==T.readyState&&!T.documentElement.doScroll?E.setTimeout(C.ready):(T.addEventListener("DOMContentLoaded",B),E.addEventListener("load",B));var Q=function(e,t,n,i,r,o,s){var a=0,l=e.length,u=null==n;if("object"===w(n))for(a in r=!0,n)Q(e,t,a,n[a],!0,o,s);else if(void 0!==i&&(r=!0,b(i)||(s=!0),u&&(t=s?(t.call(e,i),null):(u=t,function(e,t,n){return u.call(C(e),n)})),t))for(;a<l;a++)t(e[a],n,s?i:i.call(e[a],a,t(e[a],n)));return r?e:u?t.call(e):l?t(e[0],n):o},W=/^-ms-/,U=/-([a-z])/g;function $(e,t){return t.toUpperCase()}function z(e){return e.replace(W,"ms-").replace(U,$)}function V(e){return 1===e.nodeType||9===e.nodeType||!+e.nodeType}function X(){this.expando=C.expando+X.uid++}X.uid=1,X.prototype={cache:function(e){var t=e[this.expando];return t||(t={},V(e)&&(e.nodeType?e[this.expando]=t:Object.defineProperty(e,this.expando,{value:t,configurable:!0}))),t},set:function(e,t,n){var i,r=this.cache(e);if("string"==typeof t)r[z(t)]=n;else for(i in t)r[z(i)]=t[i];return r},get:function(e,t){return void 0===t?this.cache(e):e[this.expando]&&e[this.expando][z(t)]},access:function(e,t,n){return void 0===t||t&&"string"==typeof t&&void 0===n?this.get(e,t):(this.set(e,t,n),void 0!==n?n:t)},remove:function(e,t){var n,i=e[this.expando];if(void 0!==i){if(void 0!==t){n=(t=Array.isArray(t)?t.map(z):(t=z(t))in i?[t]:t.match(q)||[]).length;for(;n--;)delete i[t[n]]}void 0!==t&&!C.isEmptyObject(i)||(e.nodeType?e[this.expando]=void 0:delete e[this.expando])}},hasData:function(e){var t=e[this.expando];return void 0!==t&&!C.isEmptyObject(t)}};var Y=new X,K=new X,G=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,J=/[A-Z]/g;function Z(e,t,n){var i,r;if(void 0===n&&1===e.nodeType)if(i="data-"+t.replace(J,"-$&").toLowerCase(),"string"==typeof(n=e.getAttribute(i))){try{n="true"===(r=n)||"false"!==r&&("null"===r?null:r===+r+""?+r:G.test(r)?JSON.parse(r):r)}catch(e){}K.set(e,t,n)}else n=void 0;return n}C.extend({hasData:function(e){return K.hasData(e)||Y.hasData(e)},data:function(e,t,n){return K.access(e,t,n)},removeData:function(e,t){K.remove(e,t)},_data:function(e,t,n){return Y.access(e,t,n)},_removeData:function(e,t){Y.remove(e,t)}}),C.fn.extend({data:function(n,e){var t,i,r,o=this[0],s=o&&o.attributes;if(void 0!==n)return"object"==typeof n?this.each(function(){K.set(this,n)}):Q(this,function(e){var t;return o&&void 0===e?void 0!==(t=K.get(o,n))||void 0!==(t=Z(o,n))?t:void 0:void this.each(function(){K.set(this,n,e)})},null,e,1<arguments.length,null,!0);if(this.length&&(r=K.get(o),1===o.nodeType&&!Y.get(o,"hasDataAttrs"))){for(t=s.length;t--;)s[t]&&0===(i=s[t].name).indexOf("data-")&&(i=z(i.slice(5)),Z(o,i,r[i]));Y.set(o,"hasDataAttrs",!0)}return r},removeData:function(e){return this.each(function(){K.remove(this,e)})}}),C.extend({queue:function(e,t,n){var i;if(e)return t=(t||"fx")+"queue",i=Y.get(e,t),n&&(!i||Array.isArray(n)?i=Y.access(e,t,C.makeArray(n)):i.push(n)),i||[]},dequeue:function(e,t){t=t||"fx";var n=C.queue(e,t),i=n.length,r=n.shift(),o=C._queueHooks(e,t);"inprogress"===r&&(r=n.shift(),i--),r&&("fx"===t&&n.unshift("inprogress"),delete o.stop,r.call(e,function(){C.dequeue(e,t)},o)),!i&&o&&o.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return Y.get(e,n)||Y.access(e,n,{empty:C.Callbacks("once memory").add(function(){Y.remove(e,[t+"queue",n])})})}}),C.fn.extend({queue:function(t,n){var e=2;return"string"!=typeof t&&(n=t,t="fx",e--),arguments.length<e?C.queue(this[0],t):void 0===n?this:this.each(function(){var e=C.queue(this,t,n);C._queueHooks(this,t),"fx"===t&&"inprogress"!==e[0]&&C.dequeue(this,t)})},dequeue:function(e){return this.each(function(){C.dequeue(this,e)})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,t){function n(){--r||o.resolveWith(s,[s])}var i,r=1,o=C.Deferred(),s=this,a=this.length;for("string"!=typeof e&&(t=e,e=void 0),e=e||"fx";a--;)(i=Y.get(s[a],e+"queueHooks"))&&i.empty&&(r++,i.empty.add(n));return n(),o.promise(t)}});var ee=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,te=new RegExp("^(?:([+-])=|)("+ee+")([a-z%]*)$","i"),ne=["Top","Right","Bottom","Left"],ie=T.documentElement,re=function(e){return C.contains(e.ownerDocument,e)},oe={composed:!0};ie.getRootNode&&(re=function(e){return C.contains(e.ownerDocument,e)||e.getRootNode(oe)===e.ownerDocument});function se(e,t){return"none"===(e=t||e).style.display||""===e.style.display&&re(e)&&"none"===C.css(e,"display")}function ae(e,t,n,i){var r,o,s=20,a=i?function(){return i.cur()}:function(){return C.css(e,t,"")},l=a(),u=n&&n[3]||(C.cssNumber[t]?"":"px"),c=e.nodeType&&(C.cssNumber[t]||"px"!==u&&+l)&&te.exec(C.css(e,t));if(c&&c[3]!==u){for(l/=2,u=u||c[3],c=+l||1;s--;)C.style(e,t,c+u),(1-o)*(1-(o=a()/l||.5))<=0&&(s=0),c/=o;c*=2,C.style(e,t,c+u),n=n||[]}return n&&(c=+c||+l||0,r=n[1]?c+(n[1]+1)*n[2]:+n[2],i&&(i.unit=u,i.start=c,i.end=r)),r}var le={};function ue(e,t){for(var n,i,r,o,s,a,l=[],u=0,c=e.length;u<c;u++)(i=e[u]).style&&(n=i.style.display,t?("none"===n&&(l[u]=Y.get(i,"display")||null,l[u]||(i.style.display="")),""===i.style.display&&se(i)&&(l[u]=(a=o=r=void 0,o=i.ownerDocument,s=i.nodeName,(a=le[s])||(r=o.body.appendChild(o.createElement(s)),a=C.css(r,"display"),r.parentNode.removeChild(r),"none"===a&&(a="block"),le[s]=a)))):"none"!==n&&(l[u]="none",Y.set(i,"display",n)));for(u=0;u<c;u++)null!=l[u]&&(e[u].style.display=l[u]);return e}C.fn.extend({show:function(){return ue(this,!0)},hide:function(){return ue(this)},toggle:function(e){return"boolean"==typeof e?e?this.show():this.hide():this.each(function(){se(this)?C(this).show():C(this).hide()})}});var ce,fe=/^(?:checkbox|radio)$/i,de=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,he=/^$|^module$|\/(?:java|ecma)script/i,pe=T.createDocumentFragment().appendChild(T.createElement("div"));(ce=T.createElement("input")).setAttribute("type","radio"),ce.setAttribute("checked","checked"),ce.setAttribute("name","t"),pe.appendChild(ce),y.checkClone=pe.cloneNode(!0).cloneNode(!0).lastChild.checked,pe.innerHTML="<textarea>x</textarea>",y.noCloneChecked=!!pe.cloneNode(!0).lastChild.defaultValue,pe.innerHTML="<option></option>",y.option=!!pe.lastChild;var ge={thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function me(e,t){var n=void 0!==e.getElementsByTagName?e.getElementsByTagName(t||"*"):void 0!==e.querySelectorAll?e.querySelectorAll(t||"*"):[];return void 0===t||t&&A(e,t)?C.merge([e],n):n}function ve(e,t){for(var n=0,i=e.length;n<i;n++)Y.set(e[n],"globalEval",!t||Y.get(t[n],"globalEval"))}ge.tbody=ge.tfoot=ge.colgroup=ge.caption=ge.thead,ge.th=ge.td,y.option||(ge.optgroup=ge.option=[1,"<select multiple='multiple'>","</select>"]);var ye=/<|&#?\w+;/;function be(e,t,n,i,r){for(var o,s,a,l,u,c,f=t.createDocumentFragment(),d=[],h=0,p=e.length;h<p;h++)if((o=e[h])||0===o)if("object"===w(o))C.merge(d,o.nodeType?[o]:o);else if(ye.test(o)){for(s=s||f.appendChild(t.createElement("div")),a=(de.exec(o)||["",""])[1].toLowerCase(),l=ge[a]||ge._default,s.innerHTML=l[1]+C.htmlPrefilter(o)+l[2],c=l[0];c--;)s=s.lastChild;C.merge(d,s.childNodes),(s=f.firstChild).textContent=""}else d.push(t.createTextNode(o));for(f.textContent="",h=0;o=d[h++];)if(i&&-1<C.inArray(o,i))r&&r.push(o);else if(u=re(o),s=me(f.appendChild(o),"script"),u&&ve(s),n)for(c=0;o=s[c++];)he.test(o.type||"")&&n.push(o);return f}var _e=/^key/,we=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,xe=/^([^.]*)(?:\.(.+)|)/;function Ee(){return!0}function Te(){return!1}function Ce(e,t){return e===function(){try{return T.activeElement}catch(e){}}()==("focus"===t)}function Se(e,t,n,i,r,o){var s,a;if("object"==typeof t){for(a in"string"!=typeof n&&(i=i||n,n=void 0),t)Se(e,a,n,i,t[a],o);return e}if(null==i&&null==r?(r=n,i=n=void 0):null==r&&("string"==typeof n?(r=i,i=void 0):(r=i,i=n,n=void 0)),!1===r)r=Te;else if(!r)return e;return 1===o&&(s=r,(r=function(e){return C().off(e),s.apply(this,arguments)}).guid=s.guid||(s.guid=C.guid++)),e.each(function(){C.event.add(this,t,r,i,n)})}function Ae(e,r,o){o?(Y.set(e,r,!1),C.event.add(e,r,{namespace:!1,handler:function(e){var t,n,i=Y.get(this,r);if(1&e.isTrigger&&this[r]){if(i.length)(C.event.special[r]||{}).delegateType&&e.stopPropagation();else if(i=a.call(arguments),Y.set(this,r,i),t=o(this,r),this[r](),i!==(n=Y.get(this,r))||t?Y.set(this,r,!1):n={},i!==n)return e.stopImmediatePropagation(),e.preventDefault(),n.value}else i.length&&(Y.set(this,r,{value:C.event.trigger(C.extend(i[0],C.Event.prototype),i.slice(1),this)}),e.stopImmediatePropagation())}})):void 0===Y.get(e,r)&&C.event.add(e,r,Ee)}C.event={global:{},add:function(t,e,n,i,r){var o,s,a,l,u,c,f,d,h,p,g,m=Y.get(t);if(V(t))for(n.handler&&(n=(o=n).handler,r=o.selector),r&&C.find.matchesSelector(ie,r),n.guid||(n.guid=C.guid++),(l=m.events)||(l=m.events=Object.create(null)),(s=m.handle)||(s=m.handle=function(e){return void 0!==C&&C.event.triggered!==e.type?C.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(q)||[""]).length;u--;)h=g=(a=xe.exec(e[u])||[])[1],p=(a[2]||"").split(".").sort(),h&&(f=C.event.special[h]||{},h=(r?f.delegateType:f.bindType)||h,f=C.event.special[h]||{},c=C.extend({type:h,origType:g,data:i,handler:n,guid:n.guid,selector:r,needsContext:r&&C.expr.match.needsContext.test(r),namespace:p.join(".")},o),(d=l[h])||((d=l[h]=[]).delegateCount=0,f.setup&&!1!==f.setup.call(t,i,p,s)||t.addEventListener&&t.addEventListener(h,s)),f.add&&(f.add.call(t,c),c.handler.guid||(c.handler.guid=n.guid)),r?d.splice(d.delegateCount++,0,c):d.push(c),C.event.global[h]=!0)},remove:function(e,t,n,i,r){var o,s,a,l,u,c,f,d,h,p,g,m=Y.hasData(e)&&Y.get(e);if(m&&(l=m.events)){for(u=(t=(t||"").match(q)||[""]).length;u--;)if(h=g=(a=xe.exec(t[u])||[])[1],p=(a[2]||"").split(".").sort(),h){for(f=C.event.special[h]||{},d=l[h=(i?f.delegateType:f.bindType)||h]||[],a=a[2]&&new RegExp("(^|\\.)"+p.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=d.length;o--;)c=d[o],!r&&g!==c.origType||n&&n.guid!==c.guid||a&&!a.test(c.namespace)||i&&i!==c.selector&&("**"!==i||!c.selector)||(d.splice(o,1),c.selector&&d.delegateCount--,f.remove&&f.remove.call(e,c));s&&!d.length&&(f.teardown&&!1!==f.teardown.call(e,p,m.handle)||C.removeEvent(e,h,m.handle),delete l[h])}else for(h in l)C.event.remove(e,h+t[u],n,i,!0);C.isEmptyObject(l)&&Y.remove(e,"handle events")}},dispatch:function(e){var t,n,i,r,o,s,a=new Array(arguments.length),l=C.event.fix(e),u=(Y.get(this,"events")||Object.create(null))[l.type]||[],c=C.event.special[l.type]||{};for(a[0]=l,t=1;t<arguments.length;t++)a[t]=arguments[t];if(l.delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,l)){for(s=C.event.handlers.call(this,l,u),t=0;(r=s[t++])&&!l.isPropagationStopped();)for(l.currentTarget=r.elem,n=0;(o=r.handlers[n++])&&!l.isImmediatePropagationStopped();)l.rnamespace&&!1!==o.namespace&&!l.rnamespace.test(o.namespace)||(l.handleObj=o,l.data=o.data,void 0!==(i=((C.event.special[o.origType]||{}).handle||o.handler).apply(r.elem,a))&&!1===(l.result=i)&&(l.preventDefault(),l.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,l),l.result}},handlers:function(e,t){var n,i,r,o,s,a=[],l=t.delegateCount,u=e.target;if(l&&u.nodeType&&!("click"===e.type&&1<=e.button))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&("click"!==e.type||!0!==u.disabled)){for(o=[],s={},n=0;n<l;n++)void 0===s[r=(i=t[n]).selector+" "]&&(s[r]=i.needsContext?-1<C(r,this).index(u):C.find(r,this,null,[u]).length),s[r]&&o.push(i);o.length&&a.push({elem:u,handlers:o})}return u=this,l<t.length&&a.push({elem:u,handlers:t.slice(l)}),a},addProp:function(t,e){Object.defineProperty(C.Event.prototype,t,{enumerable:!0,configurable:!0,get:b(e)?function(){if(this.originalEvent)return e(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[t]},set:function(e){Object.defineProperty(this,t,{enumerable:!0,configurable:!0,writable:!0,value:e})}})},fix:function(e){return e[C.expando]?e:new C.Event(e)},special:{load:{noBubble:!0},click:{setup:function(e){var t=this||e;return fe.test(t.type)&&t.click&&A(t,"input")&&Ae(t,"click",Ee),!1},trigger:function(e){var t=this||e;return fe.test(t.type)&&t.click&&A(t,"input")&&Ae(t,"click"),!0},_default:function(e){var t=e.target;return fe.test(t.type)&&t.click&&A(t,"input")&&Y.get(t,"click")||A(t,"a")}},beforeunload:{postDispatch:function(e){void 0!==e.result&&e.originalEvent&&(e.originalEvent.returnValue=e.result)}}}},C.removeEvent=function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n)},C.Event=function(e,t){if(!(this instanceof C.Event))return new C.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||void 0===e.defaultPrevented&&!1===e.returnValue?Ee:Te,this.target=e.target&&3===e.target.nodeType?e.target.parentNode:e.target,this.currentTarget=e.currentTarget,this.relatedTarget=e.relatedTarget):this.type=e,t&&C.extend(this,t),this.timeStamp=e&&e.timeStamp||Date.now(),this[C.expando]=!0},C.Event.prototype={constructor:C.Event,isDefaultPrevented:Te,isPropagationStopped:Te,isImmediatePropagationStopped:Te,isSimulated:!1,preventDefault:function(){var e=this.originalEvent;this.isDefaultPrevented=Ee,e&&!this.isSimulated&&e.preventDefault()},stopPropagation:function(){var e=this.originalEvent;this.isPropagationStopped=Ee,e&&!this.isSimulated&&e.stopPropagation()},stopImmediatePropagation:function(){var e=this.originalEvent;this.isImmediatePropagationStopped=Ee,e&&!this.isSimulated&&e.stopImmediatePropagation(),this.stopPropagation()}},C.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(e){var t=e.button;return null==e.which&&_e.test(e.type)?null!=e.charCode?e.charCode:e.keyCode:!e.which&&void 0!==t&&we.test(e.type)?1&t?1:2&t?3:4&t?2:0:e.which}},C.event.addProp),C.each({focus:"focusin",blur:"focusout"},function(e,t){C.event.special[e]={setup:function(){return Ae(this,e,Ce),!1},trigger:function(){return Ae(this,e),!0},delegateType:t}}),C.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(e,r){C.event.special[e]={delegateType:r,bindType:r,handle:function(e){var t,n=e.relatedTarget,i=e.handleObj;return n&&(n===this||C.contains(this,n))||(e.type=i.origType,t=i.handler.apply(this,arguments),e.type=r),t}}}),C.fn.extend({on:function(e,t,n,i){return Se(this,e,t,n,i)},one:function(e,t,n,i){return Se(this,e,t,n,i,1)},off:function(e,t,n){var i,r;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,C(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if("object"!=typeof e)return!1!==t&&"function"!=typeof t||(n=t,t=void 0),!1===n&&(n=Te),this.each(function(){C.event.remove(this,e,n,t)});for(r in e)this.off(r,t,e[r]);return this}});var Ne=/<script|<style|<link/i,ke=/checked\s*(?:[^=]|=\s*.checked.)/i,De=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function je(e,t){return A(e,"table")&&A(11!==t.nodeType?t:t.firstChild,"tr")&&C(e).children("tbody")[0]||e}function Oe(e){return e.type=(null!==e.getAttribute("type"))+"/"+e.type,e}function Le(e){return"true/"===(e.type||"").slice(0,5)?e.type=e.type.slice(5):e.removeAttribute("type"),e}function Ie(e,t){var n,i,r,o,s,a;if(1===t.nodeType){if(Y.hasData(e)&&(a=Y.get(e).events))for(r in Y.remove(t,"handle events"),a)for(n=0,i=a[r].length;n<i;n++)C.event.add(t,r,a[r][n]);K.hasData(e)&&(o=K.access(e),s=C.extend({},o),K.set(t,s))}}function qe(n,i,r,o){i=m(i);var e,t,s,a,l,u,c=0,f=n.length,d=f-1,h=i[0],p=b(h);if(p||1<f&&"string"==typeof h&&!y.checkClone&&ke.test(h))return n.each(function(e){var t=n.eq(e);p&&(i[0]=h.call(this,e,t.html())),qe(t,i,r,o)});if(f&&(t=(e=be(i,n[0].ownerDocument,!1,n,o)).firstChild,1===e.childNodes.length&&(e=t),t||o)){for(a=(s=C.map(me(e,"script"),Oe)).length;c<f;c++)l=e,c!==d&&(l=C.clone(l,!0,!0),a&&C.merge(s,me(l,"script"))),r.call(n[c],l,c);if(a)for(u=s[s.length-1].ownerDocument,C.map(s,Le),c=0;c<a;c++)l=s[c],he.test(l.type||"")&&!Y.access(l,"globalEval")&&C.contains(u,l)&&(l.src&&"module"!==(l.type||"").toLowerCase()?C._evalUrl&&!l.noModule&&C._evalUrl(l.src,{nonce:l.nonce||l.getAttribute("nonce")},u):_(l.textContent.replace(De,""),l,u))}return n}function Pe(e,t,n){for(var i,r=t?C.filter(t,e):e,o=0;null!=(i=r[o]);o++)n||1!==i.nodeType||C.cleanData(me(i)),i.parentNode&&(n&&re(i)&&ve(me(i,"script")),i.parentNode.removeChild(i));return e}C.extend({htmlPrefilter:function(e){return e},clone:function(e,t,n){var i,r,o,s,a,l,u,c=e.cloneNode(!0),f=re(e);if(!(y.noCloneChecked||1!==e.nodeType&&11!==e.nodeType||C.isXMLDoc(e)))for(s=me(c),i=0,r=(o=me(e)).length;i<r;i++)a=o[i],"input"===(u=(l=s[i]).nodeName.toLowerCase())&&fe.test(a.type)?l.checked=a.checked:"input"!==u&&"textarea"!==u||(l.defaultValue=a.defaultValue);if(t)if(n)for(o=o||me(e),s=s||me(c),i=0,r=o.length;i<r;i++)Ie(o[i],s[i]);else Ie(e,c);return 0<(s=me(c,"script")).length&&ve(s,!f&&me(e,"script")),c},cleanData:function(e){for(var t,n,i,r=C.event.special,o=0;void 0!==(n=e[o]);o++)if(V(n)){if(t=n[Y.expando]){if(t.events)for(i in t.events)r[i]?C.event.remove(n,i):C.removeEvent(n,i,t.handle);n[Y.expando]=void 0}n[K.expando]&&(n[K.expando]=void 0)}}}),C.fn.extend({detach:function(e){return Pe(this,e,!0)},remove:function(e){return Pe(this,e)},text:function(e){return Q(this,function(e){return void 0===e?C.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=e)})},null,e,arguments.length)},append:function(){return qe(this,arguments,function(e){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||je(this,e).appendChild(e)})},prepend:function(){return qe(this,arguments,function(e){var t;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(t=je(this,e)).insertBefore(e,t.firstChild)})},before:function(){return qe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this)})},after:function(){return qe(this,arguments,function(e){this.parentNode&&this.parentNode.insertBefore(e,this.nextSibling)})},empty:function(){for(var e,t=0;null!=(e=this[t]);t++)1===e.nodeType&&(C.cleanData(me(e,!1)),e.textContent="");return this},clone:function(e,t){return e=null!=e&&e,t=null==t?e:t,this.map(function(){return C.clone(this,e,t)})},html:function(e){return Q(this,function(e){var t=this[0]||{},n=0,i=this.length;if(void 0===e&&1===t.nodeType)return t.innerHTML;if("string"==typeof e&&!Ne.test(e)&&!ge[(de.exec(e)||["",""])[1].toLowerCase()]){e=C.htmlPrefilter(e);try{for(;n<i;n++)1===(t=this[n]||{}).nodeType&&(C.cleanData(me(t,!1)),t.innerHTML=e);t=0}catch(e){}}t&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(){var n=[];return qe(this,arguments,function(e){var t=this.parentNode;C.inArray(this,n)<0&&(C.cleanData(me(this)),t&&t.replaceChild(e,this))},n)}}),C.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,s){C.fn[e]=function(e){for(var t,n=[],i=C(e),r=i.length-1,o=0;o<=r;o++)t=o===r?this:this.clone(!0),C(i[o])[s](t),l.apply(n,t.get());return this.pushStack(n)}});function He(e,t,n){var i,r,o={};for(r in t)o[r]=e.style[r],e.style[r]=t[r];for(r in i=n.call(e),t)e.style[r]=o[r];return i}var Re,Me,Fe,Be,Qe,We,Ue,$e,ze=new RegExp("^("+ee+")(?!px)[a-z%]+$","i"),Ve=function(e){var t=e.ownerDocument.defaultView;return t&&t.opener||(t=E),t.getComputedStyle(e)},Xe=new RegExp(ne.join("|"),"i");function Ye(e,t,n){var i,r,o,s,a=e.style;return(n=n||Ve(e))&&(""!==(s=n.getPropertyValue(t)||n[t])||re(e)||(s=C.style(e,t)),!y.pixelBoxStyles()&&ze.test(s)&&Xe.test(t)&&(i=a.width,r=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=s,s=n.width,a.width=i,a.minWidth=r,a.maxWidth=o)),void 0!==s?s+"":s}function Ke(e,t){return{get:function(){if(!e())return(this.get=t).apply(this,arguments);delete this.get}}}function Ge(){var e;$e&&(Ue.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0",$e.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%",ie.appendChild(Ue).appendChild($e),e=E.getComputedStyle($e),Re="1%"!==e.top,We=12===Je(e.marginLeft),$e.style.right="60%",Be=36===Je(e.right),Me=36===Je(e.width),$e.style.position="absolute",Fe=12===Je($e.offsetWidth/3),ie.removeChild(Ue),$e=null)}function Je(e){return Math.round(parseFloat(e))}Ue=T.createElement("div"),($e=T.createElement("div")).style&&($e.style.backgroundClip="content-box",$e.cloneNode(!0).style.backgroundClip="",y.clearCloneStyle="content-box"===$e.style.backgroundClip,C.extend(y,{boxSizingReliable:function(){return Ge(),Me},pixelBoxStyles:function(){return Ge(),Be},pixelPosition:function(){return Ge(),Re},reliableMarginLeft:function(){return Ge(),We},scrollboxSize:function(){return Ge(),Fe},reliableTrDimensions:function(){var e,t,n,i;return null==Qe&&(e=T.createElement("table"),t=T.createElement("tr"),n=T.createElement("div"),e.style.cssText="position:absolute;left:-11111px",t.style.height="1px",n.style.height="9px",ie.appendChild(e).appendChild(t).appendChild(n),i=E.getComputedStyle(t),Qe=3<parseInt(i.height),ie.removeChild(e)),Qe}}));var Ze=["Webkit","Moz","ms"],et=T.createElement("div").style,tt={};function nt(e){return C.cssProps[e]||tt[e]||(e in et?e:tt[e]=function(e){for(var t=e[0].toUpperCase()+e.slice(1),n=Ze.length;n--;)if((e=Ze[n]+t)in et)return e}(e)||e)}var it=/^(none|table(?!-c[ea]).+)/,rt=/^--/,ot={position:"absolute",visibility:"hidden",display:"block"},st={letterSpacing:"0",fontWeight:"400"};function at(e,t,n){var i=te.exec(t);return i?Math.max(0,i[2]-(n||0))+(i[3]||"px"):t}function lt(e,t,n,i,r,o){var s="width"===t?1:0,a=0,l=0;if(n===(i?"border":"content"))return 0;for(;s<4;s+=2)"margin"===n&&(l+=C.css(e,n+ne[s],!0,r)),i?("content"===n&&(l-=C.css(e,"padding"+ne[s],!0,r)),"margin"!==n&&(l-=C.css(e,"border"+ne[s]+"Width",!0,r))):(l+=C.css(e,"padding"+ne[s],!0,r),"padding"!==n?l+=C.css(e,"border"+ne[s]+"Width",!0,r):a+=C.css(e,"border"+ne[s]+"Width",!0,r));return!i&&0<=o&&(l+=Math.max(0,Math.ceil(e["offset"+t[0].toUpperCase()+t.slice(1)]-o-l-a-.5))||0),l}function ut(e,t,n){var i=Ve(e),r=(!y.boxSizingReliable()||n)&&"border-box"===C.css(e,"boxSizing",!1,i),o=r,s=Ye(e,t,i),a="offset"+t[0].toUpperCase()+t.slice(1);if(ze.test(s)){if(!n)return s;s="auto"}return(!y.boxSizingReliable()&&r||!y.reliableTrDimensions()&&A(e,"tr")||"auto"===s||!parseFloat(s)&&"inline"===C.css(e,"display",!1,i))&&e.getClientRects().length&&(r="border-box"===C.css(e,"boxSizing",!1,i),(o=a in e)&&(s=e[a])),(s=parseFloat(s)||0)+lt(e,t,n||(r?"border":"content"),o,i,s)+"px"}function ct(e,t,n,i,r){return new ct.prototype.init(e,t,n,i,r)}C.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Ye(e,"opacity");return""===n?"1":n}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(e,t,n,i){if(e&&3!==e.nodeType&&8!==e.nodeType&&e.style){var r,o,s,a=z(t),l=rt.test(t),u=e.style;if(l||(t=nt(a)),s=C.cssHooks[t]||C.cssHooks[a],void 0===n)return s&&"get"in s&&void 0!==(r=s.get(e,!1,i))?r:u[t];"string"==(o=typeof n)&&(r=te.exec(n))&&r[1]&&(n=ae(e,t,r),o="number"),null!=n&&n==n&&("number"!==o||l||(n+=r&&r[3]||(C.cssNumber[a]?"":"px")),y.clearCloneStyle||""!==n||0!==t.indexOf("background")||(u[t]="inherit"),s&&"set"in s&&void 0===(n=s.set(e,n,i))||(l?u.setProperty(t,n):u[t]=n))}},css:function(e,t,n,i){var r,o,s,a=z(t);return rt.test(t)||(t=nt(a)),(s=C.cssHooks[t]||C.cssHooks[a])&&"get"in s&&(r=s.get(e,!0,n)),void 0===r&&(r=Ye(e,t,i)),"normal"===r&&t in st&&(r=st[t]),""===n||n?(o=parseFloat(r),!0===n||isFinite(o)?o||0:r):r}}),C.each(["height","width"],function(e,l){C.cssHooks[l]={get:function(e,t,n){if(t)return!it.test(C.css(e,"display"))||e.getClientRects().length&&e.getBoundingClientRect().width?ut(e,l,n):He(e,ot,function(){return ut(e,l,n)})},set:function(e,t,n){var i,r=Ve(e),o=!y.scrollboxSize()&&"absolute"===r.position,s=(o||n)&&"border-box"===C.css(e,"boxSizing",!1,r),a=n?lt(e,l,n,s,r):0;return s&&o&&(a-=Math.ceil(e["offset"+l[0].toUpperCase()+l.slice(1)]-parseFloat(r[l])-lt(e,l,"border",!1,r)-.5)),a&&(i=te.exec(t))&&"px"!==(i[3]||"px")&&(e.style[l]=t,t=C.css(e,l)),at(0,t,a)}}}),C.cssHooks.marginLeft=Ke(y.reliableMarginLeft,function(e,t){if(t)return(parseFloat(Ye(e,"marginLeft"))||e.getBoundingClientRect().left-He(e,{marginLeft:0},function(){return e.getBoundingClientRect().left}))+"px"}),C.each({margin:"",padding:"",border:"Width"},function(r,o){C.cssHooks[r+o]={expand:function(e){for(var t=0,n={},i="string"==typeof e?e.split(" "):[e];t<4;t++)n[r+ne[t]+o]=i[t]||i[t-2]||i[0];return n}},"margin"!==r&&(C.cssHooks[r+o].set=at)}),C.fn.extend({css:function(e,t){return Q(this,function(e,t,n){var i,r,o={},s=0;if(Array.isArray(t)){for(i=Ve(e),r=t.length;s<r;s++)o[t[s]]=C.css(e,t[s],!1,i);return o}return void 0!==n?C.style(e,t,n):C.css(e,t)},e,t,1<arguments.length)}}),((C.Tween=ct).prototype={constructor:ct,init:function(e,t,n,i,r,o){this.elem=e,this.prop=n,this.easing=r||C.easing._default,this.options=t,this.start=this.now=this.cur(),this.end=i,this.unit=o||(C.cssNumber[n]?"":"px")},cur:function(){var e=ct.propHooks[this.prop];return e&&e.get?e.get(this):ct.propHooks._default.get(this)},run:function(e){var t,n=ct.propHooks[this.prop];return this.options.duration?this.pos=t=C.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):ct.propHooks._default.set(this),this}}).init.prototype=ct.prototype,(ct.propHooks={_default:{get:function(e){var t;return 1!==e.elem.nodeType||null!=e.elem[e.prop]&&null==e.elem.style[e.prop]?e.elem[e.prop]:(t=C.css(e.elem,e.prop,""))&&"auto"!==t?t:0},set:function(e){C.fx.step[e.prop]?C.fx.step[e.prop](e):1!==e.elem.nodeType||!C.cssHooks[e.prop]&&null==e.elem.style[nt(e.prop)]?e.elem[e.prop]=e.now:C.style(e.elem,e.prop,e.now+e.unit)}}}).scrollTop=ct.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},C.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2},_default:"swing"},C.fx=ct.prototype.init,C.fx.step={};var ft,dt,ht,pt,gt=/^(?:toggle|show|hide)$/,mt=/queueHooks$/;function vt(){dt&&(!1===T.hidden&&E.requestAnimationFrame?E.requestAnimationFrame(vt):E.setTimeout(vt,C.fx.interval),C.fx.tick())}function yt(){return E.setTimeout(function(){ft=void 0}),ft=Date.now()}function bt(e,t){var n,i=0,r={height:e};for(t=t?1:0;i<4;i+=2-t)r["margin"+(n=ne[i])]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function _t(e,t,n){for(var i,r=(wt.tweeners[t]||[]).concat(wt.tweeners["*"]),o=0,s=r.length;o<s;o++)if(i=r[o].call(n,t,e))return i}function wt(o,e,t){var n,s,i=0,r=wt.prefilters.length,a=C.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var e=ft||yt(),t=Math.max(0,u.startTime+u.duration-e),n=1-(t/u.duration||0),i=0,r=u.tweens.length;i<r;i++)u.tweens[i].run(n);return a.notifyWith(o,[u,n,t]),n<1&&r?t:(r||a.notifyWith(o,[u,1,0]),a.resolveWith(o,[u]),!1)},u=a.promise({elem:o,props:C.extend({},e),opts:C.extend(!0,{specialEasing:{},easing:C.easing._default},t),originalProperties:e,originalOptions:t,startTime:ft||yt(),duration:t.duration,tweens:[],createTween:function(e,t){var n=C.Tween(o,u.opts,e,t,u.opts.specialEasing[e]||u.opts.easing);return u.tweens.push(n),n},stop:function(e){var t=0,n=e?u.tweens.length:0;if(s)return this;for(s=!0;t<n;t++)u.tweens[t].run(1);return e?(a.notifyWith(o,[u,1,0]),a.resolveWith(o,[u,e])):a.rejectWith(o,[u,e]),this}}),c=u.props;for(function(e,t){var n,i,r,o,s;for(n in e)if(r=t[i=z(n)],o=e[n],Array.isArray(o)&&(r=o[1],o=e[n]=o[0]),n!==i&&(e[i]=o,delete e[n]),(s=C.cssHooks[i])&&"expand"in s)for(n in o=s.expand(o),delete e[i],o)n in e||(e[n]=o[n],t[n]=r);else t[i]=r}(c,u.opts.specialEasing);i<r;i++)if(n=wt.prefilters[i].call(u,o,c,u.opts))return b(n.stop)&&(C._queueHooks(u.elem,u.opts.queue).stop=n.stop.bind(n)),n;return C.map(c,_t,u),b(u.opts.start)&&u.opts.start.call(o,u),u.progress(u.opts.progress).done(u.opts.done,u.opts.complete).fail(u.opts.fail).always(u.opts.always),C.fx.timer(C.extend(l,{elem:o,anim:u,queue:u.opts.queue})),u}C.Animation=C.extend(wt,{tweeners:{"*":[function(e,t){var n=this.createTween(e,t);return ae(n.elem,e,te.exec(t),n),n}]},tweener:function(e,t){for(var n,i=0,r=(e=b(e)?(t=e,["*"]):e.match(q)).length;i<r;i++)n=e[i],wt.tweeners[n]=wt.tweeners[n]||[],wt.tweeners[n].unshift(t)},prefilters:[function(e,t,n){var i,r,o,s,a,l,u,c,f="width"in t||"height"in t,d=this,h={},p=e.style,g=e.nodeType&&se(e),m=Y.get(e,"fxshow");for(i in n.queue||(null==(s=C._queueHooks(e,"fx")).unqueued&&(s.unqueued=0,a=s.empty.fire,s.empty.fire=function(){s.unqueued||a()}),s.unqueued++,d.always(function(){d.always(function(){s.unqueued--,C.queue(e,"fx").length||s.empty.fire()})})),t)if(r=t[i],gt.test(r)){if(delete t[i],o=o||"toggle"===r,r===(g?"hide":"show")){if("show"!==r||!m||void 0===m[i])continue;g=!0}h[i]=m&&m[i]||C.style(e,i)}if((l=!C.isEmptyObject(t))||!C.isEmptyObject(h))for(i in f&&1===e.nodeType&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],null==(u=m&&m.display)&&(u=Y.get(e,"display")),"none"===(c=C.css(e,"display"))&&(u?c=u:(ue([e],!0),u=e.style.display||u,c=C.css(e,"display"),ue([e]))),("inline"===c||"inline-block"===c&&null!=u)&&"none"===C.css(e,"float")&&(l||(d.done(function(){p.display=u}),null==u&&(c=p.display,u="none"===c?"":c)),p.display="inline-block")),n.overflow&&(p.overflow="hidden",d.always(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]})),l=!1,h)l||(m?"hidden"in m&&(g=m.hidden):m=Y.access(e,"fxshow",{display:u}),o&&(m.hidden=!g),g&&ue([e],!0),d.done(function(){for(i in g||ue([e]),Y.remove(e,"fxshow"),h)C.style(e,i,h[i])})),l=_t(g?m[i]:0,i,d),i in m||(m[i]=l.start,g&&(l.end=l.start,l.start=0))}],prefilter:function(e,t){t?wt.prefilters.unshift(e):wt.prefilters.push(e)}}),C.speed=function(e,t,n){var i=e&&"object"==typeof e?C.extend({},e):{complete:n||!n&&t||b(e)&&e,duration:e,easing:n&&t||t&&!b(t)&&t};return C.fx.off?i.duration=0:"number"!=typeof i.duration&&(i.duration in C.fx.speeds?i.duration=C.fx.speeds[i.duration]:i.duration=C.fx.speeds._default),null!=i.queue&&!0!==i.queue||(i.queue="fx"),i.old=i.complete,i.complete=function(){b(i.old)&&i.old.call(this),i.queue&&C.dequeue(this,i.queue)},i},C.fn.extend({fadeTo:function(e,t,n,i){return this.filter(se).css("opacity",0).show().end().animate({opacity:t},e,n,i)},animate:function(t,e,n,i){function r(){var e=wt(this,C.extend({},t),s);(o||Y.get(this,"finish"))&&e.stop(!0)}var o=C.isEmptyObject(t),s=C.speed(e,n,i);return r.finish=r,o||!1===s.queue?this.each(r):this.queue(s.queue,r)},stop:function(r,e,o){function s(e){var t=e.stop;delete e.stop,t(o)}return"string"!=typeof r&&(o=e,e=r,r=void 0),e&&this.queue(r||"fx",[]),this.each(function(){var e=!0,t=null!=r&&r+"queueHooks",n=C.timers,i=Y.get(this);if(t)i[t]&&i[t].stop&&s(i[t]);else for(t in i)i[t]&&i[t].stop&&mt.test(t)&&s(i[t]);for(t=n.length;t--;)n[t].elem!==this||null!=r&&n[t].queue!==r||(n[t].anim.stop(o),e=!1,n.splice(t,1));!e&&o||C.dequeue(this,r)})},finish:function(s){return!1!==s&&(s=s||"fx"),this.each(function(){var e,t=Y.get(this),n=t[s+"queue"],i=t[s+"queueHooks"],r=C.timers,o=n?n.length:0;for(t.finish=!0,C.queue(this,s,[]),i&&i.stop&&i.stop.call(this,!0),e=r.length;e--;)r[e].elem===this&&r[e].queue===s&&(r[e].anim.stop(!0),r.splice(e,1));for(e=0;e<o;e++)n[e]&&n[e].finish&&n[e].finish.call(this);delete t.finish})}}),C.each(["toggle","show","hide"],function(e,i){var r=C.fn[i];C.fn[i]=function(e,t,n){return null==e||"boolean"==typeof e?r.apply(this,arguments):this.animate(bt(i,!0),e,t,n)}}),C.each({slideDown:bt("show"),slideUp:bt("hide"),slideToggle:bt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,i){C.fn[e]=function(e,t,n){return this.animate(i,e,t,n)}}),C.timers=[],C.fx.tick=function(){var e,t=0,n=C.timers;for(ft=Date.now();t<n.length;t++)(e=n[t])()||n[t]!==e||n.splice(t--,1);n.length||C.fx.stop(),ft=void 0},C.fx.timer=function(e){C.timers.push(e),C.fx.start()},C.fx.interval=13,C.fx.start=function(){dt||(dt=!0,vt())},C.fx.stop=function(){dt=null},C.fx.speeds={slow:600,fast:200,_default:400},C.fn.delay=function(i,e){return i=C.fx&&C.fx.speeds[i]||i,e=e||"fx",this.queue(e,function(e,t){var n=E.setTimeout(e,i);t.stop=function(){E.clearTimeout(n)}})},ht=T.createElement("input"),pt=T.createElement("select").appendChild(T.createElement("option")),ht.type="checkbox",y.checkOn=""!==ht.value,y.optSelected=pt.selected,(ht=T.createElement("input")).value="t",ht.type="radio",y.radioValue="t"===ht.value;var xt,Et=C.expr.attrHandle;C.fn.extend({attr:function(e,t){return Q(this,C.attr,e,t,1<arguments.length)},removeAttr:function(e){return this.each(function(){C.removeAttr(this,e)})}}),C.extend({attr:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===e.getAttribute?C.prop(e,t,n):(1===o&&C.isXMLDoc(e)||(r=C.attrHooks[t.toLowerCase()]||(C.expr.match.bool.test(t)?xt:void 0)),void 0!==n?null===n?void C.removeAttr(e,t):r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:(e.setAttribute(t,n+""),n):!(r&&"get"in r&&null!==(i=r.get(e,t)))&&null==(i=C.find.attr(e,t))?void 0:i)},attrHooks:{type:{set:function(e,t){if(!y.radioValue&&"radio"===t&&A(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}}},removeAttr:function(e,t){var n,i=0,r=t&&t.match(q);if(r&&1===e.nodeType)for(;n=r[i++];)e.removeAttribute(n)}}),xt={set:function(e,t,n){return!1===t?C.removeAttr(e,n):e.setAttribute(n,n),n}},C.each(C.expr.match.bool.source.match(/\w+/g),function(e,t){var s=Et[t]||C.find.attr;Et[t]=function(e,t,n){var i,r,o=t.toLowerCase();return n||(r=Et[o],Et[o]=i,i=null!=s(e,t,n)?o:null,Et[o]=r),i}});var Tt=/^(?:input|select|textarea|button)$/i,Ct=/^(?:a|area)$/i;function St(e){return(e.match(q)||[]).join(" ")}function At(e){return e.getAttribute&&e.getAttribute("class")||""}function Nt(e){return Array.isArray(e)?e:"string"==typeof e&&e.match(q)||[]}C.fn.extend({prop:function(e,t){return Q(this,C.prop,e,t,1<arguments.length)},removeProp:function(e){return this.each(function(){delete this[C.propFix[e]||e]})}}),C.extend({prop:function(e,t,n){var i,r,o=e.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&C.isXMLDoc(e)||(t=C.propFix[t]||t,r=C.propHooks[t]),void 0!==n?r&&"set"in r&&void 0!==(i=r.set(e,n,t))?i:e[t]=n:r&&"get"in r&&null!==(i=r.get(e,t))?i:e[t]},propHooks:{tabIndex:{get:function(e){var t=C.find.attr(e,"tabindex");return t?parseInt(t,10):Tt.test(e.nodeName)||Ct.test(e.nodeName)&&e.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),y.optSelected||(C.propHooks.selected={get:function(e){var t=e.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(e){var t=e.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}}),C.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){C.propFix[this.toLowerCase()]=this}),C.fn.extend({addClass:function(t){var e,n,i,r,o,s,a,l=0;if(b(t))return this.each(function(e){C(this).addClass(t.call(this,e,At(this)))});if((e=Nt(t)).length)for(;n=this[l++];)if(r=At(n),i=1===n.nodeType&&" "+St(r)+" "){for(s=0;o=e[s++];)i.indexOf(" "+o+" ")<0&&(i+=o+" ");r!==(a=St(i))&&n.setAttribute("class",a)}return this},removeClass:function(t){var e,n,i,r,o,s,a,l=0;if(b(t))return this.each(function(e){C(this).removeClass(t.call(this,e,At(this)))});if(!arguments.length)return this.attr("class","");if((e=Nt(t)).length)for(;n=this[l++];)if(r=At(n),i=1===n.nodeType&&" "+St(r)+" "){for(s=0;o=e[s++];)for(;-1<i.indexOf(" "+o+" ");)i=i.replace(" "+o+" "," ");r!==(a=St(i))&&n.setAttribute("class",a)}return this},toggleClass:function(r,t){var o=typeof r,s="string"==o||Array.isArray(r);return"boolean"==typeof t&&s?t?this.addClass(r):this.removeClass(r):b(r)?this.each(function(e){C(this).toggleClass(r.call(this,e,At(this),t),t)}):this.each(function(){var e,t,n,i;if(s)for(t=0,n=C(this),i=Nt(r);e=i[t++];)n.hasClass(e)?n.removeClass(e):n.addClass(e);else void 0!==r&&"boolean"!=o||((e=At(this))&&Y.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==r&&Y.get(this,"__className__")||""))})},hasClass:function(e){for(var t,n=0,i=" "+e+" ";t=this[n++];)if(1===t.nodeType&&-1<(" "+St(At(t))+" ").indexOf(i))return!0;return!1}});var kt=/\r/g;C.fn.extend({val:function(n){var i,e,r,t=this[0];return arguments.length?(r=b(n),this.each(function(e){var t;1===this.nodeType&&(null==(t=r?n.call(this,e,C(this).val()):n)?t="":"number"==typeof t?t+="":Array.isArray(t)&&(t=C.map(t,function(e){return null==e?"":e+""})),(i=C.valHooks[this.type]||C.valHooks[this.nodeName.toLowerCase()])&&"set"in i&&void 0!==i.set(this,t,"value")||(this.value=t))})):t?(i=C.valHooks[t.type]||C.valHooks[t.nodeName.toLowerCase()])&&"get"in i&&void 0!==(e=i.get(t,"value"))?e:"string"==typeof(e=t.value)?e.replace(kt,""):null==e?"":e:void 0}}),C.extend({valHooks:{option:{get:function(e){var t=C.find.attr(e,"value");return null!=t?t:St(C.text(e))}},select:{get:function(e){for(var t,n,i=e.options,r=e.selectedIndex,o="select-one"===e.type,s=o?null:[],a=o?r+1:i.length,l=r<0?a:o?r:0;l<a;l++)if(((n=i[l]).selected||l===r)&&!n.disabled&&(!n.parentNode.disabled||!A(n.parentNode,"optgroup"))){if(t=C(n).val(),o)return t;s.push(t)}return s},set:function(e,t){for(var n,i,r=e.options,o=C.makeArray(t),s=r.length;s--;)((i=r[s]).selected=-1<C.inArray(C.valHooks.option.get(i),o))&&(n=!0);return n||(e.selectedIndex=-1),o}}}}),C.each(["radio","checkbox"],function(){C.valHooks[this]={set:function(e,t){if(Array.isArray(t))return e.checked=-1<C.inArray(C(e).val(),t)}},y.checkOn||(C.valHooks[this].get=function(e){return null===e.getAttribute("value")?"on":e.value})}),y.focusin="onfocusin"in E;function Dt(e){e.stopPropagation()}var jt=/^(?:focusinfocus|focusoutblur)$/;C.extend(C.event,{trigger:function(e,t,n,i){var r,o,s,a,l,u,c,f=[n||T],d=v.call(e,"type")?e.type:e,h=v.call(e,"namespace")?e.namespace.split("."):[],p=c=o=n=n||T;if(3!==n.nodeType&&8!==n.nodeType&&!jt.test(d+C.event.triggered)&&(-1<d.indexOf(".")&&(d=(h=d.split(".")).shift(),h.sort()),a=d.indexOf(":")<0&&"on"+d,(e=e[C.expando]?e:new C.Event(d,"object"==typeof e&&e)).isTrigger=i?2:3,e.namespace=h.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+h.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=n),t=null==t?[e]:C.makeArray(t,[e]),u=C.event.special[d]||{},i||!u.trigger||!1!==u.trigger.apply(n,t))){if(!i&&!u.noBubble&&!g(n)){for(s=u.delegateType||d,jt.test(s+d)||(p=p.parentNode);p;p=p.parentNode)f.push(p),o=p;o===(n.ownerDocument||T)&&f.push(o.defaultView||o.parentWindow||E)}for(r=0;(p=f[r++])&&!e.isPropagationStopped();)c=p,e.type=1<r?s:u.bindType||d,(l=(Y.get(p,"events")||Object.create(null))[e.type]&&Y.get(p,"handle"))&&l.apply(p,t),(l=a&&p[a])&&l.apply&&V(p)&&(e.result=l.apply(p,t),!1===e.result&&e.preventDefault());return e.type=d,i||e.isDefaultPrevented()||u._default&&!1!==u._default.apply(f.pop(),t)||!V(n)||a&&b(n[d])&&!g(n)&&((o=n[a])&&(n[a]=null),C.event.triggered=d,e.isPropagationStopped()&&c.addEventListener(d,Dt),n[d](),e.isPropagationStopped()&&c.removeEventListener(d,Dt),C.event.triggered=void 0,o&&(n[a]=o)),e.result}},simulate:function(e,t,n){var i=C.extend(new C.Event,n,{type:e,isSimulated:!0});C.event.trigger(i,null,t)}}),C.fn.extend({trigger:function(e,t){return this.each(function(){C.event.trigger(e,t,this)})},triggerHandler:function(e,t){var n=this[0];if(n)return C.event.trigger(e,t,n,!0)}}),y.focusin||C.each({focus:"focusin",blur:"focusout"},function(n,i){function r(e){C.event.simulate(i,e.target,C.event.fix(e))}C.event.special[i]={setup:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,i);t||e.addEventListener(n,r,!0),Y.access(e,i,(t||0)+1)},teardown:function(){var e=this.ownerDocument||this.document||this,t=Y.access(e,i)-1;t?Y.access(e,i,t):(e.removeEventListener(n,r,!0),Y.remove(e,i))}}});var Ot=E.location,Lt={guid:Date.now()},It=/\?/;C.parseXML=function(e){var t;if(!e||"string"!=typeof e)return null;try{t=(new E.DOMParser).parseFromString(e,"text/xml")}catch(e){t=void 0}return t&&!t.getElementsByTagName("parsererror").length||C.error("Invalid XML: "+e),t};var qt=/\[\]$/,Pt=/\r?\n/g,Ht=/^(?:submit|button|image|reset|file)$/i,Rt=/^(?:input|select|textarea|keygen)/i;C.param=function(e,t){function n(e,t){var n=b(t)?t():t;r[r.length]=encodeURIComponent(e)+"="+encodeURIComponent(null==n?"":n)}var i,r=[];if(null==e)return"";if(Array.isArray(e)||e.jquery&&!C.isPlainObject(e))C.each(e,function(){n(this.name,this.value)});else for(i in e)!function n(i,e,r,o){if(Array.isArray(e))C.each(e,function(e,t){r||qt.test(i)?o(i,t):n(i+"["+("object"==typeof t&&null!=t?e:"")+"]",t,r,o)});else if(r||"object"!==w(e))o(i,e);else for(var t in e)n(i+"["+t+"]",e[t],r,o)}(i,e[i],t,n);return r.join("&")},C.fn.extend({serialize:function(){return C.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var e=C.prop(this,"elements");return e?C.makeArray(e):this}).filter(function(){var e=this.type;return this.name&&!C(this).is(":disabled")&&Rt.test(this.nodeName)&&!Ht.test(e)&&(this.checked||!fe.test(e))}).map(function(e,t){var n=C(this).val();return null==n?null:Array.isArray(n)?C.map(n,function(e){return{name:t.name,value:e.replace(Pt,"\r\n")}}):{name:t.name,value:n.replace(Pt,"\r\n")}}).get()}});var Mt=/%20/g,Ft=/#.*$/,Bt=/([?&])_=[^&]*/,Qt=/^(.*?):[ \t]*([^\r\n]*)$/gm,Wt=/^(?:GET|HEAD)$/,Ut=/^\/\//,$t={},zt={},Vt="*/".concat("*"),Xt=T.createElement("a");function Yt(o){return function(e,t){"string"!=typeof e&&(t=e,e="*");var n,i=0,r=e.toLowerCase().match(q)||[];if(b(t))for(;n=r[i++];)"+"===n[0]?(n=n.slice(1)||"*",(o[n]=o[n]||[]).unshift(t)):(o[n]=o[n]||[]).push(t)}}function Kt(t,r,o,s){var a={},l=t===zt;function u(e){var i;return a[e]=!0,C.each(t[e]||[],function(e,t){var n=t(r,o,s);return"string"!=typeof n||l||a[n]?l?!(i=n):void 0:(r.dataTypes.unshift(n),u(n),!1)}),i}return u(r.dataTypes[0])||!a["*"]&&u("*")}function Gt(e,t){var n,i,r=C.ajaxSettings.flatOptions||{};for(n in t)void 0!==t[n]&&((r[n]?e:i=i||{})[n]=t[n]);return i&&C.extend(!0,e,i),e}Xt.href=Ot.href,C.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:Ot.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(Ot.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":Vt,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":C.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(e,t){return t?Gt(Gt(e,C.ajaxSettings),t):Gt(C.ajaxSettings,e)},ajaxPrefilter:Yt($t),ajaxTransport:Yt(zt),ajax:function(e,t){"object"==typeof e&&(t=e,e=void 0),t=t||{};var c,f,d,n,h,i,p,g,r,o,m=C.ajaxSetup({},t),v=m.context||m,y=m.context&&(v.nodeType||v.jquery)?C(v):C.event,b=C.Deferred(),_=C.Callbacks("once memory"),w=m.statusCode||{},s={},a={},l="canceled",x={readyState:0,getResponseHeader:function(e){var t;if(p){if(!n)for(n={};t=Qt.exec(d);)n[t[1].toLowerCase()+" "]=(n[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=n[e.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return p?d:null},setRequestHeader:function(e,t){return null==p&&(e=a[e.toLowerCase()]=a[e.toLowerCase()]||e,s[e]=t),this},overrideMimeType:function(e){return null==p&&(m.mimeType=e),this},statusCode:function(e){if(e)if(p)x.always(e[x.status]);else for(var t in e)w[t]=[w[t],e[t]];return this},abort:function(e){var t=e||l;return c&&c.abort(t),u(0,t),this}};if(b.promise(x),m.url=((e||m.url||Ot.href)+"").replace(Ut,Ot.protocol+"//"),m.type=t.method||t.type||m.method||m.type,m.dataTypes=(m.dataType||"*").toLowerCase().match(q)||[""],null==m.crossDomain){i=T.createElement("a");try{i.href=m.url,i.href=i.href,m.crossDomain=Xt.protocol+"//"+Xt.host!=i.protocol+"//"+i.host}catch(e){m.crossDomain=!0}}if(m.data&&m.processData&&"string"!=typeof m.data&&(m.data=C.param(m.data,m.traditional)),Kt($t,m,t,x),p)return x;for(r in(g=C.event&&m.global)&&0==C.active++&&C.event.trigger("ajaxStart"),m.type=m.type.toUpperCase(),m.hasContent=!Wt.test(m.type),f=m.url.replace(Ft,""),m.hasContent?m.data&&m.processData&&0===(m.contentType||"").indexOf("application/x-www-form-urlencoded")&&(m.data=m.data.replace(Mt,"+")):(o=m.url.slice(f.length),m.data&&(m.processData||"string"==typeof m.data)&&(f+=(It.test(f)?"&":"?")+m.data,delete m.data),!1===m.cache&&(f=f.replace(Bt,"$1"),o=(It.test(f)?"&":"?")+"_="+Lt.guid+++o),m.url=f+o),m.ifModified&&(C.lastModified[f]&&x.setRequestHeader("If-Modified-Since",C.lastModified[f]),C.etag[f]&&x.setRequestHeader("If-None-Match",C.etag[f])),(m.data&&m.hasContent&&!1!==m.contentType||t.contentType)&&x.setRequestHeader("Content-Type",m.contentType),x.setRequestHeader("Accept",m.dataTypes[0]&&m.accepts[m.dataTypes[0]]?m.accepts[m.dataTypes[0]]+("*"!==m.dataTypes[0]?", "+Vt+"; q=0.01":""):m.accepts["*"]),m.headers)x.setRequestHeader(r,m.headers[r]);if(m.beforeSend&&(!1===m.beforeSend.call(v,x,m)||p))return x.abort();if(l="abort",_.add(m.complete),x.done(m.success),x.fail(m.error),c=Kt(zt,m,t,x)){if(x.readyState=1,g&&y.trigger("ajaxSend",[x,m]),p)return x;m.async&&0<m.timeout&&(h=E.setTimeout(function(){x.abort("timeout")},m.timeout));try{p=!1,c.send(s,u)}catch(e){if(p)throw e;u(-1,e)}}else u(-1,"No Transport");function u(e,t,n,i){var r,o,s,a,l,u=t;p||(p=!0,h&&E.clearTimeout(h),c=void 0,d=i||"",x.readyState=0<e?4:0,r=200<=e&&e<300||304===e,n&&(a=function(e,t,n){for(var i,r,o,s,a=e.contents,l=e.dataTypes;"*"===l[0];)l.shift(),void 0===i&&(i=e.mimeType||t.getResponseHeader("Content-Type"));if(i)for(r in a)if(a[r]&&a[r].test(i)){l.unshift(r);break}if(l[0]in n)o=l[0];else{for(r in n){if(!l[0]||e.converters[r+" "+l[0]]){o=r;break}s=s||r}o=o||s}if(o)return o!==l[0]&&l.unshift(o),n[o]}(m,x,n)),!r&&-1<C.inArray("script",m.dataTypes)&&(m.converters["text script"]=function(){}),a=function(e,t,n,i){var r,o,s,a,l,u={},c=e.dataTypes.slice();if(c[1])for(s in e.converters)u[s.toLowerCase()]=e.converters[s];for(o=c.shift();o;)if(e.responseFields[o]&&(n[e.responseFields[o]]=t),!l&&i&&e.dataFilter&&(t=e.dataFilter(t,e.dataType)),l=o,o=c.shift())if("*"===o)o=l;else if("*"!==l&&l!==o){if(!(s=u[l+" "+o]||u["* "+o]))for(r in u)if((a=r.split(" "))[1]===o&&(s=u[l+" "+a[0]]||u["* "+a[0]])){!0===s?s=u[r]:!0!==u[r]&&(o=a[0],c.unshift(a[1]));break}if(!0!==s)if(s&&e.throws)t=s(t);else try{t=s(t)}catch(e){return{state:"parsererror",error:s?e:"No conversion from "+l+" to "+o}}}return{state:"success",data:t}}(m,a,x,r),r?(m.ifModified&&((l=x.getResponseHeader("Last-Modified"))&&(C.lastModified[f]=l),(l=x.getResponseHeader("etag"))&&(C.etag[f]=l)),204===e||"HEAD"===m.type?u="nocontent":304===e?u="notmodified":(u=a.state,o=a.data,r=!(s=a.error))):(s=u,!e&&u||(u="error",e<0&&(e=0))),x.status=e,x.statusText=(t||u)+"",r?b.resolveWith(v,[o,u,x]):b.rejectWith(v,[x,u,s]),x.statusCode(w),w=void 0,g&&y.trigger(r?"ajaxSuccess":"ajaxError",[x,m,r?o:s]),_.fireWith(v,[x,u]),g&&(y.trigger("ajaxComplete",[x,m]),--C.active||C.event.trigger("ajaxStop")))}return x},getJSON:function(e,t,n){return C.get(e,t,n,"json")},getScript:function(e,t){return C.get(e,void 0,t,"script")}}),C.each(["get","post"],function(e,r){C[r]=function(e,t,n,i){return b(t)&&(i=i||n,n=t,t=void 0),C.ajax(C.extend({url:e,type:r,dataType:i,data:t,success:n},C.isPlainObject(e)&&e))}}),C.ajaxPrefilter(function(e){for(var t in e.headers)"content-type"===t.toLowerCase()&&(e.contentType=e.headers[t]||"")}),C._evalUrl=function(e,t,n){return C.ajax({url:e,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(e){C.globalEval(e,t,n)}})},C.fn.extend({wrapAll:function(e){var t;return this[0]&&(b(e)&&(e=e.call(this[0])),t=C(e,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var e=this;e.firstElementChild;)e=e.firstElementChild;return e}).append(this)),this},wrapInner:function(n){return b(n)?this.each(function(e){C(this).wrapInner(n.call(this,e))}):this.each(function(){var e=C(this),t=e.contents();t.length?t.wrapAll(n):e.append(n)})},wrap:function(t){var n=b(t);return this.each(function(e){C(this).wrapAll(n?t.call(this,e):t)})},unwrap:function(e){return this.parent(e).not("body").each(function(){C(this).replaceWith(this.childNodes)}),this}}),C.expr.pseudos.hidden=function(e){return!C.expr.pseudos.visible(e)},C.expr.pseudos.visible=function(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)},C.ajaxSettings.xhr=function(){try{return new E.XMLHttpRequest}catch(e){}};var Jt={0:200,1223:204},Zt=C.ajaxSettings.xhr();y.cors=!!Zt&&"withCredentials"in Zt,y.ajax=Zt=!!Zt,C.ajaxTransport(function(r){var o,s;if(y.cors||Zt&&!r.crossDomain)return{send:function(e,t){var n,i=r.xhr();if(i.open(r.type,r.url,r.async,r.username,r.password),r.xhrFields)for(n in r.xhrFields)i[n]=r.xhrFields[n];for(n in r.mimeType&&i.overrideMimeType&&i.overrideMimeType(r.mimeType),r.crossDomain||e["X-Requested-With"]||(e["X-Requested-With"]="XMLHttpRequest"),e)i.setRequestHeader(n,e[n]);o=function(e){return function(){o&&(o=s=i.onload=i.onerror=i.onabort=i.ontimeout=i.onreadystatechange=null,"abort"===e?i.abort():"error"===e?"number"!=typeof i.status?t(0,"error"):t(i.status,i.statusText):t(Jt[i.status]||i.status,i.statusText,"text"!==(i.responseType||"text")||"string"!=typeof i.responseText?{binary:i.response}:{text:i.responseText},i.getAllResponseHeaders()))}},i.onload=o(),s=i.onerror=i.ontimeout=o("error"),void 0!==i.onabort?i.onabort=s:i.onreadystatechange=function(){4===i.readyState&&E.setTimeout(function(){o&&s()})},o=o("abort");try{i.send(r.hasContent&&r.data||null)}catch(e){if(o)throw e}},abort:function(){o&&o()}}}),C.ajaxPrefilter(function(e){e.crossDomain&&(e.contents.script=!1)}),C.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(e){return C.globalEval(e),e}}}),C.ajaxPrefilter("script",function(e){void 0===e.cache&&(e.cache=!1),e.crossDomain&&(e.type="GET")}),C.ajaxTransport("script",function(n){var i,r;if(n.crossDomain||n.scriptAttrs)return{send:function(e,t){i=C("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",r=function(e){i.remove(),r=null,e&&t("error"===e.type?404:200,e.type)}),T.head.appendChild(i[0])},abort:function(){r&&r()}}});var en,tn=[],nn=/(=)\?(?=&|$)|\?\?/;C.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=tn.pop()||C.expando+"_"+Lt.guid++;return this[e]=!0,e}}),C.ajaxPrefilter("json jsonp",function(e,t,n){var i,r,o,s=!1!==e.jsonp&&(nn.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&nn.test(e.data)&&"data");if(s||"jsonp"===e.dataTypes[0])return i=e.jsonpCallback=b(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,s?e[s]=e[s].replace(nn,"$1"+i):!1!==e.jsonp&&(e.url+=(It.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return o||C.error(i+" was not called"),o[0]},e.dataTypes[0]="json",r=E[i],E[i]=function(){o=arguments},n.always(function(){void 0===r?C(E).removeProp(i):E[i]=r,e[i]&&(e.jsonpCallback=t.jsonpCallback,tn.push(i)),o&&b(r)&&r(o[0]),o=r=void 0}),"script"}),y.createHTMLDocument=((en=T.implementation.createHTMLDocument("").body).innerHTML="<form></form><form></form>",2===en.childNodes.length),C.parseHTML=function(e,t,n){return"string"!=typeof e?[]:("boolean"==typeof t&&(n=t,t=!1),t||(y.createHTMLDocument?((i=(t=T.implementation.createHTMLDocument("")).createElement("base")).href=T.location.href,t.head.appendChild(i)):t=T),o=!n&&[],(r=N.exec(e))?[t.createElement(r[1])]:(r=be([e],t,o),o&&o.length&&C(o).remove(),C.merge([],r.childNodes)));var i,r,o},C.fn.load=function(e,t,n){var i,r,o,s=this,a=e.indexOf(" ");return-1<a&&(i=St(e.slice(a)),e=e.slice(0,a)),b(t)?(n=t,t=void 0):t&&"object"==typeof t&&(r="POST"),0<s.length&&C.ajax({url:e,type:r||"GET",dataType:"html",data:t}).done(function(e){o=arguments,s.html(i?C("<div>").append(C.parseHTML(e)).find(i):e)}).always(n&&function(e,t){s.each(function(){n.apply(this,o||[e.responseText,t,e])})}),this},C.expr.pseudos.animated=function(t){return C.grep(C.timers,function(e){return t===e.elem}).length},C.offset={setOffset:function(e,t,n){var i,r,o,s,a,l,u=C.css(e,"position"),c=C(e),f={};"static"===u&&(e.style.position="relative"),a=c.offset(),o=C.css(e,"top"),l=C.css(e,"left"),r=("absolute"===u||"fixed"===u)&&-1<(o+l).indexOf("auto")?(s=(i=c.position()).top,i.left):(s=parseFloat(o)||0,parseFloat(l)||0),b(t)&&(t=t.call(e,n,C.extend({},a))),null!=t.top&&(f.top=t.top-a.top+s),null!=t.left&&(f.left=t.left-a.left+r),"using"in t?t.using.call(e,f):("number"==typeof f.top&&(f.top+="px"),"number"==typeof f.left&&(f.left+="px"),c.css(f))}},C.fn.extend({offset:function(t){if(arguments.length)return void 0===t?this:this.each(function(e){C.offset.setOffset(this,t,e)});var e,n,i=this[0];return i?i.getClientRects().length?(e=i.getBoundingClientRect(),n=i.ownerDocument.defaultView,{top:e.top+n.pageYOffset,left:e.left+n.pageXOffset}):{top:0,left:0}:void 0},position:function(){if(this[0]){var e,t,n,i=this[0],r={top:0,left:0};if("fixed"===C.css(i,"position"))t=i.getBoundingClientRect();else{for(t=this.offset(),n=i.ownerDocument,e=i.offsetParent||n.documentElement;e&&(e===n.body||e===n.documentElement)&&"static"===C.css(e,"position");)e=e.parentNode;e&&e!==i&&1===e.nodeType&&((r=C(e).offset()).top+=C.css(e,"borderTopWidth",!0),r.left+=C.css(e,"borderLeftWidth",!0))}return{top:t.top-r.top-C.css(i,"marginTop",!0),left:t.left-r.left-C.css(i,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var e=this.offsetParent;e&&"static"===C.css(e,"position");)e=e.offsetParent;return e||ie})}}),C.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(t,r){var o="pageYOffset"===r;C.fn[t]=function(e){return Q(this,function(e,t,n){var i;return g(e)?i=e:9===e.nodeType&&(i=e.defaultView),void 0===n?i?i[r]:e[t]:void(i?i.scrollTo(o?i.pageXOffset:n,o?n:i.pageYOffset):e[t]=n)},t,e,arguments.length)}}),C.each(["top","left"],function(e,n){C.cssHooks[n]=Ke(y.pixelPosition,function(e,t){if(t)return t=Ye(e,n),ze.test(t)?C(e).position()[n]+"px":t})}),C.each({Height:"height",Width:"width"},function(s,a){C.each({padding:"inner"+s,content:a,"":"outer"+s},function(i,o){C.fn[o]=function(e,t){var n=arguments.length&&(i||"boolean"!=typeof e),r=i||(!0===e||!0===t?"margin":"border");return Q(this,function(e,t,n){var i;return g(e)?0===o.indexOf("outer")?e["inner"+s]:e.document.documentElement["client"+s]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+s],i["scroll"+s],e.body["offset"+s],i["offset"+s],i["client"+s])):void 0===n?C.css(e,t,r):C.style(e,t,n,r)},a,n?e:void 0,n)}})}),C.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(e,t){C.fn[t]=function(e){return this.on(t,e)}}),C.fn.extend({bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},delegate:function(e,t,n,i){return this.on(t,e,n,i)},undelegate:function(e,t,n){return 1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),C.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){C.fn[n]=function(e,t){return 0<arguments.length?this.on(n,null,e,t):this.trigger(n)}});var rn=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;C.proxy=function(e,t){var n,i,r;if("string"==typeof t&&(n=e[t],t=e,e=n),b(e))return i=a.call(arguments,2),(r=function(){return e.apply(t||this,i.concat(a.call(arguments)))}).guid=e.guid=e.guid||C.guid++,r},C.holdReady=function(e){e?C.readyWait++:C.ready(!0)},C.isArray=Array.isArray,C.parseJSON=JSON.parse,C.nodeName=A,C.isFunction=b,C.isWindow=g,C.camelCase=z,C.type=w,C.now=Date.now,C.isNumeric=function(e){var t=C.type(e);return("number"===t||"string"===t)&&!isNaN(e-parseFloat(e))},C.trim=function(e){return null==e?"":(e+"").replace(rn,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return C});var on=E.jQuery,sn=E.$;return C.noConflict=function(e){return E.$===C&&(E.$=sn),e&&E.jQuery===C&&(E.jQuery=on),C},void 0===e&&(E.jQuery=E.$=C),C}),void 0===jQuery.migrateMute&&(jQuery.migrateMute=!0),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e,window)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery"),window):t(jQuery,window)}(function(a,i){"use strict";function e(e){return 0<=function(e,t){for(var n=/^(\d+)\.(\d+)\.(\d+)/,i=n.exec(e)||[],r=n.exec(t)||[],o=1;o<=3;o++){if(+r[o]<+i[o])return 1;if(+i[o]<+r[o])return-1}return 0}(a.fn.jquery,e)}a.migrateVersion="3.3.1",i.console&&i.console.log&&(a&&e("3.0.0")||i.console.log("JQMIGRATE: jQuery 3.0.0+ REQUIRED"),a.migrateWarnings&&i.console.log("JQMIGRATE: Migrate plugin loaded multiple times"),i.console.log("JQMIGRATE: Migrate is installed"+(a.migrateMute?"":" with logging active")+", version "+a.migrateVersion));var n={};function l(e){var t=i.console;a.migrateDeduplicateWarnings&&n[e]||(n[e]=!0,a.migrateWarnings.push(e),t&&t.warn&&!a.migrateMute&&(t.warn("JQMIGRATE: "+e),a.migrateTrace&&t.trace&&t.trace()))}function t(e,t,n,i){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return l(i),n},set:function(e){l(i),n=e}})}function r(e,t,n,i){e[t]=function(){return l(i),n.apply(this,arguments)}}a.migrateDeduplicateWarnings=!0,a.migrateWarnings=[],void 0===a.migrateTrace&&(a.migrateTrace=!0),a.migrateReset=function(){n={},a.migrateWarnings.length=0},"BackCompat"===i.document.compatMode&&l("jQuery is not compatible with Quirks Mode");var o,s,u={},c=a.fn.init,f=a.find,d=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/,h=/\[(\s*[-\w]+\s*)([~|^$*]?=)\s*([-\w#]*?#[-\w#]*)\s*\]/g,p=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;for(o in a.fn.init=function(e){var t=Array.prototype.slice.call(arguments);return"string"==typeof e&&"#"===e&&(l("jQuery( '#' ) is not a valid selector"),t[0]=[]),c.apply(this,t)},a.fn.init.prototype=a.fn,a.find=function(t){var n=Array.prototype.slice.call(arguments);if("string"==typeof t&&d.test(t))try{i.document.querySelector(t)}catch(e){t=t.replace(h,function(e,t,n,i){return"["+t+n+'"'+i+'"]'});try{i.document.querySelector(t),l("Attribute selector with '#' must be quoted: "+n[0]),n[0]=t}catch(e){l("Attribute selector with '#' was not fixed: "+n[0])}}return f.apply(this,n)},f)Object.prototype.hasOwnProperty.call(f,o)&&(a.find[o]=f[o]);r(a.fn,"size",function(){return this.length},"jQuery.fn.size() is deprecated and removed; use the .length property"),r(a,"parseJSON",function(){return JSON.parse.apply(null,arguments)},"jQuery.parseJSON is deprecated; use JSON.parse"),r(a,"holdReady",a.holdReady,"jQuery.holdReady is deprecated"),r(a,"unique",a.uniqueSort,"jQuery.unique is deprecated; use jQuery.uniqueSort"),t(a.expr,"filters",a.expr.pseudos,"jQuery.expr.filters is deprecated; use jQuery.expr.pseudos"),t(a.expr,":",a.expr.pseudos,"jQuery.expr[':'] is deprecated; use jQuery.expr.pseudos"),e("3.1.1")&&r(a,"trim",function(e){return null==e?"":(e+"").replace(p,"")},"jQuery.trim is deprecated; use String.prototype.trim"),e("3.2.0")&&r(a,"nodeName",function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},"jQuery.nodeName is deprecated"),e("3.3.0")&&(r(a,"isNumeric",function(e){var t=typeof e;return("number"==t||"string"==t)&&!isNaN(e-parseFloat(e))},"jQuery.isNumeric() is deprecated"),a.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(e,t){u["[object "+t+"]"]=t.toLowerCase()}),r(a,"type",function(e){return null==e?e+"":"object"==typeof e||"function"==typeof e?u[Object.prototype.toString.call(e)]||"object":typeof e},"jQuery.type is deprecated"),r(a,"isFunction",function(e){return"function"==typeof e},"jQuery.isFunction() is deprecated"),r(a,"isWindow",function(e){return null!=e&&e===e.window},"jQuery.isWindow() is deprecated"),r(a,"isArray",Array.isArray,"jQuery.isArray is deprecated; use Array.isArray")),a.ajax&&(s=a.ajax,a.ajax=function(){var e=s.apply(this,arguments);return e.promise&&(r(e,"success",e.done,"jQXHR.success is deprecated and removed"),r(e,"error",e.fail,"jQXHR.error is deprecated and removed"),r(e,"complete",e.always,"jQXHR.complete is deprecated and removed")),e});var g=a.fn.removeAttr,m=a.fn.toggleClass,v=/\S+/g;function y(e){return e.replace(/-([a-z])/g,function(e,t){return t.toUpperCase()})}a.fn.removeAttr=function(e){var n=this;return a.each(e.match(v),function(e,t){a.expr.match.bool.test(t)&&(l("jQuery.fn.removeAttr no longer sets boolean properties: "+t),n.prop(t,!1))}),g.apply(this,arguments)};var b,_=!(a.fn.toggleClass=function(t){return void 0!==t&&"boolean"!=typeof t?m.apply(this,arguments):(l("jQuery.fn.toggleClass( boolean ) is deprecated"),this.each(function(){var e=this.getAttribute&&this.getAttribute("class")||"";e&&a.data(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==t&&a.data(this,"__className__")||"")}))}),w=/^[a-z]/,x=/^(?:Border(?:Top|Right|Bottom|Left)?(?:Width|)|(?:Margin|Padding)?(?:Top|Right|Bottom|Left)?|(?:Min|Max)?(?:Width|Height))$/;a.swap&&a.each(["height","width","reliableMarginRight"],function(e,t){var n=a.cssHooks[t]&&a.cssHooks[t].get;n&&(a.cssHooks[t].get=function(){var e;return _=!0,e=n.apply(this,arguments),_=!1,e})}),a.swap=function(e,t,n,i){var r,o,s={};for(o in _||l("jQuery.swap() is undocumented and deprecated"),t)s[o]=e.style[o],e.style[o]=t[o];for(o in r=n.apply(e,i||[]),t)e.style[o]=s[o];return r},e("3.4.0")&&"undefined"!=typeof Proxy&&(a.cssProps=new Proxy(a.cssProps||{},{set:function(){return l("JQMIGRATE: jQuery.cssProps is deprecated"),Reflect.set.apply(this,arguments)}})),a.cssNumber||(a.cssNumber={}),b=a.fn.css,a.fn.css=function(e,t){var n,i,r=this;return e&&"object"==typeof e&&!Array.isArray(e)&&a.each(e,function(e,t){a.fn.css.call(r,e,t)}),"number"==typeof t&&(i=n=y(e),w.test(i)&&x.test(i[0].toUpperCase()+i.slice(1))||a.cssNumber[n]||l('Number-typed values are deprecated for jQuery.fn.css( "'+e+'", value )')),b.apply(this,arguments)};var E,T,C,S,A=a.data;a.data=function(e,t,n){var i,r,o;if(t&&"object"==typeof t&&2===arguments.length){for(o in i=a.hasData(e)&&A.call(this,e),r={},t)o!==y(o)?(l("jQuery.data() always sets/gets camelCased names: "+o),i[o]=t[o]):r[o]=t[o];return A.call(this,e,r),t}return t&&"string"==typeof t&&t!==y(t)&&(i=a.hasData(e)&&A.call(this,e))&&t in i?(l("jQuery.data() always sets/gets camelCased names: "+t),2<arguments.length&&(i[t]=n),i[t]):A.apply(this,arguments)},a.fx&&(C=a.Tween.prototype.run,S=function(e){return e},a.Tween.prototype.run=function(){1<a.easing[this.easing].length&&(l("'jQuery.easing."+this.easing.toString()+"' should use only one argument"),a.easing[this.easing]=S),C.apply(this,arguments)},E=a.fx.interval||13,T="jQuery.fx.interval is deprecated",i.requestAnimationFrame&&Object.defineProperty(a.fx,"interval",{configurable:!0,enumerable:!0,get:function(){return i.document.hidden||l(T),E},set:function(e){l(T),E=e}}));var N=a.fn.load,k=a.event.add,D=a.event.fix;function j(e){var t=i.document.implementation.createHTMLDocument("");return t.body.innerHTML=e,t.body&&t.body.innerHTML}function O(e){var t=e.replace(L,"<$1></$2>");t!==e&&j(e)!==j(t)&&l("HTML tags must be properly nested and closed: "+e)}a.event.props=[],a.event.fixHooks={},t(a.event.props,"concat",a.event.props.concat,"jQuery.event.props.concat() is deprecated and removed"),a.event.fix=function(e){var t,n=e.type,i=this.fixHooks[n],r=a.event.props;if(r.length)for(l("jQuery.event.props are deprecated and removed: "+r.join());r.length;)a.event.addProp(r.pop());if(i&&!i._migrated_&&(i._migrated_=!0,l("jQuery.event.fixHooks are deprecated and removed: "+n),(r=i.props)&&r.length))for(;r.length;)a.event.addProp(r.pop());return t=D.call(this,e),i&&i.filter?i.filter(t,e):t},a.event.add=function(e,t){return e===i&&"load"===t&&"complete"===i.document.readyState&&l("jQuery(window).on('load'...) called after load event occurred"),k.apply(this,arguments)},a.each(["load","unload","error"],function(e,t){a.fn[t]=function(){var e=Array.prototype.slice.call(arguments,0);return"load"===t&&"string"==typeof e[0]?N.apply(this,e):(l("jQuery.fn."+t+"() is deprecated"),e.splice(0,0,t),arguments.length?this.on.apply(this,e):(this.triggerHandler.apply(this,e),this))}}),a.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(e,n){a.fn[n]=function(e,t){return l("jQuery.fn."+n+"() event shorthand is deprecated"),0<arguments.length?this.on(n,null,e,t):this.trigger(n)}}),a(function(){a(i.document).triggerHandler("ready")}),a.event.special.ready={setup:function(){this===i.document&&l("'ready' event is deprecated")}},a.fn.extend({bind:function(e,t,n){return l("jQuery.fn.bind() is deprecated"),this.on(e,null,t,n)},unbind:function(e,t){return l("jQuery.fn.unbind() is deprecated"),this.off(e,null,t)},delegate:function(e,t,n,i){return l("jQuery.fn.delegate() is deprecated"),this.on(t,e,n,i)},undelegate:function(e,t,n){return l("jQuery.fn.undelegate() is deprecated"),1===arguments.length?this.off(e,"**"):this.off(t,e||"**",n)},hover:function(e,t){return l("jQuery.fn.hover() is deprecated"),this.on("mouseenter",e).on("mouseleave",t||e)}});var L=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([a-z][^\/\0>\x20\t\r\n\f]*)[^>]*)\/>/gi,I=a.htmlPrefilter;a.UNSAFE_restoreLegacyHtmlPrefilter=function(){a.htmlPrefilter=function(e){return O(e),e.replace(L,"<$1></$2>")}},a.htmlPrefilter=function(e){return O(e),I(e)};var q,P=a.fn.offset;a.fn.offset=function(){var e=this[0];return!e||e.nodeType&&e.getBoundingClientRect?P.apply(this,arguments):(l("jQuery.fn.offset() requires a valid DOM element"),arguments.length?this:void 0)},a.ajax&&(q=a.param,a.param=function(e,t){var n=a.ajaxSettings&&a.ajaxSettings.traditional;return void 0===t&&n&&(l("jQuery.param() no longer uses jQuery.ajaxSettings.traditional"),t=n),q.call(this,e,t)});var H,R,M=a.fn.andSelf||a.fn.addBack;return a.fn.andSelf=function(){return l("jQuery.fn.andSelf() is deprecated and removed, use jQuery.fn.addBack()"),M.apply(this,arguments)},a.Deferred&&(H=a.Deferred,R=[["resolve","done",a.Callbacks("once memory"),a.Callbacks("once memory"),"resolved"],["reject","fail",a.Callbacks("once memory"),a.Callbacks("once memory"),"rejected"],["notify","progress",a.Callbacks("memory"),a.Callbacks("memory")]],a.Deferred=function(e){var o=H(),s=o.promise();return o.pipe=s.pipe=function(){var r=arguments;return l("deferred.pipe() is deprecated"),a.Deferred(function(i){a.each(R,function(e,t){var n="function"==typeof r[e]&&r[e];o[t[1]](function(){var e=n&&n.apply(this,arguments);e&&"function"==typeof e.promise?e.promise().done(i.resolve).fail(i.reject).progress(i.notify):i[t[0]+"With"](this===s?i.promise():this,n?[e]:arguments)})}),r=null}).promise()},e&&e.call(o,o),o},a.Deferred.exceptionHook=H.exceptionHook),a}),function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("jquery")):"function"==typeof define&&define.amd?define(["exports","jquery"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).bootstrap={},e.jQuery)}(this,function(e,p){"use strict";function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function s(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}function l(){return(l=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}p=p&&Object.prototype.hasOwnProperty.call(p,"default")?p.default:p;var t="transitionend";function n(e){var t=this,n=!1;return p(this).one(g.TRANSITION_END,function(){n=!0}),setTimeout(function(){n||g.triggerTransitionEnd(t)},e),this}var g={TRANSITION_END:"bsTransitionEnd",getUID:function(e){for(;e+=~~(1e6*Math.random()),document.getElementById(e););return e},getSelectorFromElement:function(e){var t,n=e.getAttribute("data-target");n&&"#"!==n||(n=(t=e.getAttribute("href"))&&"#"!==t?t.trim():"");try{return document.querySelector(n)?n:null}catch(e){return null}},getTransitionDurationFromElement:function(e){if(!e)return 0;var t=p(e).css("transition-duration"),n=p(e).css("transition-delay"),i=parseFloat(t),r=parseFloat(n);return i||r?(t=t.split(",")[0],n=n.split(",")[0],1e3*(parseFloat(t)+parseFloat(n))):0},reflow:function(e){return e.offsetHeight},triggerTransitionEnd:function(e){p(e).trigger(t)},supportsTransitionEnd:function(){return Boolean(t)},isElement:function(e){return(e[0]||e).nodeType},typeCheckConfig:function(e,t,n){for(var i in n)if(Object.prototype.hasOwnProperty.call(n,i)){var r=n[i],o=t[i],s=o&&g.isElement(o)?"element":null==(a=o)?""+a:{}.toString.call(a).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(r).test(s))throw new Error(e.toUpperCase()+': Option "'+i+'" provided type "'+s+'" but expected type "'+r+'".')}var a},findShadowRoot:function(e){if(!document.documentElement.attachShadow)return null;if("function"!=typeof e.getRootNode)return e instanceof ShadowRoot?e:e.parentNode?g.findShadowRoot(e.parentNode):null;var t=e.getRootNode();return t instanceof ShadowRoot?t:null},jQueryDetection:function(){if(void 0===p)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=p.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||4<=e[0])throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}};g.jQueryDetection(),p.fn.emulateTransitionEnd=n,p.event.special[g.TRANSITION_END]={bindType:t,delegateType:t,handle:function(e){if(p(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}};var r="alert",o="bs.alert",a=p.fn[r],u=function(){function i(e){this._element=e}var e=i.prototype;return e.close=function(e){var t=this._element;e&&(t=this._getRootElement(e)),this._triggerCloseEvent(t).isDefaultPrevented()||this._removeElement(t)},e.dispose=function(){p.removeData(this._element,o),this._element=null},e._getRootElement=function(e){var t=g.getSelectorFromElement(e),n=!1;return t&&(n=document.querySelector(t)),n=n||p(e).closest(".alert")[0]},e._triggerCloseEvent=function(e){var t=p.Event("close.bs.alert");return p(e).trigger(t),t},e._removeElement=function(t){var e,n=this;p(t).removeClass("show"),p(t).hasClass("fade")?(e=g.getTransitionDurationFromElement(t),p(t).one(g.TRANSITION_END,function(e){return n._destroyElement(t,e)}).emulateTransitionEnd(e)):this._destroyElement(t)},e._destroyElement=function(e){p(e).detach().trigger("closed.bs.alert").remove()},i._jQueryInterface=function(n){return this.each(function(){var e=p(this),t=e.data(o);t||(t=new i(this),e.data(o,t)),"close"===n&&t[n](this)})},i._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},s(i,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),i}();p(document).on("click.bs.alert.data-api",'[data-dismiss="alert"]',u._handleDismiss(new u)),p.fn[r]=u._jQueryInterface,p.fn[r].Constructor=u,p.fn[r].noConflict=function(){return p.fn[r]=a,u._jQueryInterface};var c="button",f="bs.button",d=p.fn[c],h="active",m='[data-toggle^="button"]',v='input:not([type="hidden"])',y=function(){function n(e){this._element=e}var e=n.prototype;return e.toggle=function(){var e,t,n=!0,i=!0,r=p(this._element).closest('[data-toggle="buttons"]')[0];!r||(e=this._element.querySelector(v))&&("radio"===e.type&&(e.checked&&this._element.classList.contains(h)?n=!1:(t=r.querySelector(".active"))&&p(t).removeClass(h)),n&&("checkbox"!==e.type&&"radio"!==e.type||(e.checked=!this._element.classList.contains(h)),p(e).trigger("change")),e.focus(),i=!1),this._element.hasAttribute("disabled")||this._element.classList.contains("disabled")||(i&&this._element.setAttribute("aria-pressed",!this._element.classList.contains(h)),n&&p(this._element).toggleClass(h))},e.dispose=function(){p.removeData(this._element,f),this._element=null},n._jQueryInterface=function(t){return this.each(function(){var e=p(this).data(f);e||(e=new n(this),p(this).data(f,e)),"toggle"===t&&e[t]()})},s(n,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),n}();p(document).on("click.bs.button.data-api",m,function(e){var t=e.target,n=t;if(p(t).hasClass("btn")||(t=p(t).closest(".btn")[0]),!t||t.hasAttribute("disabled")||t.classList.contains("disabled"))e.preventDefault();else{var i=t.querySelector(v);if(i&&(i.hasAttribute("disabled")||i.classList.contains("disabled")))return void e.preventDefault();("LABEL"!==n.tagName||i&&"checkbox"!==i.type)&&y._jQueryInterface.call(p(t),"toggle")}}).on("focus.bs.button.data-api blur.bs.button.data-api",m,function(e){var t=p(e.target).closest(".btn")[0];p(t).toggleClass("focus",/^focus(in)?$/.test(e.type))}),p(window).on("load.bs.button.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-toggle="buttons"] .btn')),t=0,n=e.length;t<n;t++){var i=e[t],r=i.querySelector(v);r.checked||r.hasAttribute("checked")?i.classList.add(h):i.classList.remove(h)}for(var o=0,s=(e=[].slice.call(document.querySelectorAll('[data-toggle="button"]'))).length;o<s;o++){var a=e[o];"true"===a.getAttribute("aria-pressed")?a.classList.add(h):a.classList.remove(h)}}),p.fn[c]=y._jQueryInterface,p.fn[c].Constructor=y,p.fn[c].noConflict=function(){return p.fn[c]=d,y._jQueryInterface};var b="carousel",_="bs.carousel",w="."+_,x=p.fn[b],E={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0,touch:!0},T={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean",touch:"boolean"},C="next",S="prev",A="slid"+w,N="active",k=".active.carousel-item",D={TOUCH:"touch",PEN:"pen"},j=function(){function o(e,t){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this.touchStartX=0,this.touchDeltaX=0,this._config=this._getConfig(t),this._element=e,this._indicatorsElement=this._element.querySelector(".carousel-indicators"),this._touchSupported="ontouchstart"in document.documentElement||0<navigator.maxTouchPoints,this._pointerEvent=Boolean(window.PointerEvent||window.MSPointerEvent),this._addEventListeners()}var e=o.prototype;return e.next=function(){this._isSliding||this._slide(C)},e.nextWhenVisible=function(){!document.hidden&&p(this._element).is(":visible")&&"hidden"!==p(this._element).css("visibility")&&this.next()},e.prev=function(){this._isSliding||this._slide(S)},e.pause=function(e){e||(this._isPaused=!0),this._element.querySelector(".carousel-item-next, .carousel-item-prev")&&(g.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},e.cycle=function(e){e||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},e.to=function(e){var t=this;this._activeElement=this._element.querySelector(k);var n=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)p(this._element).one(A,function(){return t.to(e)});else{if(n===e)return this.pause(),void this.cycle();var i=n<e?C:S;this._slide(i,this._items[e])}},e.dispose=function(){p(this._element).off(w),p.removeData(this._element,_),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},e._getConfig=function(e){return e=l({},E,e),g.typeCheckConfig(b,e,T),e},e._handleSwipe=function(){var e,t=Math.abs(this.touchDeltaX);t<=40||(e=t/this.touchDeltaX,(this.touchDeltaX=0)<e&&this.prev(),e<0&&this.next())},e._addEventListeners=function(){var t=this;this._config.keyboard&&p(this._element).on("keydown.bs.carousel",function(e){return t._keydown(e)}),"hover"===this._config.pause&&p(this._element).on("mouseenter.bs.carousel",function(e){return t.pause(e)}).on("mouseleave.bs.carousel",function(e){return t.cycle(e)}),this._config.touch&&this._addTouchEventListeners()},e._addTouchEventListeners=function(){var e,t,n=this;this._touchSupported&&(e=function(e){n._pointerEvent&&D[e.originalEvent.pointerType.toUpperCase()]?n.touchStartX=e.originalEvent.clientX:n._pointerEvent||(n.touchStartX=e.originalEvent.touches[0].clientX)},t=function(e){n._pointerEvent&&D[e.originalEvent.pointerType.toUpperCase()]&&(n.touchDeltaX=e.originalEvent.clientX-n.touchStartX),n._handleSwipe(),"hover"===n._config.pause&&(n.pause(),n.touchTimeout&&clearTimeout(n.touchTimeout),n.touchTimeout=setTimeout(function(e){return n.cycle(e)},500+n._config.interval))},p(this._element.querySelectorAll(".carousel-item img")).on("dragstart.bs.carousel",function(e){return e.preventDefault()}),this._pointerEvent?(p(this._element).on("pointerdown.bs.carousel",e),p(this._element).on("pointerup.bs.carousel",t),this._element.classList.add("pointer-event")):(p(this._element).on("touchstart.bs.carousel",e),p(this._element).on("touchmove.bs.carousel",function(e){var t;(t=e).originalEvent.touches&&1<t.originalEvent.touches.length?n.touchDeltaX=0:n.touchDeltaX=t.originalEvent.touches[0].clientX-n.touchStartX}),p(this._element).on("touchend.bs.carousel",t)))},e._keydown=function(e){if(!/input|textarea/i.test(e.target.tagName))switch(e.which){case 37:e.preventDefault(),this.prev();break;case 39:e.preventDefault(),this.next()}},e._getItemIndex=function(e){return this._items=e&&e.parentNode?[].slice.call(e.parentNode.querySelectorAll(".carousel-item")):[],this._items.indexOf(e)},e._getItemByDirection=function(e,t){var n=e===C,i=e===S,r=this._getItemIndex(t),o=this._items.length-1;if((i&&0===r||n&&r===o)&&!this._config.wrap)return t;var s=(r+(e===S?-1:1))%this._items.length;return-1==s?this._items[this._items.length-1]:this._items[s]},e._triggerSlideEvent=function(e,t){var n=this._getItemIndex(e),i=this._getItemIndex(this._element.querySelector(k)),r=p.Event("slide.bs.carousel",{relatedTarget:e,direction:t,from:i,to:n});return p(this._element).trigger(r),r},e._setActiveIndicatorElement=function(e){var t,n;this._indicatorsElement&&(t=[].slice.call(this._indicatorsElement.querySelectorAll(".active")),p(t).removeClass(N),(n=this._indicatorsElement.children[this._getItemIndex(e)])&&p(n).addClass(N))},e._slide=function(e,t){var n,i,r,o,s,a=this,l=this._element.querySelector(k),u=this._getItemIndex(l),c=t||l&&this._getItemByDirection(e,l),f=this._getItemIndex(c),d=Boolean(this._interval),h=e===C?(n="carousel-item-left",i="carousel-item-next","left"):(n="carousel-item-right",i="carousel-item-prev","right");c&&p(c).hasClass(N)?this._isSliding=!1:this._triggerSlideEvent(c,h).isDefaultPrevented()||l&&c&&(this._isSliding=!0,d&&this.pause(),this._setActiveIndicatorElement(c),r=p.Event(A,{relatedTarget:c,direction:h,from:u,to:f}),p(this._element).hasClass("slide")?(p(c).addClass(i),g.reflow(c),p(l).addClass(n),p(c).addClass(n),(o=parseInt(c.getAttribute("data-interval"),10))?(this._config.defaultInterval=this._config.defaultInterval||this._config.interval,this._config.interval=o):this._config.interval=this._config.defaultInterval||this._config.interval,s=g.getTransitionDurationFromElement(l),p(l).one(g.TRANSITION_END,function(){p(c).removeClass(n+" "+i).addClass(N),p(l).removeClass(N+" "+i+" "+n),a._isSliding=!1,setTimeout(function(){return p(a._element).trigger(r)},0)}).emulateTransitionEnd(s)):(p(l).removeClass(N),p(c).addClass(N),this._isSliding=!1,p(this._element).trigger(r)),d&&this.cycle())},o._jQueryInterface=function(i){return this.each(function(){var e=p(this).data(_),t=l({},E,p(this).data());"object"==typeof i&&(t=l({},t,i));var n="string"==typeof i?i:t.slide;if(e||(e=new o(this,t),p(this).data(_,e)),"number"==typeof i)e.to(i);else if("string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}else t.interval&&t.ride&&(e.pause(),e.cycle())})},o._dataApiClickHandler=function(e){var t,n,i,r=g.getSelectorFromElement(this);!r||(t=p(r)[0])&&p(t).hasClass("carousel")&&(n=l({},p(t).data(),p(this).data()),(i=this.getAttribute("data-slide-to"))&&(n.interval=!1),o._jQueryInterface.call(p(t),n),i&&p(t).data(_).to(i),e.preventDefault())},s(o,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return E}}]),o}();p(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",j._dataApiClickHandler),p(window).on("load.bs.carousel.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-ride="carousel"]')),t=0,n=e.length;t<n;t++){var i=p(e[t]);j._jQueryInterface.call(i,i.data())}}),p.fn[b]=j._jQueryInterface,p.fn[b].Constructor=j,p.fn[b].noConflict=function(){return p.fn[b]=x,j._jQueryInterface};var O="collapse",L="bs.collapse",I=p.fn[O],q={toggle:!0,parent:""},P={toggle:"boolean",parent:"(string|element)"},H="show",R="collapse",M="collapsing",F="collapsed",B='[data-toggle="collapse"]',Q=function(){function a(t,e){this._isTransitioning=!1,this._element=t,this._config=this._getConfig(e),this._triggerArray=[].slice.call(document.querySelectorAll('[data-toggle="collapse"][href="#'+t.id+'"],[data-toggle="collapse"][data-target="#'+t.id+'"]'));for(var n=[].slice.call(document.querySelectorAll(B)),i=0,r=n.length;i<r;i++){var o=n[i],s=g.getSelectorFromElement(o),a=[].slice.call(document.querySelectorAll(s)).filter(function(e){return e===t});null!==s&&0<a.length&&(this._selector=s,this._triggerArray.push(o))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var e=a.prototype;return e.toggle=function(){p(this._element).hasClass(H)?this.hide():this.show()},e.show=function(){var e,t,n,i,r,o,s=this;this._isTransitioning||p(this._element).hasClass(H)||(this._parent&&0===(e=[].slice.call(this._parent.querySelectorAll(".show, .collapsing")).filter(function(e){return"string"==typeof s._config.parent?e.getAttribute("data-parent")===s._config.parent:e.classList.contains(R)})).length&&(e=null),e&&(t=p(e).not(this._selector).data(L))&&t._isTransitioning||(n=p.Event("show.bs.collapse"),p(this._element).trigger(n),n.isDefaultPrevented()||(e&&(a._jQueryInterface.call(p(e).not(this._selector),"hide"),t||p(e).data(L,null)),i=this._getDimension(),p(this._element).removeClass(R).addClass(M),this._element.style[i]=0,this._triggerArray.length&&p(this._triggerArray).removeClass(F).attr("aria-expanded",!0),this.setTransitioning(!0),r="scroll"+(i[0].toUpperCase()+i.slice(1)),o=g.getTransitionDurationFromElement(this._element),p(this._element).one(g.TRANSITION_END,function(){p(s._element).removeClass(M).addClass(R+" "+H),s._element.style[i]="",s.setTransitioning(!1),p(s._element).trigger("shown.bs.collapse")}).emulateTransitionEnd(o),this._element.style[i]=this._element[r]+"px")))},e.hide=function(){var e=this;if(!this._isTransitioning&&p(this._element).hasClass(H)){var t=p.Event("hide.bs.collapse");if(p(this._element).trigger(t),!t.isDefaultPrevented()){var n=this._getDimension();this._element.style[n]=this._element.getBoundingClientRect()[n]+"px",g.reflow(this._element),p(this._element).addClass(M).removeClass(R+" "+H);var i=this._triggerArray.length;if(0<i)for(var r=0;r<i;r++){var o=this._triggerArray[r],s=g.getSelectorFromElement(o);null!==s&&(p([].slice.call(document.querySelectorAll(s))).hasClass(H)||p(o).addClass(F).attr("aria-expanded",!1))}this.setTransitioning(!0);this._element.style[n]="";var a=g.getTransitionDurationFromElement(this._element);p(this._element).one(g.TRANSITION_END,function(){e.setTransitioning(!1),p(e._element).removeClass(M).addClass(R).trigger("hidden.bs.collapse")}).emulateTransitionEnd(a)}}},e.setTransitioning=function(e){this._isTransitioning=e},e.dispose=function(){p.removeData(this._element,L),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},e._getConfig=function(e){return(e=l({},q,e)).toggle=Boolean(e.toggle),g.typeCheckConfig(O,e,P),e},e._getDimension=function(){return p(this._element).hasClass("width")?"width":"height"},e._getParent=function(){var e,n=this;g.isElement(this._config.parent)?(e=this._config.parent,void 0!==this._config.parent.jquery&&(e=this._config.parent[0])):e=document.querySelector(this._config.parent);var t='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]',i=[].slice.call(e.querySelectorAll(t));return p(i).each(function(e,t){n._addAriaAndCollapsedClass(a._getTargetFromElement(t),[t])}),e},e._addAriaAndCollapsedClass=function(e,t){var n=p(e).hasClass(H);t.length&&p(t).toggleClass(F,!n).attr("aria-expanded",n)},a._getTargetFromElement=function(e){var t=g.getSelectorFromElement(e);return t?document.querySelector(t):null},a._jQueryInterface=function(i){return this.each(function(){var e=p(this),t=e.data(L),n=l({},q,e.data(),"object"==typeof i&&i?i:{});if(!t&&n.toggle&&"string"==typeof i&&/show|hide/.test(i)&&(n.toggle=!1),t||(t=new a(this,n),e.data(L,t)),"string"==typeof i){if(void 0===t[i])throw new TypeError('No method named "'+i+'"');t[i]()}})},s(a,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return q}}]),a}();p(document).on("click.bs.collapse.data-api",B,function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var n=p(this),t=g.getSelectorFromElement(this),i=[].slice.call(document.querySelectorAll(t));p(i).each(function(){var e=p(this),t=e.data(L)?"toggle":n.data();Q._jQueryInterface.call(e,t)})}),p.fn[O]=Q._jQueryInterface,p.fn[O].Constructor=Q,p.fn[O].noConflict=function(){return p.fn[O]=I,Q._jQueryInterface};var W="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,U=function(){for(var e=["Edge","Trident","Firefox"],t=0;t<e.length;t+=1)if(W&&0<=navigator.userAgent.indexOf(e[t]))return 1;return 0}();var $=W&&window.Promise?function(e){var t=!1;return function(){t||(t=!0,window.Promise.resolve().then(function(){t=!1,e()}))}}:function(e){var t=!1;return function(){t||(t=!0,setTimeout(function(){t=!1,e()},U))}};function z(e){return e&&"[object Function]"==={}.toString.call(e)}function V(e,t){if(1!==e.nodeType)return[];var n=e.ownerDocument.defaultView.getComputedStyle(e,null);return t?n[t]:n}function X(e){return"HTML"===e.nodeName?e:e.parentNode||e.host}function Y(e){if(!e)return document.body;switch(e.nodeName){case"HTML":case"BODY":return e.ownerDocument.body;case"#document":return e.body}var t=V(e),n=t.overflow,i=t.overflowX,r=t.overflowY;return/(auto|scroll|overlay)/.test(n+r+i)?e:Y(X(e))}function K(e){return e&&e.referenceNode?e.referenceNode:e}var G=W&&!(!window.MSInputMethodContext||!document.documentMode),J=W&&/MSIE 10/.test(navigator.userAgent);function Z(e){return 11===e?G:10!==e&&G||J}function ee(e){if(!e)return document.documentElement;for(var t=Z(10)?document.body:null,n=e.offsetParent||null;n===t&&e.nextElementSibling;)n=(e=e.nextElementSibling).offsetParent;var i=n&&n.nodeName;return i&&"BODY"!==i&&"HTML"!==i?-1!==["TH","TD","TABLE"].indexOf(n.nodeName)&&"static"===V(n,"position")?ee(n):n:e?e.ownerDocument.documentElement:document.documentElement}function te(e){return null!==e.parentNode?te(e.parentNode):e}function ne(e,t){if(!(e&&e.nodeType&&t&&t.nodeType))return document.documentElement;var n=e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_FOLLOWING,i=n?e:t,r=n?t:e,o=document.createRange();o.setStart(i,0),o.setEnd(r,0);var s,a,l=o.commonAncestorContainer;if(e!==l&&t!==l||i.contains(r))return"BODY"===(a=(s=l).nodeName)||"HTML"!==a&&ee(s.firstElementChild)!==s?ee(l):l;var u=te(e);return u.host?ne(u.host,t):ne(e,te(t).host)}function ie(e,t){var n="top"===(1<arguments.length&&void 0!==t?t:"top")?"scrollTop":"scrollLeft",i=e.nodeName;if("BODY"!==i&&"HTML"!==i)return e[n];var r=e.ownerDocument.documentElement;return(e.ownerDocument.scrollingElement||r)[n]}function re(e,t){var n="x"===t?"Left":"Top",i="Left"==n?"Right":"Bottom";return parseFloat(e["border"+n+"Width"])+parseFloat(e["border"+i+"Width"])}function oe(e,t,n,i){return Math.max(t["offset"+e],t["scroll"+e],n["client"+e],n["offset"+e],n["scroll"+e],Z(10)?parseInt(n["offset"+e])+parseInt(i["margin"+("Height"===e?"Top":"Left")])+parseInt(i["margin"+("Height"===e?"Bottom":"Right")]):0)}function se(e){var t=e.body,n=e.documentElement,i=Z(10)&&getComputedStyle(n);return{height:oe("Height",t,n,i),width:oe("Width",t,n,i)}}var ae=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},le=function(e,t,n){return t&&ue(e.prototype,t),n&&ue(e,n),e};function ue(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function ce(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var fe=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n,i=arguments[t];for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e};function de(e){return fe({},e,{right:e.left+e.width,bottom:e.top+e.height})}function he(e){var t,n,i={};try{Z(10)?(i=e.getBoundingClientRect(),t=ie(e,"top"),n=ie(e,"left"),i.top+=t,i.left+=n,i.bottom+=t,i.right+=n):i=e.getBoundingClientRect()}catch(e){}var r,o={left:i.left,top:i.top,width:i.right-i.left,height:i.bottom-i.top},s="HTML"===e.nodeName?se(e.ownerDocument):{},a=s.width||e.clientWidth||o.width,l=s.height||e.clientHeight||o.height,u=e.offsetWidth-a,c=e.offsetHeight-l;return(u||c)&&(u-=re(r=V(e),"x"),c-=re(r,"y"),o.width-=u,o.height-=c),de(o)}function pe(e,t,n){var i=2<arguments.length&&void 0!==n&&n,r=Z(10),o="HTML"===t.nodeName,s=he(e),a=he(t),l=Y(e),u=V(t),c=parseFloat(u.borderTopWidth),f=parseFloat(u.borderLeftWidth);i&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var d,h,p=de({top:s.top-a.top-c,left:s.left-a.left-f,width:s.width,height:s.height});return p.marginTop=0,p.marginLeft=0,!r&&o&&(d=parseFloat(u.marginTop),h=parseFloat(u.marginLeft),p.top-=c-d,p.bottom-=c-d,p.left-=f-h,p.right-=f-h,p.marginTop=d,p.marginLeft=h),(r&&!i?t.contains(l):t===l&&"BODY"!==l.nodeName)&&(p=function(e,t,n){var i=2<arguments.length&&void 0!==n&&n,r=ie(t,"top"),o=ie(t,"left"),s=i?-1:1;return e.top+=r*s,e.bottom+=r*s,e.left+=o*s,e.right+=o*s,e}(p,t)),p}function ge(e){if(!e||!e.parentElement||Z())return document.documentElement;for(var t=e.parentElement;t&&"none"===V(t,"transform");)t=t.parentElement;return t||document.documentElement}function me(e,t,n,i,r){var o,s,a,l,u,c=4<arguments.length&&void 0!==r&&r,f={top:0,left:0},d=c?ge(e):ne(e,K(t));"viewport"===i?f=function(e,t){var n=1<arguments.length&&void 0!==t&&t,i=e.ownerDocument.documentElement,r=pe(e,i),o=Math.max(i.clientWidth,window.innerWidth||0),s=Math.max(i.clientHeight,window.innerHeight||0),a=n?0:ie(i),l=n?0:ie(i,"left");return de({top:a-r.top+r.marginTop,left:l-r.left+r.marginLeft,width:o,height:s})}(d,c):(o=void 0,"scrollParent"===i?"BODY"===(o=Y(X(t))).nodeName&&(o=e.ownerDocument.documentElement):o="window"===i?e.ownerDocument.documentElement:i,s=pe(o,d,c),"HTML"!==o.nodeName||function e(t){var n=t.nodeName;if("BODY"===n||"HTML"===n)return!1;if("fixed"===V(t,"position"))return!0;var i=X(t);return!!i&&e(i)}(d)?f=s:(l=(a=se(e.ownerDocument)).height,u=a.width,f.top+=s.top-s.marginTop,f.bottom=l+s.top,f.left+=s.left-s.marginLeft,f.right=u+s.left));var h="number"==typeof(n=n||0);return f.left+=h?n:n.left||0,f.top+=h?n:n.top||0,f.right-=h?n:n.right||0,f.bottom-=h?n:n.bottom||0,f}function ve(e,t,i,n,r,o){var s=5<arguments.length&&void 0!==o?o:0;if(-1===e.indexOf("auto"))return e;var a=me(i,n,s,r),l={top:{width:a.width,height:t.top-a.top},right:{width:a.right-t.right,height:a.height},bottom:{width:a.width,height:a.bottom-t.bottom},left:{width:t.left-a.left,height:a.height}},u=Object.keys(l).map(function(e){return fe({key:e},l[e],{area:(t=l[e]).width*t.height});var t}).sort(function(e,t){return t.area-e.area}),c=u.filter(function(e){var t=e.width,n=e.height;return t>=i.clientWidth&&n>=i.clientHeight}),f=0<c.length?c[0].key:u[0].key,d=e.split("-")[1];return f+(d?"-"+d:"")}function ye(e,t,n,i){var r=3<arguments.length&&void 0!==i?i:null;return pe(n,r?ge(t):ne(t,K(n)),r)}function be(e){var t=e.ownerDocument.defaultView.getComputedStyle(e),n=parseFloat(t.marginTop||0)+parseFloat(t.marginBottom||0),i=parseFloat(t.marginLeft||0)+parseFloat(t.marginRight||0);return{width:e.offsetWidth+i,height:e.offsetHeight+n}}function _e(e){var t={left:"right",right:"left",bottom:"top",top:"bottom"};return e.replace(/left|right|bottom|top/g,function(e){return t[e]})}function we(e,t,n){n=n.split("-")[0];var i=be(e),r={width:i.width,height:i.height},o=-1!==["right","left"].indexOf(n),s=o?"top":"left",a=o?"left":"top",l=o?"height":"width",u=o?"width":"height";return r[s]=t[s]+t[l]/2-i[l]/2,r[a]=n===a?t[a]-i[u]:t[_e(a)],r}function xe(e,t){return Array.prototype.find?e.find(t):e.filter(t)[0]}function Ee(e,n,t){return(void 0===t?e:e.slice(0,function(e,t,n){if(Array.prototype.findIndex)return e.findIndex(function(e){return e[t]===n});var i=xe(e,function(e){return e[t]===n});return e.indexOf(i)}(e,"name",t))).forEach(function(e){e.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var t=e.function||e.fn;e.enabled&&z(t)&&(n.offsets.popper=de(n.offsets.popper),n.offsets.reference=de(n.offsets.reference),n=t(n,e))}),n}function Te(e,n){return e.some(function(e){var t=e.name;return e.enabled&&t===n})}function Ce(e){for(var t=[!1,"ms","Webkit","Moz","O"],n=e.charAt(0).toUpperCase()+e.slice(1),i=0;i<t.length;i++){var r=t[i],o=r?""+r+n:e;if(void 0!==document.body.style[o])return o}return null}function Se(e){var t=e.ownerDocument;return t?t.defaultView:window}function Ae(e,t,n,i){n.updateBound=i,Se(e).addEventListener("resize",n.updateBound,{passive:!0});var r=Y(e);return function e(t,n,i,r){var o="BODY"===t.nodeName,s=o?t.ownerDocument.defaultView:t;s.addEventListener(n,i,{passive:!0}),o||e(Y(s.parentNode),n,i,r),r.push(s)}(r,"scroll",n.updateBound,n.scrollParents),n.scrollElement=r,n.eventsEnabled=!0,n}function Ne(){var e,t;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(e=this.reference,t=this.state,Se(e).removeEventListener("resize",t.updateBound),t.scrollParents.forEach(function(e){e.removeEventListener("scroll",t.updateBound)}),t.updateBound=null,t.scrollParents=[],t.scrollElement=null,t.eventsEnabled=!1,t))}function ke(e){return""!==e&&!isNaN(parseFloat(e))&&isFinite(e)}function De(n,i){Object.keys(i).forEach(function(e){var t="";-1!==["width","height","top","right","bottom","left"].indexOf(e)&&ke(i[e])&&(t="px"),n.style[e]=i[e]+t})}function je(e,t){function n(e){return e}var i=e.offsets,r=i.popper,o=i.reference,s=Math.round,a=Math.floor,l=s(o.width),u=s(r.width),c=-1!==["left","right"].indexOf(e.placement),f=-1!==e.placement.indexOf("-"),d=t?c||f||l%2==u%2?s:a:n,h=t?s:n;return{left:d(l%2==1&&u%2==1&&!f&&t?r.left-1:r.left),top:h(r.top),bottom:h(r.bottom),right:d(r.right)}}var Oe=W&&/Firefox/i.test(navigator.userAgent);function Le(e,t,n){var i,r,o=xe(e,function(e){return e.name===t}),s=!!o&&e.some(function(e){return e.name===n&&e.enabled&&e.order<o.order});return s||(i="`"+t+"`",r="`"+n+"`",console.warn(r+" modifier is required by "+i+" modifier in order to work, be sure to include it before "+i+"!")),s}var Ie=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],qe=Ie.slice(3);function Pe(e,t){var n=1<arguments.length&&void 0!==t&&t,i=qe.indexOf(e),r=qe.slice(i+1).concat(qe.slice(0,i));return n?r.reverse():r}var He="flip",Re="clockwise",Me="counterclockwise";function Fe(e,r,o,t){var s=[0,0],a=-1!==["right","left"].indexOf(t),n=e.split(/(\+|\-)/).map(function(e){return e.trim()}),i=n.indexOf(xe(n,function(e){return-1!==e.search(/,|\s/)}));n[i]&&-1===n[i].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/;return(-1!==i?[n.slice(0,i).concat([n[i].split(l)[0]]),[n[i].split(l)[1]].concat(n.slice(i+1))]:[n]).map(function(e,t){var n=(1===t?!a:a)?"height":"width",i=!1;return e.reduce(function(e,t){return""===e[e.length-1]&&-1!==["+","-"].indexOf(t)?(e[e.length-1]=t,i=!0,e):i?(e[e.length-1]+=t,i=!1,e):e.concat(t)},[]).map(function(e){return function(e,t,n,i){var r=e.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),o=+r[1],s=r[2];if(!o)return e;if(0!==s.indexOf("%"))return"vh"!==s&&"vw"!==s?o:("vh"===s?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*o;var a=void 0;switch(s){case"%p":a=n;break;case"%":case"%r":default:a=i}return de(a)[t]/100*o}(e,n,r,o)})}).forEach(function(n,i){n.forEach(function(e,t){ke(e)&&(s[i]+=e*("-"===n[t-1]?-1:1))})}),s}var Be={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(e){var t,n,i,r,o,s,a,l=e.placement,u=l.split("-")[0],c=l.split("-")[1];return c&&(n=(t=e.offsets).reference,i=t.popper,s=(r=-1!==["bottom","top"].indexOf(u))?"width":"height",a={start:ce({},o=r?"left":"top",n[o]),end:ce({},o,n[o]+n[s]-i[s])},e.offsets.popper=fe({},i,a[c])),e}},offset:{order:200,enabled:!0,fn:function(e,t){var n=t.offset,i=e.placement,r=e.offsets,o=r.popper,s=r.reference,a=i.split("-")[0],l=void 0,l=ke(+n)?[+n,0]:Fe(n,o,s,a);return"left"===a?(o.top+=l[0],o.left-=l[1]):"right"===a?(o.top+=l[0],o.left+=l[1]):"top"===a?(o.left+=l[0],o.top-=l[1]):"bottom"===a&&(o.left+=l[0],o.top+=l[1]),e.popper=o,e},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(e,i){var t=i.boundariesElement||ee(e.instance.popper);e.instance.reference===t&&(t=ee(t));var n=Ce("transform"),r=e.instance.popper.style,o=r.top,s=r.left,a=r[n];r.top="",r.left="",r[n]="";var l=me(e.instance.popper,e.instance.reference,i.padding,t,e.positionFixed);r.top=o,r.left=s,r[n]=a,i.boundaries=l;var u=i.priority,c=e.offsets.popper,f={primary:function(e){var t=c[e];return c[e]<l[e]&&!i.escapeWithReference&&(t=Math.max(c[e],l[e])),ce({},e,t)},secondary:function(e){var t="right"===e?"left":"top",n=c[t];return c[e]>l[e]&&!i.escapeWithReference&&(n=Math.min(c[t],l[e]-("right"===e?c.width:c.height))),ce({},t,n)}};return u.forEach(function(e){var t=-1!==["left","top"].indexOf(e)?"primary":"secondary";c=fe({},c,f[t](e))}),e.offsets.popper=c,e},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(e){var t=e.offsets,n=t.popper,i=t.reference,r=e.placement.split("-")[0],o=Math.floor,s=-1!==["top","bottom"].indexOf(r),a=s?"right":"bottom",l=s?"left":"top",u=s?"width":"height";return n[a]<o(i[l])&&(e.offsets.popper[l]=o(i[l])-n[u]),n[l]>o(i[a])&&(e.offsets.popper[l]=o(i[a])),e}},arrow:{order:500,enabled:!0,fn:function(e,t){var n;if(!Le(e.instance.modifiers,"arrow","keepTogether"))return e;var i=t.element;if("string"==typeof i){if(!(i=e.instance.popper.querySelector(i)))return e}else if(!e.instance.popper.contains(i))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),e;var r=e.placement.split("-")[0],o=e.offsets,s=o.popper,a=o.reference,l=-1!==["left","right"].indexOf(r),u=l?"height":"width",c=l?"Top":"Left",f=c.toLowerCase(),d=l?"left":"top",h=l?"bottom":"right",p=be(i)[u];a[h]-p<s[f]&&(e.offsets.popper[f]-=s[f]-(a[h]-p)),a[f]+p>s[h]&&(e.offsets.popper[f]+=a[f]+p-s[h]),e.offsets.popper=de(e.offsets.popper);var g=a[f]+a[u]/2-p/2,m=V(e.instance.popper),v=parseFloat(m["margin"+c]),y=parseFloat(m["border"+c+"Width"]),b=g-e.offsets.popper[f]-v-y,b=Math.max(Math.min(s[u]-p,b),0);return e.arrowElement=i,e.offsets.arrow=(ce(n={},f,Math.round(b)),ce(n,d,""),n),e},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(m,v){if(Te(m.instance.modifiers,"inner"))return m;if(m.flipped&&m.placement===m.originalPlacement)return m;var y=me(m.instance.popper,m.instance.reference,v.padding,v.boundariesElement,m.positionFixed),b=m.placement.split("-")[0],_=_e(b),w=m.placement.split("-")[1]||"",x=[];switch(v.behavior){case He:x=[b,_];break;case Re:x=Pe(b);break;case Me:x=Pe(b,!0);break;default:x=v.behavior}return x.forEach(function(e,t){if(b!==e||x.length===t+1)return m;b=m.placement.split("-")[0],_=_e(b);var n,i=m.offsets.popper,r=m.offsets.reference,o=Math.floor,s="left"===b&&o(i.right)>o(r.left)||"right"===b&&o(i.left)<o(r.right)||"top"===b&&o(i.bottom)>o(r.top)||"bottom"===b&&o(i.top)<o(r.bottom),a=o(i.left)<o(y.left),l=o(i.right)>o(y.right),u=o(i.top)<o(y.top),c=o(i.bottom)>o(y.bottom),f="left"===b&&a||"right"===b&&l||"top"===b&&u||"bottom"===b&&c,d=-1!==["top","bottom"].indexOf(b),h=!!v.flipVariations&&(d&&"start"===w&&a||d&&"end"===w&&l||!d&&"start"===w&&u||!d&&"end"===w&&c),p=!!v.flipVariationsByContent&&(d&&"start"===w&&l||d&&"end"===w&&a||!d&&"start"===w&&c||!d&&"end"===w&&u),g=h||p;(s||f||g)&&(m.flipped=!0,(s||f)&&(b=x[t+1]),g&&(w="end"===(n=w)?"start":"start"===n?"end":n),m.placement=b+(w?"-"+w:""),m.offsets.popper=fe({},m.offsets.popper,we(m.instance.popper,m.offsets.reference,m.placement)),m=Ee(m.instance.modifiers,m,"flip"))}),m},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(e){var t=e.placement,n=t.split("-")[0],i=e.offsets,r=i.popper,o=i.reference,s=-1!==["left","right"].indexOf(n),a=-1===["top","left"].indexOf(n);return r[s?"left":"top"]=o[n]-(a?r[s?"width":"height"]:0),e.placement=_e(t),e.offsets.popper=de(r),e}},hide:{order:800,enabled:!0,fn:function(e){if(!Le(e.instance.modifiers,"hide","preventOverflow"))return e;var t=e.offsets.reference,n=xe(e.instance.modifiers,function(e){return"preventOverflow"===e.name}).boundaries;if(t.bottom<n.top||t.left>n.right||t.top>n.bottom||t.right<n.left){if(!0===e.hide)return e;e.hide=!0,e.attributes["x-out-of-boundaries"]=""}else{if(!1===e.hide)return e;e.hide=!1,e.attributes["x-out-of-boundaries"]=!1}return e}},computeStyle:{order:850,enabled:!0,fn:function(e,t){var n=t.x,i=t.y,r=e.offsets.popper,o=xe(e.instance.modifiers,function(e){return"applyStyle"===e.name}).gpuAcceleration;void 0!==o&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var s,a,l=void 0!==o?o:t.gpuAcceleration,u=ee(e.instance.popper),c=he(u),f={position:r.position},d=je(e,window.devicePixelRatio<2||!Oe),h="bottom"===n?"top":"bottom",p="right"===i?"left":"right",g=Ce("transform"),m=void 0,v=void 0,v="bottom"==h?"HTML"===u.nodeName?-u.clientHeight+d.bottom:-c.height+d.bottom:d.top,m="right"==p?"HTML"===u.nodeName?-u.clientWidth+d.right:-c.width+d.right:d.left;l&&g?(f[g]="translate3d("+m+"px, "+v+"px, 0)",f[h]=0,f[p]=0,f.willChange="transform"):(s="bottom"==h?-1:1,a="right"==p?-1:1,f[h]=v*s,f[p]=m*a,f.willChange=h+", "+p);var y={"x-placement":e.placement};return e.attributes=fe({},y,e.attributes),e.styles=fe({},f,e.styles),e.arrowStyles=fe({},e.offsets.arrow,e.arrowStyles),e},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(e){var t,n;return De(e.instance.popper,e.styles),t=e.instance.popper,n=e.attributes,Object.keys(n).forEach(function(e){!1!==n[e]?t.setAttribute(e,n[e]):t.removeAttribute(e)}),e.arrowElement&&Object.keys(e.arrowStyles).length&&De(e.arrowElement,e.arrowStyles),e},onLoad:function(e,t,n,i,r){var o=ye(r,t,e,n.positionFixed),s=ve(n.placement,o,t,e,n.modifiers.flip.boundariesElement,n.modifiers.flip.padding);return t.setAttribute("x-placement",s),De(t,{position:n.positionFixed?"fixed":"absolute"}),n},gpuAcceleration:void 0}}},Qe=(le(We,[{key:"update",value:function(){return function(){var e;this.state.isDestroyed||((e={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}}).offsets.reference=ye(this.state,this.popper,this.reference,this.options.positionFixed),e.placement=ve(this.options.placement,e.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),e.originalPlacement=e.placement,e.positionFixed=this.options.positionFixed,e.offsets.popper=we(this.popper,e.offsets.reference,e.placement),e.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",e=Ee(this.modifiers,e),this.state.isCreated?this.options.onUpdate(e):(this.state.isCreated=!0,this.options.onCreate(e)))}.call(this)}},{key:"destroy",value:function(){return function(){return this.state.isDestroyed=!0,Te(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[Ce("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}.call(this)}},{key:"enableEventListeners",value:function(){return function(){this.state.eventsEnabled||(this.state=Ae(this.reference,this.options,this.state,this.scheduleUpdate))}.call(this)}},{key:"disableEventListeners",value:function(){return Ne.call(this)}}]),We);function We(e,t){var n=this,i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};ae(this,We),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=$(this.update.bind(this)),this.options=fe({},We.Defaults,i),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=t&&t.jquery?t[0]:t,this.options.modifiers={},Object.keys(fe({},We.Defaults.modifiers,i.modifiers)).forEach(function(e){n.options.modifiers[e]=fe({},We.Defaults.modifiers[e]||{},i.modifiers?i.modifiers[e]:{})}),this.modifiers=Object.keys(this.options.modifiers).map(function(e){return fe({name:e},n.options.modifiers[e])}).sort(function(e,t){return e.order-t.order}),this.modifiers.forEach(function(e){e.enabled&&z(e.onLoad)&&e.onLoad(n.reference,n.popper,n.options,e,n.state)}),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}Qe.Utils=("undefined"!=typeof window?window:global).PopperUtils,Qe.placements=Ie,Qe.Defaults=Be;var Ue="dropdown",$e="bs.dropdown",ze="."+$e,Ve=".data-api",Xe=p.fn[Ue],Ye=new RegExp("38|40|27"),Ke="hide"+ze,Ge="hidden"+ze,Je="click"+ze+Ve,Ze="keydown"+ze+Ve,et="disabled",tt="show",nt="dropdown-menu-right",it='[data-toggle="dropdown"]',rt=".dropdown-menu",ot={offset:0,flip:!0,boundary:"scrollParent",reference:"toggle",display:"dynamic",popperConfig:null},st={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)",reference:"(string|element)",display:"string",popperConfig:"(null|object)"},at=function(){function u(e,t){this._element=e,this._popper=null,this._config=this._getConfig(t),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var e=u.prototype;return e.toggle=function(){var e;this._element.disabled||p(this._element).hasClass(et)||(e=p(this._menu).hasClass(tt),u._clearMenus(),e||this.show(!0))},e.show=function(e){if(void 0===e&&(e=!1),!(this._element.disabled||p(this._element).hasClass(et)||p(this._menu).hasClass(tt))){var t={relatedTarget:this._element},n=p.Event("show.bs.dropdown",t),i=u._getParentFromElement(this._element);if(p(i).trigger(n),!n.isDefaultPrevented()){if(!this._inNavbar&&e){if(void 0===Qe)throw new TypeError("Bootstrap's dropdowns require Popper.js (https://popper.js.org/)");var r=this._element;"parent"===this._config.reference?r=i:g.isElement(this._config.reference)&&(r=this._config.reference,void 0!==this._config.reference.jquery&&(r=this._config.reference[0])),"scrollParent"!==this._config.boundary&&p(i).addClass("position-static"),this._popper=new Qe(r,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===p(i).closest(".navbar-nav").length&&p(document.body).children().on("mouseover",null,p.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),p(this._menu).toggleClass(tt),p(i).toggleClass(tt).trigger(p.Event("shown.bs.dropdown",t))}}},e.hide=function(){var e,t,n;this._element.disabled||p(this._element).hasClass(et)||!p(this._menu).hasClass(tt)||(e={relatedTarget:this._element},t=p.Event(Ke,e),n=u._getParentFromElement(this._element),p(n).trigger(t),t.isDefaultPrevented()||(this._popper&&this._popper.destroy(),p(this._menu).toggleClass(tt),p(n).toggleClass(tt).trigger(p.Event(Ge,e))))},e.dispose=function(){p.removeData(this._element,$e),p(this._element).off(ze),this._element=null,(this._menu=null)!==this._popper&&(this._popper.destroy(),this._popper=null)},e.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},e._addEventListeners=function(){var t=this;p(this._element).on("click.bs.dropdown",function(e){e.preventDefault(),e.stopPropagation(),t.toggle()})},e._getConfig=function(e){return e=l({},this.constructor.Default,p(this._element).data(),e),g.typeCheckConfig(Ue,e,this.constructor.DefaultType),e},e._getMenuElement=function(){var e;return this._menu||(e=u._getParentFromElement(this._element))&&(this._menu=e.querySelector(rt)),this._menu},e._getPlacement=function(){var e=p(this._element.parentNode),t="bottom-start";return e.hasClass("dropup")?t=p(this._menu).hasClass(nt)?"top-end":"top-start":e.hasClass("dropright")?t="right-start":e.hasClass("dropleft")?t="left-start":p(this._menu).hasClass(nt)&&(t="bottom-end"),t},e._detectNavbar=function(){return 0<p(this._element).closest(".navbar").length},e._getOffset=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=l({},e.offsets,t._config.offset(e.offsets,t._element)||{}),e}:e.offset=this._config.offset,e},e._getPopperConfig=function(){var e={placement:this._getPlacement(),modifiers:{offset:this._getOffset(),flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}};return"static"===this._config.display&&(e.modifiers.applyStyle={enabled:!1}),l({},e,this._config.popperConfig)},u._jQueryInterface=function(t){return this.each(function(){var e=p(this).data($e);if(e||(e=new u(this,"object"==typeof t?t:null),p(this).data($e,e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},u._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var t=[].slice.call(document.querySelectorAll(it)),n=0,i=t.length;n<i;n++){var r,o,s=u._getParentFromElement(t[n]),a=p(t[n]).data($e),l={relatedTarget:t[n]};e&&"click"===e.type&&(l.clickEvent=e),a&&(r=a._menu,p(s).hasClass(tt)&&(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&p.contains(s,e.target)||(o=p.Event(Ke,l),p(s).trigger(o),o.isDefaultPrevented()||("ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),t[n].setAttribute("aria-expanded","false"),a._popper&&a._popper.destroy(),p(r).removeClass(tt),p(s).removeClass(tt).trigger(p.Event(Ge,l))))))}},u._getParentFromElement=function(e){var t,n=g.getSelectorFromElement(e);return n&&(t=document.querySelector(n)),t||e.parentNode},u._dataApiKeydownHandler=function(e){if((/input|textarea/i.test(e.target.tagName)?!(32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||p(e.target).closest(rt).length)):Ye.test(e.which))&&!this.disabled&&!p(this).hasClass(et)){var t=u._getParentFromElement(this),n=p(t).hasClass(tt);if(n||27!==e.which){if(e.preventDefault(),e.stopPropagation(),!n||n&&(27===e.which||32===e.which))return 27===e.which&&p(t.querySelector(it)).trigger("focus"),void p(this).trigger("click");var i,r=[].slice.call(t.querySelectorAll(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)")).filter(function(e){return p(e).is(":visible")});0!==r.length&&(i=r.indexOf(e.target),38===e.which&&0<i&&i--,40===e.which&&i<r.length-1&&i++,i<0&&(i=0),r[i].focus())}}},s(u,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return ot}},{key:"DefaultType",get:function(){return st}}]),u}();p(document).on(Ze,it,at._dataApiKeydownHandler).on(Ze,rt,at._dataApiKeydownHandler).on(Je+" keyup.bs.dropdown.data-api",at._clearMenus).on(Je,it,function(e){e.preventDefault(),e.stopPropagation(),at._jQueryInterface.call(p(this),"toggle")}).on(Je,".dropdown form",function(e){e.stopPropagation()}),p.fn[Ue]=at._jQueryInterface,p.fn[Ue].Constructor=at,p.fn[Ue].noConflict=function(){return p.fn[Ue]=Xe,at._jQueryInterface};var lt="modal",ut="bs.modal",ct="."+ut,ft=p.fn[lt],dt={backdrop:!0,keyboard:!0,focus:!0,show:!0},ht={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},pt="hidden"+ct,gt="show"+ct,mt="focusin"+ct,vt="resize"+ct,yt="click.dismiss"+ct,bt="keydown.dismiss"+ct,_t="mousedown.dismiss"+ct,wt="modal-open",xt="fade",Et="show",Tt="modal-static",Ct=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",St=".sticky-top",At=function(){function r(e,t){this._config=this._getConfig(t),this._element=e,this._dialog=e.querySelector(".modal-dialog"),this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._isTransitioning=!1,this._scrollbarWidth=0}var e=r.prototype;return e.toggle=function(e){return this._isShown?this.hide():this.show(e)},e.show=function(e){var t,n=this;this._isShown||this._isTransitioning||(p(this._element).hasClass(xt)&&(this._isTransitioning=!0),t=p.Event(gt,{relatedTarget:e}),p(this._element).trigger(t),this._isShown||t.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),this._setEscapeEvent(),this._setResizeEvent(),p(this._element).on(yt,'[data-dismiss="modal"]',function(e){return n.hide(e)}),p(this._dialog).on(_t,function(){p(n._element).one("mouseup.dismiss.bs.modal",function(e){p(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)})))},e.hide=function(e){var t,n,i,r=this;e&&e.preventDefault(),this._isShown&&!this._isTransitioning&&(t=p.Event("hide.bs.modal"),p(this._element).trigger(t),this._isShown&&!t.isDefaultPrevented()&&(this._isShown=!1,(n=p(this._element).hasClass(xt))&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),p(document).off(mt),p(this._element).removeClass(Et),p(this._element).off(yt),p(this._dialog).off(_t),n?(i=g.getTransitionDurationFromElement(this._element),p(this._element).one(g.TRANSITION_END,function(e){return r._hideModal(e)}).emulateTransitionEnd(i)):this._hideModal()))},e.dispose=function(){[window,this._element,this._dialog].forEach(function(e){return p(e).off(ct)}),p(document).off(mt),p.removeData(this._element,ut),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._isTransitioning=null,this._scrollbarWidth=null},e.handleUpdate=function(){this._adjustDialog()},e._getConfig=function(e){return e=l({},dt,e),g.typeCheckConfig(lt,e,ht),e},e._triggerBackdropTransition=function(){var e=this;if("static"===this._config.backdrop){var t=p.Event("hidePrevented.bs.modal");if(p(this._element).trigger(t),t.defaultPrevented)return;var n=this._element.scrollHeight>document.documentElement.clientHeight;n||(this._element.style.overflowY="hidden"),this._element.classList.add(Tt);var i=g.getTransitionDurationFromElement(this._dialog);p(this._element).off(g.TRANSITION_END),p(this._element).one(g.TRANSITION_END,function(){e._element.classList.remove(Tt),n||p(e._element).one(g.TRANSITION_END,function(){e._element.style.overflowY=""}).emulateTransitionEnd(e._element,i)}).emulateTransitionEnd(i),this._element.focus()}else this.hide()},e._showElement=function(e){var t=this,n=p(this._element).hasClass(xt),i=this._dialog?this._dialog.querySelector(".modal-body"):null;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),p(this._dialog).hasClass("modal-dialog-scrollable")&&i?i.scrollTop=0:this._element.scrollTop=0,n&&g.reflow(this._element),p(this._element).addClass(Et),this._config.focus&&this._enforceFocus();function r(){t._config.focus&&t._element.focus(),t._isTransitioning=!1,p(t._element).trigger(s)}var o,s=p.Event("shown.bs.modal",{relatedTarget:e});n?(o=g.getTransitionDurationFromElement(this._dialog),p(this._dialog).one(g.TRANSITION_END,r).emulateTransitionEnd(o)):r()},e._enforceFocus=function(){var t=this;p(document).off(mt).on(mt,function(e){document!==e.target&&t._element!==e.target&&0===p(t._element).has(e.target).length&&t._element.focus()})},e._setEscapeEvent=function(){var t=this;this._isShown?p(this._element).on(bt,function(e){t._config.keyboard&&27===e.which?(e.preventDefault(),t.hide()):t._config.keyboard||27!==e.which||t._triggerBackdropTransition()}):this._isShown||p(this._element).off(bt)},e._setResizeEvent=function(){var t=this;this._isShown?p(window).on(vt,function(e){return t.handleUpdate(e)}):p(window).off(vt)},e._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._showBackdrop(function(){p(document.body).removeClass(wt),e._resetAdjustments(),e._resetScrollbar(),p(e._element).trigger(pt)})},e._removeBackdrop=function(){this._backdrop&&(p(this._backdrop).remove(),this._backdrop=null)},e._showBackdrop=function(e){var t,n,i=this,r=p(this._element).hasClass(xt)?xt:"";if(this._isShown&&this._config.backdrop){if(this._backdrop=document.createElement("div"),this._backdrop.className="modal-backdrop",r&&this._backdrop.classList.add(r),p(this._backdrop).appendTo(document.body),p(this._element).on(yt,function(e){i._ignoreBackdropClick?i._ignoreBackdropClick=!1:e.target===e.currentTarget&&i._triggerBackdropTransition()}),r&&g.reflow(this._backdrop),p(this._backdrop).addClass(Et),!e)return;if(!r)return void e();var o=g.getTransitionDurationFromElement(this._backdrop);p(this._backdrop).one(g.TRANSITION_END,e).emulateTransitionEnd(o)}else{!this._isShown&&this._backdrop?(p(this._backdrop).removeClass(Et),t=function(){i._removeBackdrop(),e&&e()},p(this._element).hasClass(xt)?(n=g.getTransitionDurationFromElement(this._backdrop),p(this._backdrop).one(g.TRANSITION_END,t).emulateTransitionEnd(n)):t()):e&&e()}},e._adjustDialog=function(){var e=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&e&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!e&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},e._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},e._checkScrollbar=function(){var e=document.body.getBoundingClientRect();this._isBodyOverflowing=Math.round(e.left+e.right)<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},e._setScrollbar=function(){var e,t,n,i,r=this;this._isBodyOverflowing&&(e=[].slice.call(document.querySelectorAll(Ct)),t=[].slice.call(document.querySelectorAll(St)),p(e).each(function(e,t){var n=t.style.paddingRight,i=p(t).css("padding-right");p(t).data("padding-right",n).css("padding-right",parseFloat(i)+r._scrollbarWidth+"px")}),p(t).each(function(e,t){var n=t.style.marginRight,i=p(t).css("margin-right");p(t).data("margin-right",n).css("margin-right",parseFloat(i)-r._scrollbarWidth+"px")}),n=document.body.style.paddingRight,i=p(document.body).css("padding-right"),p(document.body).data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")),p(document.body).addClass(wt)},e._resetScrollbar=function(){var e=[].slice.call(document.querySelectorAll(Ct));p(e).each(function(e,t){var n=p(t).data("padding-right");p(t).removeData("padding-right"),t.style.paddingRight=n||""});var t=[].slice.call(document.querySelectorAll(St));p(t).each(function(e,t){var n=p(t).data("margin-right");void 0!==n&&p(t).css("margin-right",n).removeData("margin-right")});var n=p(document.body).data("padding-right");p(document.body).removeData("padding-right"),document.body.style.paddingRight=n||""},e._getScrollbarWidth=function(){var e=document.createElement("div");e.className="modal-scrollbar-measure",document.body.appendChild(e);var t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t},r._jQueryInterface=function(n,i){return this.each(function(){var e=p(this).data(ut),t=l({},dt,p(this).data(),"object"==typeof n&&n?n:{});if(e||(e=new r(this,t),p(this).data(ut,e)),"string"==typeof n){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n](i)}else t.show&&e.show(i)})},s(r,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return dt}}]),r}();p(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(e){var t,n=this,i=g.getSelectorFromElement(this);i&&(t=document.querySelector(i));var r=p(t).data(ut)?"toggle":l({},p(t).data(),p(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var o=p(t).one(gt,function(e){e.isDefaultPrevented()||o.one(pt,function(){p(n).is(":visible")&&n.focus()})});At._jQueryInterface.call(p(t),r,this)}),p.fn[lt]=At._jQueryInterface,p.fn[lt].Constructor=At,p.fn[lt].noConflict=function(){return p.fn[lt]=ft,At._jQueryInterface};var Nt=["background","cite","href","itemtype","longdesc","poster","src","xlink:href"],kt={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Dt=/^(?:(?:https?|mailto|ftp|tel|file):|[^#&/:?]*(?:[#/?]|$))/gi,jt=/^data:(?:image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp)|video\/(?:mpeg|mp4|ogg|webm)|audio\/(?:mp3|oga|ogg|opus));base64,[\d+/a-z]+=*$/i;function Ot(e,o,t){if(0===e.length)return e;if(t&&"function"==typeof t)return t(e);for(var n=(new window.DOMParser).parseFromString(e,"text/html"),s=Object.keys(o),a=[].slice.call(n.body.querySelectorAll("*")),i=function(e){var t=a[e],n=t.nodeName.toLowerCase();if(-1===s.indexOf(t.nodeName.toLowerCase()))return t.parentNode.removeChild(t),"continue";var i=[].slice.call(t.attributes),r=[].concat(o["*"]||[],o[n]||[]);i.forEach(function(e){!function(e,t){var n=e.nodeName.toLowerCase();if(-1!==t.indexOf(n))return-1===Nt.indexOf(n)||Boolean(e.nodeValue.match(Dt)||e.nodeValue.match(jt));for(var i=t.filter(function(e){return e instanceof RegExp}),r=0,o=i.length;r<o;r++)if(n.match(i[r]))return 1}(e,r)&&t.removeAttribute(e.nodeName)})},r=0,l=a.length;r<l;r++)i(r);return n.body.innerHTML}var Lt="tooltip",It="bs.tooltip",qt="."+It,Pt=p.fn[Lt],Ht="bs-tooltip",Rt=new RegExp("(^|\\s)"+Ht+"\\S+","g"),Mt=["sanitize","whiteList","sanitizeFn"],Ft={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string|function)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)",sanitize:"boolean",sanitizeFn:"(null|function)",whiteList:"object",popperConfig:"(null|object)"},Bt={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},Qt={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent",sanitize:!0,sanitizeFn:null,whiteList:kt,popperConfig:null},Wt="show",Ut={HIDE:"hide"+qt,HIDDEN:"hidden"+qt,SHOW:"show"+qt,SHOWN:"shown"+qt,INSERTED:"inserted"+qt,CLICK:"click"+qt,FOCUSIN:"focusin"+qt,FOCUSOUT:"focusout"+qt,MOUSEENTER:"mouseenter"+qt,MOUSELEAVE:"mouseleave"+qt},$t="fade",zt="show",Vt="hover",Xt="focus",Yt=function(){function i(e,t){if(void 0===Qe)throw new TypeError("Bootstrap's tooltips require Popper.js (https://popper.js.org/)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=e,this.config=this._getConfig(t),this.tip=null,this._setListeners()}var e=i.prototype;return e.enable=function(){this._isEnabled=!0},e.disable=function(){this._isEnabled=!1},e.toggleEnabled=function(){this._isEnabled=!this._isEnabled},e.toggle=function(e){if(this._isEnabled)if(e){var t=this.constructor.DATA_KEY,n=p(e.currentTarget).data(t);n||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),p(e.currentTarget).data(t,n)),n._activeTrigger.click=!n._activeTrigger.click,n._isWithActiveTrigger()?n._enter(null,n):n._leave(null,n)}else{if(p(this.getTipElement()).hasClass(zt))return void this._leave(null,this);this._enter(null,this)}},e.dispose=function(){clearTimeout(this._timeout),p.removeData(this.element,this.constructor.DATA_KEY),p(this.element).off(this.constructor.EVENT_KEY),p(this.element).closest(".modal").off("hide.bs.modal",this._hideModalHandler),this.tip&&p(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},e.show=function(){var t=this;if("none"===p(this.element).css("display"))throw new Error("Please use show on visible elements");var e=p.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){p(this.element).trigger(e);var n=g.findShadowRoot(this.element),i=p.contains(null!==n?n:this.element.ownerDocument.documentElement,this.element);if(e.isDefaultPrevented()||!i)return;var r=this.getTipElement(),o=g.getUID(this.constructor.NAME);r.setAttribute("id",o),this.element.setAttribute("aria-describedby",o),this.setContent(),this.config.animation&&p(r).addClass($t);var s="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,a=this._getAttachment(s);this.addAttachmentClass(a);var l=this._getContainer();p(r).data(this.constructor.DATA_KEY,this),p.contains(this.element.ownerDocument.documentElement,this.tip)||p(r).appendTo(l),p(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new Qe(this.element,r,this._getPopperConfig(a)),p(r).addClass(zt),"ontouchstart"in document.documentElement&&p(document.body).children().on("mouseover",null,p.noop);var u,c=function(){t.config.animation&&t._fixTransition();var e=t._hoverState;t._hoverState=null,p(t.element).trigger(t.constructor.Event.SHOWN),"out"===e&&t._leave(null,t)};p(this.tip).hasClass($t)?(u=g.getTransitionDurationFromElement(this.tip),p(this.tip).one(g.TRANSITION_END,c).emulateTransitionEnd(u)):c()}},e.hide=function(e){function t(){i._hoverState!==Wt&&r.parentNode&&r.parentNode.removeChild(r),i._cleanTipClass(),i.element.removeAttribute("aria-describedby"),p(i.element).trigger(i.constructor.Event.HIDDEN),null!==i._popper&&i._popper.destroy(),e&&e()}var n,i=this,r=this.getTipElement(),o=p.Event(this.constructor.Event.HIDE);p(this.element).trigger(o),o.isDefaultPrevented()||(p(r).removeClass(zt),"ontouchstart"in document.documentElement&&p(document.body).children().off("mouseover",null,p.noop),this._activeTrigger.click=!1,this._activeTrigger[Xt]=!1,this._activeTrigger[Vt]=!1,p(this.tip).hasClass($t)?(n=g.getTransitionDurationFromElement(r),p(r).one(g.TRANSITION_END,t).emulateTransitionEnd(n)):t(),this._hoverState="")},e.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},e.isWithContent=function(){return Boolean(this.getTitle())},e.addAttachmentClass=function(e){p(this.getTipElement()).addClass(Ht+"-"+e)},e.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},e.setContent=function(){var e=this.getTipElement();this.setElementContent(p(e.querySelectorAll(".tooltip-inner")),this.getTitle()),p(e).removeClass($t+" "+zt)},e.setElementContent=function(e,t){"object"!=typeof t||!t.nodeType&&!t.jquery?this.config.html?(this.config.sanitize&&(t=Ot(t,this.config.whiteList,this.config.sanitizeFn)),e.html(t)):e.text(t):this.config.html?p(t).parent().is(e)||e.empty().append(t):e.text(p(t).text())},e.getTitle=function(){return this.element.getAttribute("data-original-title")||("function"==typeof this.config.title?this.config.title.call(this.element):this.config.title)},e._getPopperConfig=function(e){var t=this;return l({},{placement:e,modifiers:{offset:this._getOffset(),flip:{behavior:this.config.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t._handlePopperPlacementChange(e)},onUpdate:function(e){return t._handlePopperPlacementChange(e)}},this.config.popperConfig)},e._getOffset=function(){var t=this,e={};return"function"==typeof this.config.offset?e.fn=function(e){return e.offsets=l({},e.offsets,t.config.offset(e.offsets,t.element)||{}),e}:e.offset=this.config.offset,e},e._getContainer=function(){return!1===this.config.container?document.body:g.isElement(this.config.container)?p(this.config.container):p(document).find(this.config.container)},e._getAttachment=function(e){return Bt[e.toUpperCase()]},e._setListeners=function(){var i=this;this.config.trigger.split(" ").forEach(function(e){var t,n;"click"===e?p(i.element).on(i.constructor.Event.CLICK,i.config.selector,function(e){return i.toggle(e)}):"manual"!==e&&(t=e===Vt?i.constructor.Event.MOUSEENTER:i.constructor.Event.FOCUSIN,n=e===Vt?i.constructor.Event.MOUSELEAVE:i.constructor.Event.FOCUSOUT,p(i.element).on(t,i.config.selector,function(e){return i._enter(e)}).on(n,i.config.selector,function(e){return i._leave(e)}))}),this._hideModalHandler=function(){i.element&&i.hide()},p(this.element).closest(".modal").on("hide.bs.modal",this._hideModalHandler),this.config.selector?this.config=l({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},e._fixTitle=function(){var e=typeof this.element.getAttribute("data-original-title");!this.element.getAttribute("title")&&"string"==e||(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},e._enter=function(e,t){var n=this.constructor.DATA_KEY;(t=t||p(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),p(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusin"===e.type?Xt:Vt]=!0),p(t.getTipElement()).hasClass(zt)||t._hoverState===Wt?t._hoverState=Wt:(clearTimeout(t._timeout),t._hoverState=Wt,t.config.delay&&t.config.delay.show?t._timeout=setTimeout(function(){t._hoverState===Wt&&t.show()},t.config.delay.show):t.show())},e._leave=function(e,t){var n=this.constructor.DATA_KEY;(t=t||p(e.currentTarget).data(n))||(t=new this.constructor(e.currentTarget,this._getDelegateConfig()),p(e.currentTarget).data(n,t)),e&&(t._activeTrigger["focusout"===e.type?Xt:Vt]=!1),t._isWithActiveTrigger()||(clearTimeout(t._timeout),t._hoverState="out",t.config.delay&&t.config.delay.hide?t._timeout=setTimeout(function(){"out"===t._hoverState&&t.hide()},t.config.delay.hide):t.hide())},e._isWithActiveTrigger=function(){for(var e in this._activeTrigger)if(this._activeTrigger[e])return!0;return!1},e._getConfig=function(e){var t=p(this.element).data();return Object.keys(t).forEach(function(e){-1!==Mt.indexOf(e)&&delete t[e]}),"number"==typeof(e=l({},this.constructor.Default,t,"object"==typeof e&&e?e:{})).delay&&(e.delay={show:e.delay,hide:e.delay}),"number"==typeof e.title&&(e.title=e.title.toString()),"number"==typeof e.content&&(e.content=e.content.toString()),g.typeCheckConfig(Lt,e,this.constructor.DefaultType),e.sanitize&&(e.template=Ot(e.template,e.whiteList,e.sanitizeFn)),e},e._getDelegateConfig=function(){var e={};if(this.config)for(var t in this.config)this.constructor.Default[t]!==this.config[t]&&(e[t]=this.config[t]);return e},e._cleanTipClass=function(){var e=p(this.getTipElement()),t=e.attr("class").match(Rt);null!==t&&t.length&&e.removeClass(t.join(""))},e._handlePopperPlacementChange=function(e){this.tip=e.instance.popper,this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(e.placement))},e._fixTransition=function(){var e=this.getTipElement(),t=this.config.animation;null===e.getAttribute("x-placement")&&(p(e).removeClass($t),this.config.animation=!1,this.hide(),this.show(),this.config.animation=t)},i._jQueryInterface=function(n){return this.each(function(){var e=p(this).data(It),t="object"==typeof n&&n;if((e||!/dispose|hide/.test(n))&&(e||(e=new i(this,t),p(this).data(It,e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return Qt}},{key:"NAME",get:function(){return Lt}},{key:"DATA_KEY",get:function(){return It}},{key:"Event",get:function(){return Ut}},{key:"EVENT_KEY",get:function(){return qt}},{key:"DefaultType",get:function(){return Ft}}]),i}();p.fn[Lt]=Yt._jQueryInterface,p.fn[Lt].Constructor=Yt,p.fn[Lt].noConflict=function(){return p.fn[Lt]=Pt,Yt._jQueryInterface};var Kt="popover",Gt="bs.popover",Jt="."+Gt,Zt=p.fn[Kt],en="bs-popover",tn=new RegExp("(^|\\s)"+en+"\\S+","g"),nn=l({},Yt.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),rn=l({},Yt.DefaultType,{content:"(string|element|function)"}),on={HIDE:"hide"+Jt,HIDDEN:"hidden"+Jt,SHOW:"show"+Jt,SHOWN:"shown"+Jt,INSERTED:"inserted"+Jt,CLICK:"click"+Jt,FOCUSIN:"focusin"+Jt,FOCUSOUT:"focusout"+Jt,MOUSEENTER:"mouseenter"+Jt,MOUSELEAVE:"mouseleave"+Jt},sn=function(e){var t,n;function i(){return e.apply(this,arguments)||this}n=e,(t=i).prototype=Object.create(n.prototype),(t.prototype.constructor=t).__proto__=n;var r=i.prototype;return r.isWithContent=function(){return this.getTitle()||this._getContent()},r.addAttachmentClass=function(e){p(this.getTipElement()).addClass(en+"-"+e)},r.getTipElement=function(){return this.tip=this.tip||p(this.config.template)[0],this.tip},r.setContent=function(){var e=p(this.getTipElement());this.setElementContent(e.find(".popover-header"),this.getTitle());var t=this._getContent();"function"==typeof t&&(t=t.call(this.element)),this.setElementContent(e.find(".popover-body"),t),e.removeClass("fade show")},r._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},r._cleanTipClass=function(){var e=p(this.getTipElement()),t=e.attr("class").match(tn);null!==t&&0<t.length&&e.removeClass(t.join(""))},i._jQueryInterface=function(n){return this.each(function(){var e=p(this).data(Gt),t="object"==typeof n?n:null;if((e||!/dispose|hide/.test(n))&&(e||(e=new i(this,t),p(this).data(Gt,e)),"string"==typeof n)){if(void 0===e[n])throw new TypeError('No method named "'+n+'"');e[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return nn}},{key:"NAME",get:function(){return Kt}},{key:"DATA_KEY",get:function(){return Gt}},{key:"Event",get:function(){return on}},{key:"EVENT_KEY",get:function(){return Jt}},{key:"DefaultType",get:function(){return rn}}]),i}(Yt);p.fn[Kt]=sn._jQueryInterface,p.fn[Kt].Constructor=sn,p.fn[Kt].noConflict=function(){return p.fn[Kt]=Zt,sn._jQueryInterface};var an="scrollspy",ln="bs.scrollspy",un="."+ln,cn=p.fn[an],fn={offset:10,method:"auto",target:""},dn={offset:"number",method:"string",target:"(string|element)"},hn="active",pn=".nav, .list-group",gn=".nav-link",mn=".list-group-item",vn="position",yn=function(){function n(e,t){var n=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(t),this._selector=this._config.target+" "+gn+","+this._config.target+" "+mn+","+this._config.target+" .dropdown-item",this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,p(this._scrollElement).on("scroll.bs.scrollspy",function(e){return n._process(e)}),this.refresh(),this._process()}var e=n.prototype;return e.refresh=function(){var t=this,e=this._scrollElement===this._scrollElement.window?"offset":vn,r="auto"===this._config.method?e:this._config.method,o=r===vn?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),[].slice.call(document.querySelectorAll(this._selector)).map(function(e){var t,n=g.getSelectorFromElement(e);if(n&&(t=document.querySelector(n)),t){var i=t.getBoundingClientRect();if(i.width||i.height)return[p(t)[r]().top+o,n]}return null}).filter(function(e){return e}).sort(function(e,t){return e[0]-t[0]}).forEach(function(e){t._offsets.push(e[0]),t._targets.push(e[1])})},e.dispose=function(){p.removeData(this._element,ln),p(this._scrollElement).off(un),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},e._getConfig=function(e){var t;return"string"!=typeof(e=l({},fn,"object"==typeof e&&e?e:{})).target&&g.isElement(e.target)&&((t=p(e.target).attr("id"))||(t=g.getUID(an),p(e.target).attr("id",t)),e.target="#"+t),g.typeCheckConfig(an,e,dn),e},e._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},e._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},e._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},e._process=function(){var e=this._getScrollTop()+this._config.offset,t=this._getScrollHeight(),n=this._config.offset+t-this._getOffsetHeight();if(this._scrollHeight!==t&&this.refresh(),n<=e){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&e<this._offsets[0]&&0<this._offsets[0])return this._activeTarget=null,void this._clear();for(var r=this._offsets.length;r--;){this._activeTarget!==this._targets[r]&&e>=this._offsets[r]&&(void 0===this._offsets[r+1]||e<this._offsets[r+1])&&this._activate(this._targets[r])}}},e._activate=function(t){this._activeTarget=t,this._clear();var e=this._selector.split(",").map(function(e){return e+'[data-target="'+t+'"],'+e+'[href="'+t+'"]'}),n=p([].slice.call(document.querySelectorAll(e.join(","))));n.hasClass("dropdown-item")?(n.closest(".dropdown").find(".dropdown-toggle").addClass(hn),n.addClass(hn)):(n.addClass(hn),n.parents(pn).prev(gn+", "+mn).addClass(hn),n.parents(pn).prev(".nav-item").children(gn).addClass(hn)),p(this._scrollElement).trigger("activate.bs.scrollspy",{relatedTarget:t})},e._clear=function(){[].slice.call(document.querySelectorAll(this._selector)).filter(function(e){return e.classList.contains(hn)}).forEach(function(e){return e.classList.remove(hn)})},n._jQueryInterface=function(t){return this.each(function(){var e=p(this).data(ln);if(e||(e=new n(this,"object"==typeof t&&t),p(this).data(ln,e)),"string"==typeof t){if(void 0===e[t])throw new TypeError('No method named "'+t+'"');e[t]()}})},s(n,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"Default",get:function(){return fn}}]),n}();p(window).on("load.bs.scrollspy.data-api",function(){for(var e=[].slice.call(document.querySelectorAll('[data-spy="scroll"]')),t=e.length;t--;){var n=p(e[t]);yn._jQueryInterface.call(n,n.data())}}),p.fn[an]=yn._jQueryInterface,p.fn[an].Constructor=yn,p.fn[an].noConflict=function(){return p.fn[an]=cn,yn._jQueryInterface};var bn="bs.tab",_n=p.fn.tab,wn="active",xn=".active",En="> li > .active",Tn=function(){function i(e){this._element=e}var e=i.prototype;return e.show=function(){var e,t,n,i,r,o,s,a,l=this;this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&p(this._element).hasClass(wn)||p(this._element).hasClass("disabled")||(t=p(this._element).closest(".nav, .list-group")[0],n=g.getSelectorFromElement(this._element),t&&(i="UL"===t.nodeName||"OL"===t.nodeName?En:xn,r=(r=p.makeArray(p(t).find(i)))[r.length-1]),o=p.Event("hide.bs.tab",{relatedTarget:this._element}),s=p.Event("show.bs.tab",{relatedTarget:r}),r&&p(r).trigger(o),p(this._element).trigger(s),s.isDefaultPrevented()||o.isDefaultPrevented()||(n&&(e=document.querySelector(n)),this._activate(this._element,t),a=function(){var e=p.Event("hidden.bs.tab",{relatedTarget:l._element}),t=p.Event("shown.bs.tab",{relatedTarget:r});p(r).trigger(e),p(l._element).trigger(t)},e?this._activate(e,e.parentNode,a):a()))},e.dispose=function(){p.removeData(this._element,bn),this._element=null},e._activate=function(e,t,n){function i(){return o._transitionComplete(e,s,n)}var r,o=this,s=(!t||"UL"!==t.nodeName&&"OL"!==t.nodeName?p(t).children(xn):p(t).find(En))[0],a=n&&s&&p(s).hasClass("fade");s&&a?(r=g.getTransitionDurationFromElement(s),p(s).removeClass("show").one(g.TRANSITION_END,i).emulateTransitionEnd(r)):i()},e._transitionComplete=function(e,t,n){var i,r,o;t&&(p(t).removeClass(wn),(i=p(t.parentNode).find("> .dropdown-menu .active")[0])&&p(i).removeClass(wn),"tab"===t.getAttribute("role")&&t.setAttribute("aria-selected",!1)),p(e).addClass(wn),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),g.reflow(e),e.classList.contains("fade")&&e.classList.add("show"),e.parentNode&&p(e.parentNode).hasClass("dropdown-menu")&&((r=p(e).closest(".dropdown")[0])&&(o=[].slice.call(r.querySelectorAll(".dropdown-toggle")),p(o).addClass(wn)),e.setAttribute("aria-expanded",!0)),n&&n()},i._jQueryInterface=function(n){return this.each(function(){var e=p(this),t=e.data(bn);if(t||(t=new i(this),e.data(bn,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.5.2"}}]),i}();p(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',function(e){e.preventDefault(),Tn._jQueryInterface.call(p(this),"show")}),p.fn.tab=Tn._jQueryInterface,p.fn.tab.Constructor=Tn,p.fn.tab.noConflict=function(){return p.fn.tab=_n,Tn._jQueryInterface};var Cn="toast",Sn="bs.toast",An="."+Sn,Nn=p.fn[Cn],kn="click.dismiss"+An,Dn="show",jn="showing",On={animation:"boolean",autohide:"boolean",delay:"number"},Ln={animation:!0,autohide:!0,delay:500},In=function(){function i(e,t){this._element=e,this._config=this._getConfig(t),this._timeout=null,this._setListeners()}var e=i.prototype;return e.show=function(){var e,t,n=this,i=p.Event("show.bs.toast");p(this._element).trigger(i),i.isDefaultPrevented()||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),e=function(){n._element.classList.remove(jn),n._element.classList.add(Dn),p(n._element).trigger("shown.bs.toast"),n._config.autohide&&(n._timeout=setTimeout(function(){n.hide()},n._config.delay))},this._element.classList.remove("hide"),g.reflow(this._element),this._element.classList.add(jn),this._config.animation?(t=g.getTransitionDurationFromElement(this._element),p(this._element).one(g.TRANSITION_END,e).emulateTransitionEnd(t)):e())},e.hide=function(){var e;this._element.classList.contains(Dn)&&(e=p.Event("hide.bs.toast"),p(this._element).trigger(e),e.isDefaultPrevented()||this._close())},e.dispose=function(){this._clearTimeout(),this._element.classList.contains(Dn)&&this._element.classList.remove(Dn),p(this._element).off(kn),p.removeData(this._element,Sn),this._element=null,this._config=null},e._getConfig=function(e){return e=l({},Ln,p(this._element).data(),"object"==typeof e&&e?e:{}),g.typeCheckConfig(Cn,e,this.constructor.DefaultType),e},e._setListeners=function(){var e=this;p(this._element).on(kn,'[data-dismiss="toast"]',function(){return e.hide()})},e._close=function(){function e(){n._element.classList.add("hide"),p(n._element).trigger("hidden.bs.toast")}var t,n=this;this._element.classList.remove(Dn),this._config.animation?(t=g.getTransitionDurationFromElement(this._element),p(this._element).one(g.TRANSITION_END,e).emulateTransitionEnd(t)):e()},e._clearTimeout=function(){clearTimeout(this._timeout),this._timeout=null},i._jQueryInterface=function(n){return this.each(function(){var e=p(this),t=e.data(Sn);if(t||(t=new i(this,"object"==typeof n&&n),e.data(Sn,t)),"string"==typeof n){if(void 0===t[n])throw new TypeError('No method named "'+n+'"');t[n](this)}})},s(i,null,[{key:"VERSION",get:function(){return"4.5.2"}},{key:"DefaultType",get:function(){return On}},{key:"Default",get:function(){return Ln}}]),i}();p.fn[Cn]=In._jQueryInterface,p.fn[Cn].Constructor=In,p.fn[Cn].noConflict=function(){return p.fn[Cn]=Nn,In._jQueryInterface},e.Alert=u,e.Button=y,e.Carousel=j,e.Collapse=Q,e.Dropdown=at,e.Modal=At,e.Popover=sn,e.Scrollspy=yn,e.Tab=Tn,e.Toast=In,e.Tooltip=Yt,e.Util=g,Object.defineProperty(e,"__esModule",{value:!0})});