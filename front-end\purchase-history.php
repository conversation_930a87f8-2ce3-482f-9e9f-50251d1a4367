<?php
session_start();
include('../functions/server.php'); // Make sure this file establishes the database connection

if (isset($_GET['logout'])) {
    session_destroy();
    unset($_SESSION['username']);
    header('location: ../index.php');
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Home</title>
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap, fonts & icons -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <!-- Plugin'stylesheets -->
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <!-- Vendor stylesheets -->
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    /* Additional CSS to ensure full page height */
    html,
    body {
        height: 100%;
        margin: 0;
    }

    body {
        margin-top: -115px !important;

    }

    @media (max-width: 767px) {
        body {
            margin-top: -90px !important;
        }
    }

    .full-height {
        height: 100vh;
        /* Full viewport height */
    }

    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper contain all content */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: hidden !important;
    }

    /* Badge styles */
    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    /* Ticket type badges */
    .badge-ultimate {
        background-color: #793BF0;
        color: #fff;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #000;
    }

    .badge-premium {
        background-color: #01A7E1;
        color: #fff;
    }
    </style>
</head>

<body data-theme="light">

    <div class="site-wrapper overflow-hidden">
        <!-- Header Area -->
        <?php include('../header-footer/header.php'); ?>
        <!-- navbar-dark -->
        <?php if (isset($_SESSION['username'])) : ?>
        <?php
            $username = $_SESSION['username'];
            $sqlshow = "SELECT username, email, starter_tickets, premium_tickets, tell, company_name, tax_id, address, district, city, postal_code, country FROM user WHERE username = '$username'";
            $resultshow = mysqli_query($conn, $sqlshow);
            ?>
        <?php if ($resultshow && mysqli_num_rows($resultshow) > 0) : ?>
        <!-- Page Banner Area -->
        <div class="inner-banner pt-29 pb-md-13 bg-default-2">
            <div class="container">

            </div>
        </div>
        <div class="bg-default-2 pb-17 pb-md-29 ">
            <div class="container">
                <div class="row justify-content-md-between pt-9">
                    <div class="col-sm-6 col-md-5 col-lg-4 col-xl-3">

                    </div>
                </div>

                <div class="row mt-4">
                    <!-- Left sidebar with user menu -->
                    <div class="col-lg-3 col-md-4">
                        <?php include('user-menu.php'); ?>
                    </div>

                    <!-- Main content area -->
                    <div class="col-lg-9 col-md-8">
                        <div class="cart-details-main-block" id="dynamic-cart">
                            <!-- .cart_single-product-block starts -->
                            <!-- style="margin-top: 20px;" -->
                            <div class="bg-white p-8 rounded-10 mb-5 position-relative" style="overflow-x: hidden;">
                                <?php
                                        $username = $_SESSION['username'];

                                        // Get search query from form
                                        $search = isset($_GET['search']) ? trim($_GET['search']) : "";

                                        // Search by purchase ID, ticket type, or purchase time
                                        $searchSql = "";
                                        if (!empty($search)) {
                                            $searchSafe = mysqli_real_escape_string($conn, $search);
                                            $searchSql = "AND (
                                                purchaseid LIKE '%$searchSafe%' OR
                                                ticket_type LIKE '%$searchSafe%' OR
                                                DATE(purchase_time) = '$searchSafe'
                                            )";
                                        }

                                        // Pagination settings
                                        $itemsPerPage = 5;
                                        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
                                        if ($page < 1) $page = 1;
                                        $offset = ($page - 1) * $itemsPerPage;

                                        // Count total purchases for pagination
                                        $countSql = "SELECT COUNT(*) as total FROM purchasetickets WHERE username = '$username' $searchSql";
                                        $countResult = mysqli_query($conn, $countSql);
                                        $totalItems = mysqli_fetch_assoc($countResult)['total'];
                                        $totalPages = ceil($totalItems / $itemsPerPage);

                                        // Get purchase history with pagination
                                        $sqlHistory = "SELECT * FROM purchasetickets WHERE username = '$username' $searchSql ORDER BY purchase_time DESC LIMIT $itemsPerPage OFFSET $offset";
                                        $resultHistory = mysqli_query($conn, $sqlHistory);

                                        // Display heading and search form regardless of results
                                        ?>
                                <h2 class="text-center mb-4">Purchase History</h2>

                                <!-- Timezone indicator -->
                                <div class="text-center mb-3">
                                    <small class="text-muted">
                                        <i class="fas fa-clock"></i>
                                        Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                                    </small>
                                </div>

                                <style>
                                .form-control,
                                .btn,
                                .filter-select {
                                    font-size: 16px;
                                }

                                /* Search bar styles to match my-ticket-log.php */
                                .search-filter-row {
                                    padding: 0 40px;
                                }

                                .search-box {
                                    width: auto;
                                }

                                .search-box .input-group {
                                    width: 550px !important;
                                    max-width: 100% !important;
                                }

                                .search-input {
                                    border-top-right-radius: 0 !important;
                                    border-bottom-right-radius: 0 !important;
                                    height: 38px;
                                    font-size: 14px;
                                }

                                .search-button {
                                    border-top-right-radius: 4px !important;
                                    border-bottom-right-radius: 4px !important;
                                    border-top-left-radius: 0 !important;
                                    border-bottom-left-radius: 0 !important;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    padding: 0.375rem 0.75rem;
                                    width: 40px !important;
                                    min-width: 40px !important;
                                }

                                /* Responsive adjustments */
                                @media (max-width: 991px) {
                                    .search-box {
                                        width: 100%;
                                    }

                                    .search-box .input-group {
                                        width: 100%;
                                        max-width: 500px;
                                        margin: 0 auto;
                                    }
                                }

                                @media (max-width: 767px) {
                                    .search-filter-row {
                                        padding: 0 15px;
                                    }

                                    /* Mobile table styles */
                                    .purchase-table {
                                        font-size: 14px;
                                    }

                                    .purchase-table th,
                                    .purchase-table td {
                                        padding: 8px 4px;
                                        white-space: nowrap;
                                    }

                                    /* Center align the table on mobile */
                                    .table-responsive {
                                        margin: 0 -15px;
                                        width: calc(100% + 30px);
                                        max-width: none;
                                        overflow-x: auto;
                                    }
                                }

                                /* Extra small devices */
                                @media (max-width: 480px) {
                                    .purchase-table {
                                        font-size: 14px;
                                    }

                                    .purchase-table th,
                                    .purchase-table td {
                                        padding: 6px 3px;
                                    }

                                    .badge {
                                        font-size: 12px;
                                        padding: 4px;
                                    }

                                    ul.pagination.justify-content-center {
                                        font-size: 14px !important;
                                    }
                                }
                                </style>

                                <!-- Search form -->
                                <form method="GET" class="mb-4">
                                    <div class="search-filter-row d-flex justify-content-start flex-wrap">
                                        <div class="search-box w-100">
                                            <div class="input-group">
                                                <input type="text" name="search" class="form-control search-input"
                                                    placeholder="Search by ID, ticket type or date..."
                                                    value="<?php echo htmlspecialchars($search); ?>">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary search-button">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>

                                <?php if ($resultHistory && mysqli_num_rows($resultHistory) > 0) : ?>
                                <div class="table-responsive">
                                    <table class="table table-striped mx-auto purchase-table" style="width: 100%;">
                                        <tr>
                                            <!-- <th scope="col">Ticket Type</th> -->
                                            <th class="text-center" scope="col">Purchase Id</th>
                                            <th class="text-center" scope="col">Purchase Time</th>
                                            <th class="text-center" scope="col">Ticket type</th>
                                            <th class="text-center" scope="col">Package Size</th>
                                            <th class="text-center" scope="col">Qty</th>
                                            <th class="text-center" scope="col">Total</th>
                                        </tr>
                                        <?php while ($user = mysqli_fetch_assoc($resultHistory)) : ?>
                                        <tr>
                                            <td class="text-center" scope="row"><?php echo $user['purchaseid']; ?></td>
                                            <td class="text-center" scope="row"><?php echo showCustomerTimeSimple($user['purchase_time']); ?>
                                            </td>
                                            <td class="text-center" scope="row">
                                                <?php
                                                                // Display 'Business' for premium tickets
                                                                $ticketType = strtolower($user['ticket_type']);
                                                                $badgeClass = $ticketType;
                                                                $displayText = ucfirst($ticketType);

                                                                if ($ticketType == 'premium') {
                                                                    $displayText = 'Business';
                                                                }
                                                                ?>
                                                <span class="badge badge-<?php echo $badgeClass; ?>">
                                                    <?php echo $displayText; ?>
                                                </span>
                                            </td>
                                            <td class="text-center" scope="row"><?php echo $user['package_size']; ?>
                                            </td>
                                            <td class="text-center" scope="row">
                                                <?php echo $user['numbers_per_package']; ?></td>
                                            <td class="text-center" scope="row">
                                                <?php echo $user['dollar_price_per_package']; ?></td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </table>

                                    <!-- Pagination Controls -->
                                    <?php if ($totalPages > 1): ?>
                                    <div class="pagination-container mt-4">
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination justify-content-center">
                                                <!-- Previous Button -->
                                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"
                                                        aria-label="Previous">
                                                        <span aria-hidden="true">&laquo; Previous</span>
                                                    </a>
                                                </li>

                                                <!-- Page Numbers -->
                                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                                <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                                <?php endfor; ?>

                                                <!-- Next Button -->
                                                <li
                                                    class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                                    <a class="page-link"
                                                        href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>"
                                                        aria-label="Next">
                                                        <span aria-hidden="true">Next &raquo;</span>
                                                    </a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </div>
                                    <?php endif; ?>
                                    <?php else : ?>
                                    <div class="d-flex justify-content-center align-items-center mt-5">
                                        <div class="text-center">
                                            <?php if (!empty($search)) : ?>
                                            <p class="mb-3">No purchase history found matching
                                                "<?php echo htmlspecialchars($search); ?>".</p>
                                            <p><a href="purchase-history.php" class="btn btn-outline-primary">Clear
                                                    search</a></p>
                                            <?php else : ?>
                                            <p>No purchase history found for the user.</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <!-- .cart_single-product-block ends -->
                            </div>
                        </div>
                    </div>
                    <div class="inner-banner pt-29 pb-md-20 bg-default-2">
                        <div class="container">

                        </div>
                    </div>
                    <?php else : ?>
                    <div class="d-flex justify-content-center align-items-center full-height">
                        <div class="text-center">
                            <?php echo "No user found."; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    <?php else : ?>
                    <div class="d-flex justify-content-center align-items-center full-height">
                        <div class="text-center">
                            <?php echo "You are not logged in."; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
                <!-- Vendor Scripts -->
                <script src="../js/vendor.min.js"></script>
                <!-- Plugin's Scripts -->
                <script src="../plugins/fancybox/jquery.fancybox.min.js"></script>
                <script src="../plugins/nice-select/jquery.nice-select.min.js"></script>
                <script src="../plugins/aos/aos.min.js"></script>
                <script src="../plugins/slick/slick.min.js"></script>
                <script src="../plugins/date-picker/js/gijgo.min.js"></script>
                <script src="../plugins/counter-up/jquery.waypoints.min.js"></script>
                <script src="../plugins/counter-up/jquery.counterup.min.js"></script>
                <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>
                <!-- Activation Script -->
                <script src="../js/custom.js"></script>

                <!-- Customer Timezone Detection -->
                <script src="../js/customer-timezone.js"></script>

                <!-- Update timezone display when detected -->
                <script>
                document.addEventListener('customerTimezoneDetected', function(event) {
                    const timezoneDisplay = document.getElementById('customer-timezone-display');
                    if (timezoneDisplay) {
                        timezoneDisplay.textContent = event.detail.timezone;
                    }
                    console.log('Customer timezone updated in purchase-history:', event.detail.timezone);
                });

                // Also update on page load if timezone is already available
                document.addEventListener('DOMContentLoaded', function() {
                    setTimeout(function() {
                        if (window.CustomerTimezone) {
                            const timezone = window.CustomerTimezone.getCustomerTimezone();
                            const timezoneDisplay = document.getElementById('customer-timezone-display');
                            if (timezoneDisplay && timezone) {
                                timezoneDisplay.textContent = timezone;
                            }
                        }
                    }, 1000);
                });
                </script>
</body>

</html>