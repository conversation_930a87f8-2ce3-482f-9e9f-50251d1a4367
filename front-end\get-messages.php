<?php
// Include database connection
include('../functions/server.php');

// Include timezone helper for proper time handling
include('../functions/timezone-helper.php');

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$user_id = $_SESSION['user_id'];

// Get ticket ID
$ticket_id = isset($_GET['ticket_id']) ? intval($_GET['ticket_id']) : null;

if (!$ticket_id) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'No ticket ID provided']);
    exit();
}

// Create chat_messages table if it doesn't exist
$create_table_sql = "CREATE TABLE IF NOT EXISTS chat_messages (
    id INT(11) AUTO_INCREMENT PRIMARY KEY,
    ticket_id INT(11) NOT NULL,
    sender_id INT(11) NOT NULL,
    sender_type ENUM('user', 'admin') NOT NULL,
    message TEXT NOT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);";

mysqli_query($conn, $create_table_sql);

// Verify the ticket belongs to the user
$ticket_query = "SELECT * FROM support_tickets WHERE id = $ticket_id AND user_id = $user_id";
$ticket_result = mysqli_query($conn, $ticket_query);

if (!$ticket_result || mysqli_num_rows($ticket_result) === 0) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Invalid ticket']);
    exit();
}

// Get messages for the ticket
$messages = [];
$messages_query = "SELECT cm.*,
                  CASE
                      WHEN cm.sender_type = 'admin' THEN a.username
                      WHEN cm.sender_type = 'user' THEN u.username
                  END as sender_name
                  FROM chat_messages cm
                  LEFT JOIN admin_users a ON cm.sender_id = a.id AND cm.sender_type = 'admin'
                  LEFT JOIN user u ON cm.sender_id = u.id AND cm.sender_type = 'user'
                  WHERE cm.ticket_id = $ticket_id
                  ORDER BY cm.created_at ASC";
$messages_result = mysqli_query($conn, $messages_query);

if ($messages_result) {
    while ($message = mysqli_fetch_assoc($messages_result)) {
        // Let JavaScript handle time formatting in user's timezone
        // Don't add formatted_time here - JavaScript will format it
        $messages[] = $message;
    }
}

// Mark admin messages as read
$update_query = "UPDATE chat_messages
                SET is_read = 1
                WHERE ticket_id = $ticket_id
                AND sender_type = 'admin'
                AND is_read = 0";
mysqli_query($conn, $update_query);

// Return messages as JSON
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'messages' => $messages
]);
