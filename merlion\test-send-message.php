<?php
// Include database connection
include('../functions/server.php');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    echo "Please log in as admin first.";
    exit();
}

echo "<h2>Admin Send Message Test</h2>";

// Test the send-message.php endpoint
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<h3>Testing send-message.php endpoint...</h3>";
    
    // Simulate the AJAX request
    $_POST['message'] = 'Test message from admin';
    $_POST['user_id'] = 1; // Use a valid user ID
    $_POST['ticket_id'] = 1; // Use a valid ticket ID
    
    // Include the send-message.php logic
    include('send-message.php');
    exit();
}

// Show form to test
?>
<form method="POST">
    <h3>Test Admin Message Sending</h3>
    <p>This will test the send-message.php endpoint with sample data.</p>
    <button type="submit">Test Send Message</button>
</form>

<h3>Current Admin Session:</h3>
<p>Admin ID: <?php echo $_SESSION['admin_id'] ?? 'Not set'; ?></p>
<p>Admin Username: <?php echo $_SESSION['admin_username'] ?? 'Not set'; ?></p>

<h3>Database Tables Check:</h3>
<?php
// Check if chat_messages table exists
$check_table = "SHOW TABLES LIKE 'chat_messages'";
$result = mysqli_query($conn, $check_table);
if (mysqli_num_rows($result) > 0) {
    echo "<p>✅ chat_messages table exists</p>";
    
    // Show table structure
    $describe = "DESCRIBE chat_messages";
    $desc_result = mysqli_query($conn, $describe);
    echo "<table border='1'><tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = mysqli_fetch_assoc($desc_result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . $row['Default'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ chat_messages table does not exist</p>";
}

// Check if admin_users table exists
$check_admin_table = "SHOW TABLES LIKE 'admin_users'";
$admin_result = mysqli_query($conn, $check_admin_table);
if (mysqli_num_rows($admin_result) > 0) {
    echo "<p>✅ admin_users table exists</p>";
} else {
    echo "<p>❌ admin_users table does not exist</p>";
}

echo "<br><a href='admin-chat.php'>← Back to Admin Chat</a>";
mysqli_close($conn);
?>
