<?php
/**
 * LOCALHOST WEBHOOK SIMULATION
 * This file simulates webhook processing for localhost environments
 * where Stripe cannot send real webhooks
 */

function simulateWebhookProcessing($stripe_session, $conn) {
    error_log("Simulating webhook processing for session: " . $stripe_session->id);

    $session_id = $stripe_session->id;

    // DOUBLE PROCESSING PROTECTION: Check if this session was already processed by real webhook
    $already_processed_by_webhook = 0;
    $check_real_webhook = $conn->prepare("SELECT COUNT(*) FROM purchasetickets WHERE transactionid = ?");
    $check_real_webhook->bind_param("s", $session_id);
    $check_real_webhook->execute();
    $check_real_webhook->bind_result($already_processed_by_webhook);
    $check_real_webhook->fetch();
    $check_real_webhook->close();

    if ($already_processed_by_webhook > 0) {
        error_log("Simulate webhook: Session $session_id already processed by real webhook, skipping simulation");
        return true; // Return success since it was already processed
    }

    // Extract data from Stripe session (same as webhook)
    $customer_details = $stripe_session->customer_details;
    $email = $customer_details->email ?? '';
    $full_name = $customer_details->name ?? '';
    $address = $customer_details->address ?? null;
    $session_id = $stripe_session->id;
    
    error_log("Simulate webhook: Session ID: $session_id, Email: $email, Name: $full_name");

    // Check if user already exists
    $check = $conn->prepare("SELECT id, username FROM user WHERE email = ?");
    $check->bind_param("s", $email);
    $check->execute();
    $result = $check->get_result();
    $user_exists = false;
    
    if ($result->num_rows > 0) {
        $existing_user = $result->fetch_assoc();
        $user_id = $existing_user['id'];
        $username = $existing_user['username'];
        $user_exists = true;
        error_log("Simulate webhook: User already exists with email: $email, user_id: $user_id");
    }

    if (!$user_exists) {
        // Generate username & password (same logic as webhook)
        $username = 'user' . rand(10000,99999);
        $password = bin2hex(random_bytes(4));
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Parse address fields
        $address1 = $address->line1 ?? '';
        $address2 = $address->line2 ?? '';
        $district = $address->district ?? ($address->suburb ?? '');
        $city = $address->city ?? '';
        $state = $address->state ?? '';
        $postal_code = $address->postal_code ?? '';
        $country = $address->country ?? '';
        $first_name = $full_name;
        $last_name = '';
        $registration_time = date('Y-m-d H:i:s');

        // Create Stripe customer (same as webhook)
        $stripe_customer_id = null;
        try {
            $customer = \Stripe\Customer::create([
                'email' => $email,
                'name' => $full_name,
                'address' => [
                    'line1' => $address1,
                    'line2' => $address2,
                    'city' => $city,
                    'state' => $state,
                    'postal_code' => $postal_code,
                    'country' => $country,
                ],
                'metadata' => [
                    'source' => 'localhost_simulation',
                    'username' => $username
                ]
            ]);
            $stripe_customer_id = $customer->id;
            error_log("Simulate webhook: Stripe customer created: $stripe_customer_id");
        } catch(Exception $e) {
            error_log("Simulate webhook: Error creating Stripe customer: " . $e->getMessage());
        }

        // Insert user into database (same as webhook)
        try {
            $sql = "INSERT INTO user (username, email, password, address, address2, district, city, state, country, postal_code, registration_time, first_name, last_name, stripe_customer_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ssssssssssssss", $username, $email, $hashed_password, $address1, $address2, $district, $city, $state, $country, $postal_code, $registration_time, $first_name, $last_name, $stripe_customer_id);
            $stmt->execute();
            $user_id = $conn->insert_id;
            error_log("Simulate webhook: User saved to database, id: $user_id");
        } catch(Exception $e) {
            error_log("Simulate webhook: DB error (user): " . $e->getMessage());
            return false;
        }
    } else {
        // For existing users, DO NOT generate new password or change their existing password
        // Just use a placeholder for payment_temp (not used for existing users anyway)
        $password = 'existing_user_placeholder';
        error_log("Simulate webhook: Existing user detected, keeping original password intact: $username");
    }

    // Save to payment_temp (same as webhook)
    try {
        $stmt2 = $conn->prepare("INSERT INTO payment_temp (session_id, username, email, password, user_created) VALUES (?, ?, ?, ?, ?)");
        $user_created = !$user_exists ? 1 : 0;
        $stmt2->bind_param("ssssi", $session_id, $username, $email, $password, $user_created);
        $stmt2->execute();
        error_log("Simulate webhook: payment_temp saved with password: $password, user_created: $user_created");

        // ONLY update password for NEW users, NOT existing users
        if (!$user_exists) {
            $correct_password_hash = password_hash($password, PASSWORD_DEFAULT);
            $update_password_stmt = $conn->prepare("UPDATE user SET password = ? WHERE username = ?");
            $update_password_stmt->bind_param("ss", $correct_password_hash, $username);
            $update_password_stmt->execute();
            error_log("Simulate webhook: Updated NEW user password hash to match display password: $password");
        } else {
            error_log("Simulate webhook: Skipped password update for existing user to preserve original password");
        }

    } catch(Exception $e) {
        error_log("Simulate webhook: DB error (payment_temp): " . $e->getMessage());
        return false;
    }

    // Process tickets and purchase history (simplified version)
    try {
        error_log("Simulate webhook: Starting ticket processing for user_id: $user_id");
        
        // Get purchased items from session metadata
        $metadata = $stripe_session->metadata;
        $purchased_items = [];
        
        // Try database cart session first
        if (!empty($metadata['cart_session_id'])) {
            $cart_session_id = $metadata['cart_session_id'];
            $cart_query = "SELECT cart_data FROM cart_sessions WHERE session_id = ?";
            $cart_stmt = $conn->prepare($cart_query);
            $cart_stmt->bind_param("s", $cart_session_id);
            $cart_stmt->execute();
            $cart_result = $cart_stmt->get_result();

            if ($cart_result->num_rows > 0) {
                $cart_row = $cart_result->fetch_assoc();
                $purchased_items = json_decode($cart_row['cart_data'], true);
                error_log("Simulate webhook: Found cart data: " . json_encode($purchased_items));
            }
        }

        if (!empty($purchased_items)) {
            // Begin transaction
            $conn->begin_transaction();

            // Process each purchased item
            foreach ($purchased_items as $item) {
                $ticket_type = $item['ticket_type'];
                $package_size = $item['package_size'];
                $dollar_price_per_package = $item['dollar_price_per_package'];
                $quantity = $item['quantity'];
                $numbers_per_package = $item['numbers_per_package'] ?? 1;
                $total_tickets = $numbers_per_package * $quantity;

                error_log("Simulate webhook: Processing item: Type=$ticket_type, Size=$package_size, Quantity=$quantity");

                // Update user's ticket count
                $column = '';
                if (stripos($ticket_type, 'starter') !== false) $column = 'starter_tickets';
                elseif (stripos($ticket_type, 'premium') !== false || stripos($ticket_type, 'business') !== false) $column = 'premium_tickets';
                elseif (stripos($ticket_type, 'ultimate') !== false) $column = 'ultimate_tickets';
                else $column = 'premium_tickets'; // default

                $update = $conn->prepare("UPDATE user SET $column = $column + ? WHERE id = ?");
                $update->bind_param("ii", $total_tickets, $user_id);
                if (!$update->execute()) {
                    throw new Exception("Failed to update ticket count: " . $update->error);
                }

                // Insert into purchasetickets
                $insert = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)");
                $insert->bind_param("sssiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $session_id, $total_tickets);
                if (!$insert->execute()) {
                    throw new Exception("Failed to insert purchase history: " . $insert->error);
                }
            }

            // Commit transaction
            $conn->commit();
            error_log("Simulate webhook: Successfully processed all tickets");
        }

        // Clear guest cart from session if it exists
        if (isset($_SESSION['guest_cart'])) {
            unset($_SESSION['guest_cart']);
            error_log("Simulate webhook: Cleared guest cart from session");
        }

    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollback();
        }
        error_log("Simulate webhook: Error processing tickets: " . $e->getMessage());
    }

    // NOTE: Don't delete payment_temp here on localhost - let payment-success.php show credentials first
    // The payment-success.php will handle cleanup after displaying credentials to user
    error_log("Simulate webhook: Keeping payment_temp data for payment-success.php to display credentials");

    error_log("Simulate webhook: Processing completed for session: $session_id");
    return true;
}
?>
