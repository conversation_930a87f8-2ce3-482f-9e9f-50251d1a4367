<?php
include('server.php');

echo "<h2>🗑️ Remove admin_notifications Table</h2>";

echo "<h3>🔍 **Analysis: Why This Table Can Be Safely Removed**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>❌ Problems with admin_notifications Table:</h4>";
echo "<ul style='color: #856404;'>";
echo "<li><strong>Redundant functionality:</strong> Duplicates existing notification systems</li>";
echo "<li><strong>Database waste:</strong> Creates unnecessary records</li>";
echo "<li><strong>Performance impact:</strong> Extra queries and table creation</li>";
echo "<li><strong>Maintenance overhead:</strong> More code to maintain</li>";
echo "<li><strong>Inconsistent usage:</strong> Not used consistently across all features</li>";
echo "</ul>";

echo "<h4>✅ Existing Notification Systems (Already Working):</h4>";
echo "<ul style='color: #856404;'>";
echo "<li><strong>New Tickets:</strong> <code>support_tickets.is_seen_by_admin = 0</code></li>";
echo "<li><strong>Unread Messages:</strong> <code>chat_messages.is_read = 0 AND sender_type = 'user'</code></li>";
echo "<li><strong>Admin Interface:</strong> Already shows correct counts without admin_notifications</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📊 **Current admin_notifications Table Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Check if table exists
$table_exists = false;
$record_count = 0;

$check_table_query = "SHOW TABLES LIKE 'admin_notifications'";
$check_result = mysqli_query($conn, $check_table_query);
if (mysqli_num_rows($check_result) > 0) {
    $table_exists = true;
    
    // Get record count
    $count_query = "SELECT COUNT(*) as count FROM admin_notifications";
    $count_result = mysqli_query($conn, $count_query);
    $record_count = mysqli_fetch_assoc($count_result)['count'];
    
    // Get recent records
    $recent_query = "SELECT * FROM admin_notifications ORDER BY created_at DESC LIMIT 5";
    $recent_result = mysqli_query($conn, $recent_query);
}

echo "<h4>Table Status:</h4>";
echo "<ul>";
if ($table_exists) {
    echo "<li><strong>Table exists:</strong> ✅ Yes</li>";
    echo "<li><strong>Record count:</strong> $record_count</li>";
    echo "<li><strong>Storage waste:</strong> " . ($record_count * 0.5) . " KB (estimated)</li>";
} else {
    echo "<li><strong>Table exists:</strong> ❌ No (already removed or never created)</li>";
}
echo "</ul>";

if ($table_exists && $record_count > 0) {
    echo "<h4>Recent Records (Last 5):</h4>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 6px;'>ID</th>";
    echo "<th style='padding: 6px;'>Type</th>";
    echo "<th style='padding: 6px;'>Title</th>";
    echo "<th style='padding: 6px;'>Created</th>";
    echo "<th style='padding: 6px;'>Read</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($recent_result)) {
        echo "<tr>";
        echo "<td style='padding: 6px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 6px;'>" . $row['type'] . "</td>";
        echo "<td style='padding: 6px;'>" . htmlspecialchars(substr($row['title'], 0, 30)) . "...</td>";
        echo "<td style='padding: 6px;'>" . $row['created_at'] . "</td>";
        echo "<td style='padding: 6px;'>" . ($row['is_read'] ? '✅' : '❌') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}
echo "</div>";

echo "<h3>📋 **Files That Will Be Updated**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";

$files_to_update = [
    'merlion/create-admin-notifications-table.php' => 'DELETE - Table creation file',
    'merlion/admin-notifications.php' => 'UPDATE - Remove table creation, keep existing logic',
    'merlion/test-notifications.php' => 'UPDATE - Remove table creation and queries',
    'front-end/send-message.php' => 'UPDATE - Remove admin_notifications INSERT',
    'merlion/create-ticket.php' => 'UPDATE - Remove admin_notifications INSERT',
    'front-end/chat-support.php' => 'UPDATE - Remove admin_notifications INSERT',
    'merlion/admin-chat.php' => 'UPDATE - Remove admin_notifications queries'
];

echo "<h4>Files to be modified:</h4>";
echo "<ol>";
foreach ($files_to_update as $file => $action) {
    $action_color = strpos($action, 'DELETE') !== false ? 'color: #dc3545;' : 'color: #007bff;';
    echo "<li><strong>$file</strong> - <span style='$action_color'>$action</span></li>";
}
echo "</ol>";

echo "<h4>Files that DON'T need changes:</h4>";
echo "<ul>";
echo "<li><strong>front-end/get-user-notifications.php</strong> - Uses chat_messages table (correct)</li>";
echo "<li><strong>front-end/check-notifications.php</strong> - Uses chat_messages table (correct)</li>";
echo "<li><strong>merlion/admin-menu.php</strong> - Uses existing notification endpoints</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🛠️ **Removal Actions**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";

if (isset($_POST['action'])) {
    if ($_POST['action'] === 'remove_table') {
        // Drop the table
        if ($table_exists) {
            $drop_query = "DROP TABLE admin_notifications";
            if (mysqli_query($conn, $drop_query)) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
                echo "<h4>✅ Table Dropped Successfully!</h4>";
                echo "<p>The admin_notifications table has been removed from the database.</p>";
                echo "</div>";
                $table_exists = false;
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0; color: #721c24;'>";
                echo "<h4>❌ Error Dropping Table</h4>";
                echo "<p>Error: " . mysqli_error($conn) . "</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0; color: #856404;'>";
            echo "<h4>⚠️ Table Already Removed</h4>";
            echo "<p>The admin_notifications table doesn't exist.</p>";
            echo "</div>";
        }
    }
}

if ($table_exists) {
    echo "<h4>🗑️ Drop admin_notifications Table:</h4>";
    echo "<form method='POST' style='margin: 10px 0;'>";
    echo "<input type='hidden' name='action' value='remove_table'>";
    echo "<button type='submit' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;' onclick='return confirm(\"Are you sure you want to drop the admin_notifications table? This action cannot be undone.\")'>Drop Table</button>";
    echo "</form>";
    echo "<p style='color: #dc3545; font-size: 12px;'><strong>⚠️ Warning:</strong> This will permanently delete the table and all its data.</p>";
} else {
    echo "<h4 style='color: #155724;'>✅ Table Already Removed</h4>";
    echo "<p style='color: #155724;'>The admin_notifications table has been successfully removed or never existed.</p>";
}
echo "</div>";

echo "<h3>🔧 **Next Steps: Update Code Files**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>After dropping the table, you need to:</h4>";
echo "<ol style='color: #856404;'>";
echo "<li><strong>Update the code files</strong> to remove admin_notifications references</li>";
echo "<li><strong>Test the notification system</strong> to ensure it still works</li>";
echo "<li><strong>Verify admin interface</strong> shows correct notification counts</li>";
echo "</ol>";

echo "<h4>Automatic Code Updates Available:</h4>";
echo "<div style='display: flex; gap: 10px; margin: 10px 0;'>";
echo "<a href='update-admin-notifications-code.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Auto-Update Code Files</a>";
echo "<a href='test-notification-system.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Notification System</a>";
echo "</div>";
echo "</div>";

echo "<h3>📊 **Benefits After Removal**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bee5eb;'>";
echo "<h4 style='color: #0c5460;'>🚀 Performance Benefits:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Reduced database queries:</strong> No more CREATE TABLE IF NOT EXISTS on every request</li>";
echo "<li><strong>Less storage usage:</strong> No redundant notification records</li>";
echo "<li><strong>Faster page loads:</strong> Fewer database operations</li>";
echo "<li><strong>Cleaner code:</strong> Simplified notification logic</li>";
echo "</ul>";

echo "<h4 style='color: #0c5460;'>🛡️ Maintenance Benefits:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li><strong>Less code to maintain:</strong> Fewer database operations</li>";
echo "<li><strong>Consistent notifications:</strong> Single source of truth</li>";
echo "<li><strong>Better performance:</strong> No redundant table creation</li>";
echo "<li><strong>Simplified debugging:</strong> Fewer moving parts</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 **Current Notification System Status**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";

// Test current notification counts
$new_tickets_query = "SELECT COUNT(*) as count FROM support_tickets WHERE is_seen_by_admin = 0";
$new_tickets_result = mysqli_query($conn, $new_tickets_query);
$new_tickets_count = mysqli_fetch_assoc($new_tickets_result)['count'];

$unread_messages_query = "SELECT COUNT(*) as count FROM chat_messages WHERE sender_type = 'user' AND is_read = 0";
$unread_messages_result = mysqli_query($conn, $unread_messages_query);
$unread_messages_count = mysqli_fetch_assoc($unread_messages_result)['count'];

echo "<h4>✅ Working Notification Counts (Without admin_notifications):</h4>";
echo "<ul>";
echo "<li><strong>New Tickets (unseen by admin):</strong> $new_tickets_count</li>";
echo "<li><strong>Unread Messages from Users:</strong> $unread_messages_count</li>";
echo "<li><strong>Total Notifications:</strong> " . ($new_tickets_count + $unread_messages_count) . "</li>";
echo "</ul>";

echo "<p style='color: #155724;'><strong>✅ Perfect!</strong> The notification system works perfectly without the admin_notifications table.</p>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
</style>
