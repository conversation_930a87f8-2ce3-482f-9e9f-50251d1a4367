# GraphQL Ticket Queries

# Query specific tickets (example from user request) - Updated with actual Appika fields
query GetSpecificTickets {
  ticket1: getTicket(id: 1) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    agent_group
    assign2
    subject
    followers
    tags
    type
    type_name
    priority
    status
    creator_by_contact
    creator_by_agent
    ofc_id
    solved_date
    sla_id
    first_response_time
    first_response_due
    first_response
    next_response_time
    next_response_due
    next_response
    resolution_due
    remind_first
    remind_next
    remind_res
    created
    updated
  }
  ticket3: getTicket(id: 3) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    agent_group
    assign2
    subject
    followers
    tags
    type
    type_name
    priority
    status
    creator_by_contact
    creator_by_agent
    ofc_id
    solved_date
    sla_id
    first_response_time
    first_response_due
    first_response
    next_response_time
    next_response_due
    next_response
    resolution_due
    remind_first
    remind_next
    remind_res
    created
    updated
  }
}

# Get single ticket with all fields
query GetTicketDetails($id: Int!) {
  getTicket(id: $id) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
  }
}

# List tickets with pagination
query ListTickets($limit: Int, $offset: Int, $status: String) {
  listTickets(limit: $limit, offset: $offset, status: $status) {
    tickets {
      id
      ticket_no
      req_email
      subject
      priority
      type_name
      status
      created
      updated
    }
    totalCount
    hasNextPage
    hasPreviousPage
  }
}

# Search tickets by criteria
query SearchTickets($searchInput: TicketSearchInput!) {
  searchTickets(input: $searchInput) {
    tickets {
      id
      ticket_no
      req_email
      subject
      priority
      type_name
      status
      created
      updated
    }
    totalCount
  }
}

# Get tickets by status
query GetTicketsByStatus($status: TicketStatus!) {
  getTicketsByStatus(status: $status) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}

# Get tickets by priority
query GetTicketsByPriority($priority: TicketPriority!) {
  getTicketsByPriority(priority: $priority) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}

# Get tickets by type
query GetTicketsByType($type: TicketType!) {
  getTicketsByType(type: $type) {
    id
    ticket_no
    req_email
    subject
    priority
    type_name
    status
    created
    updated
  }
}

# Get user's tickets
query GetUserTickets($userId: Int!, $limit: Int, $offset: Int) {
  getUserTickets(userId: $userId, limit: $limit, offset: $offset) {
    tickets {
      id
      ticket_no
      contact_id
      agent_id
      req_email
      subject
      type
      type_name
      priority
      status
      created
      updated
    }
    totalCount
  }
}

# Variables examples for the queries above:

# For GetTicketDetails:
# {
#   "id": "1"
# }

# For ListTickets:
# {
#   "limit": 10,
#   "offset": 0,
#   "status": "open"
# }

# For SearchTickets:
# {
#   "searchInput": {
#     "subject": "test",
#     "status": "open",
#     "priority": "high"
#   }
# }

# For GetTicketsByStatus:
# {
#   "status": "OPEN"
# }

# For GetTicketsByPriority:
# {
#   "priority": "HIGH"
# }

# For GetTicketsByType:
# {
#   "type": "STARTER"
# }

# For GetUserTickets:
# {
#   "userId": "1",
#   "limit": 10,
#   "offset": 0
# }
