<?php
// Test chat timezone functionality
include('../functions/server.php');
include('../functions/timezone-helper.php');

// Start session for testing
session_start();

echo "<h1>🕐 Chat Timezone Test</h1>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>📋 Current Time Information</h2>";

// Test current UTC time
$current_utc = UTCTimeHelper::getCurrentUTC();
echo "<p><strong>Current UTC Time:</strong> $current_utc</p>";

// Test server timezone
$server_time = date('Y-m-d H:i:s');
echo "<p><strong>Server Time (may be MST):</strong> $server_time</p>";

// Test customer timezone functions
if (isset($_SESSION['user_id'])) {
    $customer_timezone = getCustomerTimezone();
    echo "<p><strong>Customer Timezone:</strong> $customer_timezone</p>";
    
    $customer_time = showCustomerTime($current_utc, 'M j, Y g:i A');
    echo "<p><strong>Customer Time Display:</strong> $customer_time</p>";
} else {
    echo "<p><strong>Customer:</strong> Not logged in - using default timezone</p>";
}

echo "</div>";

echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>🔍 Database Storage Test</h2>";

// Test what would be saved to database
echo "<p><strong>What gets saved to database:</strong></p>";
echo "<ul>";
echo "<li><strong>UTC Time (correct):</strong> " . UTCTimeHelper::getCurrentUTC() . "</li>";
echo "<li><strong>Server Time (wrong):</strong> " . date('Y-m-d H:i:s') . "</li>";
echo "</ul>";

echo "<p><strong>✅ Our chat system now uses:</strong> UTCTimeHelper::getCurrentUTC()</p>";
echo "<p><strong>❌ Before it used:</strong> CURRENT_TIMESTAMP (server timezone)</p>";

echo "</div>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>🌍 Deployment Behavior</h2>";

echo "<p><strong>On localhost (XAMPP):</strong></p>";
echo "<ul>";
echo "<li>Server timezone: " . date_default_timezone_get() . "</li>";
echo "<li>Messages saved in: UTC (using our helper)</li>";
echo "<li>Messages displayed in: User's timezone</li>";
echo "</ul>";

echo "<p><strong>On production server (MST timezone):</strong></p>";
echo "<ul>";
echo "<li>Server timezone: MST (Mountain Standard Time)</li>";
echo "<li>Messages saved in: UTC (using our helper) ✅</li>";
echo "<li>Messages displayed in: User's timezone ✅</li>";
echo "</ul>";

echo "<p><strong>🎯 Result:</strong> Consistent behavior regardless of server timezone!</p>";

echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>✅ What We Fixed</h2>";

echo "<ol>";
echo "<li><strong>Added timezone helper</strong> to chat-support.php and send-message.php</li>";
echo "<li><strong>Changed message storage</strong> to use UTC instead of server time</li>";
echo "<li><strong>Updated time display</strong> to show times in user's timezone</li>";
echo "<li><strong>Added time to 'Created' field</strong> in ticket list</li>";
echo "<li><strong>Fixed JavaScript</strong> to format times properly</li>";
echo "</ol>";

echo "</div>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>⚠️ Important Notes</h2>";

echo "<ul>";
echo "<li><strong>Old messages:</strong> May still be in server timezone (MST)</li>";
echo "<li><strong>New messages:</strong> Will be saved in UTC</li>";
echo "<li><strong>Display:</strong> All times converted to user's timezone</li>";
echo "<li><strong>Production:</strong> Will work correctly regardless of server timezone</li>";
echo "</ul>";

echo "</div>";

// Test sample time conversions
echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h2>🧪 Sample Time Conversions</h2>";

$sample_utc = '2024-12-20 15:30:00'; // 3:30 PM UTC

echo "<p><strong>Sample UTC Time:</strong> $sample_utc</p>";

$timezones = [
    'Asia/Singapore' => 'Singapore Customer',
    'America/New_York' => 'US East Coast Customer', 
    'Europe/London' => 'UK Customer',
    'Australia/Sydney' => 'Australia Customer'
];

foreach ($timezones as $tz => $label) {
    $converted = UTCTimeHelper::formatForDisplay($sample_utc, $tz, 'M j, Y g:i A');
    echo "<p><strong>$label ($tz):</strong> $converted</p>";
}

echo "</div>";

echo "<p><a href='chat-support.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>← Back to Chat Support</a></p>";
?>
