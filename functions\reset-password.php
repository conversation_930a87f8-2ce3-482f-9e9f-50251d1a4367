<?php
session_start();
include('server.php');

// Set content type to JSON
header('Content-Type: application/json');

// Initialize response
$response = array('success' => false, 'message' => '');

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Invalid request method.';
    echo json_encode($response);
    exit();
}

// Get and validate input
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';

// Validation
if (empty($email)) {
    $response['message'] = 'Email address is required.';
    echo json_encode($response);
    exit();
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $response['message'] = 'Please enter a valid email address.';
    echo json_encode($response);
    exit();
}

if (empty($new_password)) {
    $response['message'] = 'New password is required.';
    echo json_encode($response);
    exit();
}

if (strlen($new_password) < 6) {
    $response['message'] = 'Password must be at least 6 characters long.';
    echo json_encode($response);
    exit();
}

try {
    // Check if user exists
    $email_escaped = mysqli_real_escape_string($conn, $email);
    $check_query = "SELECT id, username, email, password FROM user WHERE email = '$email_escaped' LIMIT 1";
    $result = mysqli_query($conn, $check_query);

    if (!$result) {
        $response['message'] = 'Database error occurred. Please try again.';
        echo json_encode($response);
        exit();
    }

    if (mysqli_num_rows($result) === 0) {
        $response['message'] = 'No account found with this email address.';
        echo json_encode($response);
        exit();
    }

    $user = mysqli_fetch_assoc($result);
    $user_id = $user['id'];
    $username = $user['username'];
    $current_password = $user['password'];

    // Determine current password type and hash new password accordingly
    $new_password_hash = '';
    $password_type = '';

    // Check if current password is bcrypt (starts with $2y$ or $2a$)
    if (strpos($current_password, '$2y$') === 0 || strpos($current_password, '$2a$') === 0) {
        // Current password is bcrypt, use bcrypt for new password
        $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
        $password_type = 'bcrypt';
    } else {
        // Current password is MD5 (or other), use MD5 for new password to maintain compatibility
        $new_password_hash = md5($new_password);
        $password_type = 'md5';
    }

    // Update password in database
    $new_password_escaped = mysqli_real_escape_string($conn, $new_password_hash);
    $update_query = "UPDATE user SET password = '$new_password_escaped' WHERE id = $user_id";
    
    if (mysqli_query($conn, $update_query)) {
        // Log the password reset for security
        $log_message = date('Y-m-d H:i:s') . " - Password reset for user: $username (ID: $user_id, Email: $email) - Type: $password_type" . PHP_EOL;
        file_put_contents(__DIR__ . '/password_reset.log', $log_message, FILE_APPEND | LOCK_EX);

        $response['success'] = true;
        $response['message'] = 'Password has been successfully reset! You can now sign in with your new password.';
    } else {
        $response['message'] = 'Failed to update password. Please try again.';
    }

} catch (Exception $e) {
    // Log the error
    error_log("Password reset error: " . $e->getMessage());
    $response['message'] = 'An unexpected error occurred. Please try again later.';
}

echo json_encode($response);
?>
