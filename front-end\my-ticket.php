<?php
include('../functions/server.php');
session_start();

if (!isset($_SESSION['username'])) {
    header("Location: ../index.php");
    exit();
}

$username = $_SESSION['username'];

// ค่าค้นหาจากฟอร์ม
$search = isset($_GET['search']) ? trim($_GET['search']) : "";

// Get filter values from dropdowns
$typeFilter = isset($_GET['type']) ? trim($_GET['type']) : "";
$statusFilter = isset($_GET['status']) ? trim($_GET['status']) : "";
$priorityFilter = isset($_GET['priority']) ? trim($_GET['priority']) : "";
$severityFilter = isset($_GET['severity']) ? trim($_GET['severity']) : "";

// ค้นหาจาก subject, id, ่สร้าง
$searchSql = "";
if (!empty($search)) {
    $searchSafe = mysqli_real_escape_string($conn, $search);
    $searchSql = "AND (
        st.subject LIKE '%$searchSafe%' OR
        st.id LIKE '%$searchSafe%' OR
        DATE(st.created_at) = '$searchSafe' OR
        YEAR(st.created_at) = '$searchSafe' OR
        MONTH(st.created_at) = '$searchSafe' OR
        DAY(st.created_at) = '$searchSafe' OR
        DATE_FORMAT(st.created_at, '%Y-%m') = '$searchSafe' OR
        DATE_FORMAT(st.created_at, '%m-%d') = '$searchSafe'
    )";
}

// Add filter conditions
if (!empty($typeFilter)) {
    $typeFilterSafe = mysqli_real_escape_string($conn, $typeFilter);
    $searchSql .= " AND st.ticket_type = '$typeFilterSafe'";
}

if (!empty($statusFilter)) {
    $statusFilterSafe = mysqli_real_escape_string($conn, $statusFilter);
    $searchSql .= " AND st.status = '$statusFilterSafe'";
}

if (!empty($priorityFilter)) {
    $priorityFilterSafe = mysqli_real_escape_string($conn, $priorityFilter);
    $searchSql .= " AND st.priority = '$priorityFilterSafe'";
}

if (!empty($severityFilter)) {
    $severityFilterSafe = mysqli_real_escape_string($conn, $severityFilter);
    $searchSql .= " AND st.severity = '$severityFilterSafe'";
}

//  user ข้อมูล
$userQuery = "SELECT * FROM user WHERE username = '$username'";
$userResult = mysqli_query($conn, $userQuery);
$user = mysqli_fetch_assoc($userResult);
$userId = $user['id'] ?? 0;

$starterRemaining = $user['starter_tickets'] ?? 0;
$premiumRemaining = $user['premium_tickets'] ?? 0;
$ultimateRemaining = $user['ultimate_tickets'] ?? 0;

// Pagination settings
$itemsPerPage = 5;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
if ($page < 1) $page = 1;
$offset = ($page - 1) * $itemsPerPage;

// Count total tickets for pagination
$countSql = "SELECT COUNT(*) as total FROM support_tickets st
            WHERE st.user_id = $userId $searchSql";
$countResult = mysqli_query($conn, $countSql);
$totalItems = mysqli_fetch_assoc($countResult)['total'];
$totalPages = ceil($totalItems / $itemsPerPage);

// รายการ support tickets ของ้ใช้ with pagination
$sqlTickets = "SELECT * FROM support_tickets st
               WHERE st.user_id = $userId $searchSql
               ORDER BY st.created_at DESC
               LIMIT $itemsPerPage OFFSET $offset";

$resultTickets = mysqli_query($conn, $sqlTickets);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>My Support Tickets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="shortcut icon" href="../image/png/favicon.png" type="image/x-icon">
    <!-- Bootstrap and plugins -->
    <link rel="stylesheet" href="../css/bootstrap.css">
    <link rel="stylesheet" href="../css/main.css">
    <link rel="stylesheet" href="../fonts/icon-font/css/style.css">
    <link rel="stylesheet" href="../fonts/typography-font/typo.css">
    <link rel="stylesheet" href="../fonts/fontawesome-5/css/all.css">
    <link rel="stylesheet" href="../plugins/aos/aos.min.css">
    <link rel="stylesheet" href="../plugins/fancybox/jquery.fancybox.min.css">
    <link rel="stylesheet" href="../plugins/nice-select/nice-select.min.css">
    <link rel="stylesheet" href="../plugins/slick/slick.min.css">
    <link rel="stylesheet" href="../plugins/date-picker/css/gijgo.min.css" type="text/css" />
    <link rel="stylesheet" href="../plugins/theme-mode-switcher/switcher-panel.css">
    <link rel="stylesheet" href="../css/theme-mode-custom.css">
    <style>
    body {
        padding-top: 140px;
        background-color: #F4F7FA;
    }

    @media (max-width: 767px) {
        body {
            margin-top: -20px;
        }

        .badge {
            padding: 6.8px;
            font-size: 14px !important;
        }
    }

    .badge {
        padding: 6.8px;
        font-size: 16px;
    }

    .badge-open {
        background-color: #4CAF50;
        color: #000;
    }

    .badge-in_progress {
        background-color: #2196F3;
        color: #fff;
    }

    .badge-resolved {
        background-color: #9C27B0;
        color: #fff;
    }

    .badge-closed {
        background-color: #757575;
        color: #fff;
    }

    /* Ticket type badges */
    .badge-ultimate {
        background-color: #007BFF;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-starter {
        background-color: #fbbf24;
        color: #000 !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-premium,
    .badge-business {
        background-color: #01A7E1;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .badge-ultimate {
        background-color: #793BF0;
        color: #fff !important;
        font-size: 16px;
        padding: 6.8px;
    }

    .card h3 {
        color: #007bff;
        margin: 0;
    }

    .card h5 {
        font-weight: 600;
        margin-bottom: 10px;
    }

    /* Responsive styles */
    @media (max-width: 991px) {
        body {
            padding-top: 150px;
        }

        .ticket-cards .card {
            margin-bottom: 15px;
        }
    }

    @media (max-width: 767px) {
        body {
            padding-top: 120px;
        }

        .filter-container {
            margin-top: 15px;
            text-align: left !important;
            padding-left: 40px !important;
            padding-right: 40px !important;
        }

        .filter-container select {
            margin-bottom: 10px;
            margin-left: 0 !important;
            margin-right: 5px;
            width: 48% !important;
            display: inline-block !important;

        }

        /* Removed search-container styles as they're now handled by search-filter-row */

        .ticket-cards .col-md-4 {
            margin-bottom: 15px;
        }

        .table-responsive {
            border: 0;
            width: 100%;
            margin-bottom: 15px;
            overflow-y: hidden;
            -ms-overflow-style: -ms-autohiding-scrollbar;
        }

        .pagination-container .pagination {
            flex-wrap: wrap;
            justify-content: center;
        }

        .pagination-container .page-item {
            margin-bottom: 5px;
        }
    }

    @media (max-width: 575px) {
        .create-ticket-btn {
            display: block;
            width: 100%;
            margin-bottom: 20px;
        }

        .table th,
        .table td {
            padding: 0.5rem;
            font-size: 0.85rem;
        }

        .btn-sm {
            padding: 0.25rem 0.4rem;
            font-size: 0.75rem;
        }
    }

    /* Menu open state for mobile */
    body.menu-open {
        overflow: hidden;
    }

    /* edit field here */
    .container {
        width: 1800px;
        max-width: 95%;
    }

    /* Make site-wrapper wider */
    .site-wrapper {
        max-width: 100% !important;
        width: 100% !important;
        overflow-x: visible !important;
    }
    </style>
</head>

<body>
    <?php include('../header-footer/header.php'); ?>

    <div class="container">
        <div class="row">
            <!-- Sidebar - Hidden on mobile, visible on larger screens -->
            <div class="col-lg-3 col-md-4 d-none d-md-block mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Mobile menu - Visible only on mobile -->
            <div class="col-12 d-md-none mb-4">
                <?php include('user-menu.php'); ?>
            </div>

            <!-- Main content area -->
            <div class="col-lg-9 col-md-8">
                <div class="cart-details-main-block" id="dynamic-cart">
                    <!-- White background card with rounded corners -->
                    <div class="bg-white p-8 rounded-10 mb-5 overflow-hidden position-relative">
                        <h2 class="text-center mb-4">My Support Tickets</h2>

                        <!-- Login Success Message -->
                        <?php if (isset($_GET['login_success']) && $_GET['login_success'] == '1'): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            <strong>Welcome back!</strong> You have successfully signed in. Your support tickets are now available below.
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        <?php endif; ?>

                        <!-- Timezone indicator -->
                        <div class="text-center mb-3">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i>
                                Times shown in your timezone: <span id="customer-timezone-display"><?php echo getCustomerTimezone(); ?></span>
                            </small>
                        </div>

                        <!-- add new ticket button -->
                        <div class="text-right mb-3">
                            <a href="create-ticket.php" class="btn create-ticket-btn"
                                style="background-color: #473BF0; color: white; padding: 0.5em 1em; font-size: 0.9em; border-radius: 5px; text-decoration: none;">Create
                                New Ticket</a>
                        </div>


                        <!-- Remaining Tickets Dashboard -->
                        <div class="row text-center mb-4 ticket-cards">
                            <div class="col-md-4 col-sm-6">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Starter Tickets</h5>
                                        <h3><?php echo $starterRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-6">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Business Tickets</h5>
                                        <h3><?php echo $premiumRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 col-sm-12">
                                <div class="card shadow-sm">
                                    <div class="card-body">
                                        <h5>Remaining Ultimate Tickets</h5>
                                        <h3><?php echo $ultimateRemaining; ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br>

                        <style>
                        .form-control,
                        .btn,
                        .filter-select {
                            font-size: 14px;
                            /* Reduced from 16px */
                            height: 40px !important;
                        }

                        .filter-select {
                            font-size: 14px;
                            height: 40px !important;
                            /* width: 120px !important; */
                        }

                        /* Additional responsive styles for the search form and filters */
                        @media (min-width: 992px) {

                            /* Desktop-specific styles */
                            .search-filter-row {
                                justify-content: flex-start !important;
                                /* Left align on desktop */
                                /* padding-left: 20px; */
                                margin-left: -10px;
                                /* Add some padding on the left */
                            }

                            .search-box .input-group {
                                margin-left: 0;
                                /* Ensure left alignment on desktop */
                            }

                            /* Filter row styles for desktop */
                            .filter-row-container {
                                margin-top: 15px;
                                margin-bottom: 20px;
                            }

                            .filter-item {
                                padding: 0 5px;
                            }

                            .filter-select {
                                width: 100% !important;
                                border-radius: 4px;
                            }
                        }

                        @media (max-width: 576px) {
                            .search-filter-row {
                                margin: 0 -10px;
                            }

                            h2 {
                                font-size: 1.5rem;
                            }
                        }

                        .filter-container select {
                            width: auto !important;
                            padding: 4px 8px;
                            /* Smaller padding */
                            height: 40px !important;
                            /* Allow height to adjust to content */
                        }

                        /* Removed search-container styles as they're now handled by search-box */

                        .d-flex.flex-wrap.gap-2 {
                            gap: 10px;
                        }

                        @media (max-width: 767px) {
                            .filter-container select {
                                width: 46% !important;
                                /* Slightly smaller on mobile */
                                font-size: 13px;
                                /* Even smaller font on mobile */
                            }
                        }

                        /* Search bar styles from my-ticket-log.php - modified for left alignment */
                        .search-filter-row {
                            padding: 0 40px;
                        }

                        .search-box {
                            width: auto;
                        }

                        .search-box .input-group {
                            width: 550px !important;
                            max-width: 100% !important;
                            margin-left: 0;
                            /* Align to the left */
                        }

                        .search-input {
                            border-top-right-radius: 0 !important;
                            border-bottom-right-radius: 0 !important;
                            height: 38px;
                            font-size: 14px;
                        }

                        .search-button {
                            border-top-right-radius: 4px !important;
                            border-bottom-right-radius: 4px !important;
                            border-top-left-radius: 0 !important;
                            border-bottom-left-radius: 0 !important;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            padding: 0.375rem 0.75rem;
                            width: 40px !important;
                            min-width: 40px !important;
                        }

                        /* Hide the word "Search" on smaller screens */
                        @media (max-width: 767px) {
                            .search-button span {
                                display: none;
                            }

                            .search-button i {
                                margin-right: 0;
                            }

                            .search-filter-row {
                                padding: 0 15px;
                            }

                            .search-box .input-group {
                                width: 100% !important;
                                max-width: 100% !important;
                                margin: 0 auto;
                                /* Center on mobile */
                            }

                            .search-filter-row {
                                justify-content: center !important;
                                /* Center on mobile */
                            }

                            .search-input {
                                font-size: 16px;
                                /* Larger font size for better mobile readability */
                            }
                        }

                        /* Extra small devices */
                        @media (max-width: 480px) {
                            .search-filter-row {
                                padding: 0 10px;
                            }

                            .search-input::placeholder {
                                font-size: 14px;
                            }

                            ul.pagination.justify-content-center {
                                font-size: 14px !important;
                            }
                        }

                        .filter-container select {
                            height: 38px;
                            font-size: 14px;
                            min-width: 120px;
                            margin-right: 8px;
                            margin-bottom: 8px;
                        }

                        /* Corner styling for filter selects */
                        .filter-select.top-left {
                            border-top-left-radius: 4px;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.top-right {
                            border-top-left-radius: 0;
                            border-top-right-radius: 4px;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.bottom-left {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 4px;
                            border-bottom-right-radius: 0;
                        }

                        .filter-select.bottom-right {
                            border-top-left-radius: 0;
                            border-top-right-radius: 0;
                            border-bottom-left-radius: 0;
                            border-bottom-right-radius: 4px;
                        }


                        /* Responsive adjustments */
                        @media (min-width: 768px) and (max-width: 991px) {

                            /* Tablet-specific styles */
                            .search-filter-row {
                                justify-content: flex-start !important;
                                /* Left align on tablets */
                            }

                            .search-box .input-group {
                                margin-left: 0;
                                /* Ensure left alignment on tablets */
                                width: 450px !important;
                                /* Slightly smaller on tablets */
                            }

                            /* Filter row styles for tablets */
                            .filter-row-container {
                                margin-top: 15px;
                                margin-bottom: 20px;
                            }

                            .filter-item {
                                padding: 0 5px;
                            }

                            .filter-select {
                                width: 100% !important;
                                border-radius: 4px;
                                font-size: 13px;
                            }
                        }

                        @media (max-width: 767px) {
                            .filter-container {
                                padding-left: 15px !important;
                                padding-right: 15px !important;
                            }

                            /* Mobile styles for filter row */
                            .filter-row-container .row {
                                margin: 0;
                            }

                            .filter-item {
                                margin-bottom: 10px;
                            }

                            .filter-select {
                                width: 100% !important;
                                font-size: 13px;
                                border-radius: 4px !important;
                            }
                        }

                        @media (max-width: 575px) {
                            .filter-row-container {
                                padding: 0 10px;
                            }

                            .filter-select {
                                font-size: 12px;
                            }
                        }

                        /* Table styles */
                        .table td {
                            vertical-align: middle;
                        }

                        /* Subject cell styles */
                        td:nth-child(3) {
                            text-align: left !important;
                            padding-left: 15px !important;
                            max-width: 40%;
                            white-space: normal;
                        }

                        .subject-content {
                            overflow: hidden;
                            text-overflow: ellipsis;
                            max-height: 2.4em;
                            line-height: 1.2;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                        }

                        /* Details button styles */
                        td:nth-child(8) .btn {
                            padding: 0.15rem 0.4rem;
                            font-size: 11px;
                            min-width: 40px;
                            max-width: 40px;
                            height: 24px;
                            line-height: 1;
                        }

                        /* Mobile styles for table */
                        @media (max-width: 767px) {
                            td:nth-child(3) {
                                max-width: 45%;
                            }

                            td:nth-child(8) .btn {
                                padding: 0.1rem 0.3rem;
                                font-size: 10px;
                                min-width: 35px;
                                max-width: 35px;
                                height: 22px;
                            }
                        }
                        </style>

                        <!-- Search form and filters -->
                        <form method="GET">
                            <div class="mb-3" style="padding: 0 0px align-items-center;">
                                <!-- Search bar -->
                                <div class="mb-3">
                                    <div class="search-filter-row d-flex justify-content-start flex-wrap">
                                        <div class="search-box w-100">
                                            <div class="input-group">
                                                <input type="text" name="search" class="form-control search-input"
                                                    placeholder="Search by ID, subject or date..."
                                                    value="<?php echo htmlspecialchars($search); ?>">
                                                <div class="input-group-append">
                                                    <button type="submit" class="btn btn-primary search-button">
                                                        <i class="fas fa-search"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Filters row -->
                                <div class="filter-row-container">
                                    <div class="row mx-auto" style="max-width: 95%;">
                                        <!-- Filter 1 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="type" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Types</option>
                                                <option value="starter"
                                                    <?php echo $typeFilter === 'starter' ? 'selected' : ''; ?>>Starter
                                                </option>
                                                <option value="premium"
                                                    <?php echo $typeFilter === 'premium' ? 'selected' : ''; ?>>Business
                                                </option>
                                                <option value="ultimate"
                                                    <?php echo $typeFilter === 'ultimate' ? 'selected' : ''; ?>>Ultimate
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Filter 2 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="status" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Statuses</option>
                                                <option value="open"
                                                    <?php echo $statusFilter === 'open' ? 'selected' : ''; ?>>Open
                                                </option>
                                                <option value="in_progress"
                                                    <?php echo $statusFilter === 'in_progress' ? 'selected' : ''; ?>>In
                                                    Progress</option>
                                                <option value="resolved"
                                                    <?php echo $statusFilter === 'resolved' ? 'selected' : ''; ?>>
                                                    Resolved
                                                </option>
                                                <option value="closed"
                                                    <?php echo $statusFilter === 'closed' ? 'selected' : ''; ?>>Closed
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Filter 3 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="priority" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Priorities</option>
                                                <option value="low"
                                                    <?php echo $priorityFilter === 'low' ? 'selected' : ''; ?>>
                                                    Information
                                                </option>
                                                <option value="medium"
                                                    <?php echo $priorityFilter === 'medium' ? 'selected' : ''; ?>>Minor
                                                </option>
                                                <option value="high"
                                                    <?php echo $priorityFilter === 'high' ? 'selected' : ''; ?>>
                                                    Important
                                                </option>
                                                <option value="critical"
                                                    <?php echo $priorityFilter === 'critical' ? 'selected' : ''; ?>>
                                                    Critical
                                                </option>
                                            </select>
                                        </div>

                                        <!-- Filter 4 -->
                                        <div class="col-md-3 filter-item">
                                            <select name="severity" class="form-control filter-select"
                                                onchange="this.form.submit()">
                                                <option value="">All Severities</option>
                                                <option value="Low"
                                                    <?php echo $severityFilter === 'Low' ? 'selected' : ''; ?>>
                                                    Information
                                                </option>
                                                <option value="Normal"
                                                    <?php echo $severityFilter === 'Normal' ? 'selected' : ''; ?>>Minor
                                                </option>
                                                <option value="High"
                                                    <?php echo $severityFilter === 'High' ? 'selected' : ''; ?>>
                                                    Important
                                                </option>
                                                <option value="Critical"
                                                    <?php echo $severityFilter === 'Critical' ? 'selected' : ''; ?>>
                                                    Critical
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>


                        <?php if ($resultTickets && mysqli_num_rows($resultTickets) > 0) : ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-striped text-center mx-auto"
                                style="max-width: 95%;">
                                <thead class="thead-light">
                                    <tr>
                                        <th style="width: 10%;">Created</th>
                                        <th style="width: 5%;">ID</th>
                                        <th style="width: 40%;">Subject</th>
                                        <th style="width: 8%;">Type</th>
                                        <th style="width: 8%;">Status</th>
                                        <th style="width: 8%;">Priority</th>
                                        <th style="width: 8%;">Severity</th>
                                        <th style="width: 6%;">Details</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php while ($ticket = mysqli_fetch_assoc($resultTickets)) : ?>
                                    <tr>
                                        <td><?php echo showCustomerTimeSimple($ticket['created_at']); ?></td>
                                        <td><?php echo $ticket['id']; ?></td>
                                        <td>
                                            <div class="subject-content">
                                                <?php echo htmlspecialchars($ticket['subject']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                                    // Display 'Business' for premium tickets
                                                    $ticketType = $ticket['ticket_type'];
                                                    $badgeClass = $ticketType;
                                                    $displayText = ucfirst($ticketType);

                                                    if (strtolower($ticketType) == 'premium') {
                                                        $displayText = 'Business';
                                                    }
                                                    ?>
                                            <span class="badge badge-<?php echo $badgeClass; ?>">
                                                <?php echo $displayText; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-<?php echo $ticket['status']; ?>">
                                                <?php echo ucfirst($ticket['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo ucfirst($ticket['priority']); ?></td>
                                        <td><?php echo ucfirst($ticket['severity']); ?></td>
                                        <td>
                                            <a href="ticket-detail.php?id=<?php echo $ticket['id']; ?>"
                                                class="btn btn-sm btn-outline-primary details-btn">View</a>
                                        </td>
                                    </tr>
                                    <?php endwhile; ?>
                                </tbody>
                            </table>

                            <!-- Pagination Controls -->
                            <?php if ($totalPages > 1): ?>
                            <div class="pagination-container mt-4">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination justify-content-center">
                                        <!-- Previous Button -->
                                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                            <a class="page-link"
                                                href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>"
                                                aria-label="Previous">
                                                <span aria-hidden="true">&laquo; Previous</span>
                                            </a>
                                        </li>

                                        <!-- Page Numbers -->
                                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                        <li class="page-item <?php echo ($page == $i) ? 'active' : ''; ?>">
                                            <a class="page-link"
                                                href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                        <?php endfor; ?>

                                        <!-- Next Button -->
                                        <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                                            <a class="page-link"
                                                href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($typeFilter) ? '&type=' . urlencode($typeFilter) : ''; ?><?php echo !empty($statusFilter) ? '&status=' . urlencode($statusFilter) : ''; ?><?php echo !empty($priorityFilter) ? '&priority=' . urlencode($priorityFilter) : ''; ?><?php echo !empty($severityFilter) ? '&severity=' . urlencode($severityFilter) : ''; ?>"
                                                aria-label="Next">
                                                <span aria-hidden="true">Next &raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <?php endif; ?>
                        </div>
                        <?php else : ?>
                        <div class="text-center mt-5">
                            <?php if (!empty($search) || !empty($typeFilter) || !empty($statusFilter) || !empty($priorityFilter) || !empty($severityFilter)) : ?>
                            <p class="mb-3">No support tickets found with the current filters.</p>
                            <p><a href="my-ticket.php" class="btn btn-outline-primary">Clear all filters</a></p>
                            <?php else : ?>
                            <p>No support tickets found.</p>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/vendor.min.js"></script>
    <script src="../plugins/theme-mode-switcher/gr-theme-mode-switcher.js"></script>

    <!-- At the very end of the file, just before the closing </body> tag -->
    <script>
    // Direct initialization for the user menu on my-ticket.php
    (function() {
        console.log('Initializing menu on my-ticket.php');

        // Function to initialize the menu
        function initUserMenu() {
            console.log('Running menu initialization');

            // Get menu elements - use more specific selectors
            var menuTitles = document.querySelectorAll('.dashboard-menu .menu-title');

            if (menuTitles.length === 0) {
                console.error('Menu titles not found');
                return;
            }

            console.log('Found ' + menuTitles.length + ' menu titles');

            // Setup each menu (there might be multiple on mobile/desktop)
            menuTitles.forEach(function(menuTitle) {
                var menuContainer = menuTitle.closest('.dashboard-menu');
                if (!menuContainer) return;

                var menuToggle = menuContainer.querySelector('.mobile-menu-toggle');
                var menuItems = menuContainer.querySelector('.menu-items');

                if (!menuToggle || !menuItems) {
                    console.error('Menu components not found');
                    return;
                }

                // Toggle function
                function toggleMenu(e) {
                    console.log('Toggle menu clicked');
                    e.preventDefault();
                    e.stopPropagation();
                    menuItems.classList.toggle('expanded');
                    menuToggle.classList.toggle('active');
                }

                // Remove existing handler and add new one
                menuTitle.onclick = null;
                menuTitle.onclick = toggleMenu;

                // Check for active items
                var hasActiveItem = menuItems.querySelector('.active');
                if (hasActiveItem && window.innerWidth <= 767) {
                    console.log('Auto-expanding menu with active item');
                    menuItems.classList.add('expanded');
                    menuToggle.classList.add('active');
                }

                console.log('Menu setup complete for one container');
            });
        }

        // Run immediately
        initUserMenu();

        // Also run when DOM is fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initUserMenu);
        }

        // Run again after delays to catch any late rendering
        setTimeout(initUserMenu, 100);
        setTimeout(initUserMenu, 500);
        setTimeout(initUserMenu, 1000);

        // Try to use the global function if available
        if (window.initUserMenu) {
            setTimeout(window.initUserMenu, 200);
            setTimeout(window.initUserMenu, 600);
        }
    })();
    </script>

    <!-- Customer Timezone Detection -->
    <script src="../js/customer-timezone.js"></script>

    <!-- Update timezone display when detected -->
    <script>
    document.addEventListener('customerTimezoneDetected', function(event) {
        const timezoneDisplay = document.getElementById('customer-timezone-display');
        if (timezoneDisplay) {
            timezoneDisplay.textContent = event.detail.timezone;
        }
        console.log('Customer timezone updated in my-ticket:', event.detail.timezone);
    });

    // Also update on page load if timezone is already available
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            if (window.CustomerTimezone) {
                const timezone = window.CustomerTimezone.getCustomerTimezone();
                const timezoneDisplay = document.getElementById('customer-timezone-display');
                if (timezoneDisplay && timezone) {
                    timezoneDisplay.textContent = timezone;
                }
            }
        }, 1000);
    });
    </script>

    <!-- Auto-hide login success message -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-hide login success alert after 5 seconds
        const loginSuccessAlert = document.querySelector('.alert-success[role="alert"]');
        if (loginSuccessAlert) {
            setTimeout(function() {
                // Use Bootstrap's fade out animation
                loginSuccessAlert.classList.remove('show');
                // Remove from DOM after animation completes
                setTimeout(function() {
                    if (loginSuccessAlert.parentNode) {
                        loginSuccessAlert.parentNode.removeChild(loginSuccessAlert);
                    }
                }, 150); // Bootstrap fade animation duration
            }, 5000); // 5 seconds
        }
    });
    </script>

</body>

</html>