<?php
session_start();
include('server.php');

// Set content type to JSON for AJAX requests
if (isset($_POST['action'])) {
    header('Content-Type: application/json');
}

// Handle AJAX requests for debugging
if (isset($_POST['action']) && $_POST['action'] === 'debug_signin') {
    $username_or_email = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username_or_email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'Username and password required']);
        exit();
    }
    
    $debug_info = [];
    $debug_info['input'] = [
        'username_or_email' => $username_or_email,
        'password' => $password,
        'is_email' => strpos($username_or_email, '@') !== false
    ];
    
    // Step 1: Check user table
    $is_email = strpos($username_or_email, '@') !== false;
    $field = $is_email ? 'email' : 'username';
    
    $stmt = $conn->prepare("SELECT id, username, email, password FROM user WHERE $field = ? LIMIT 1");
    $stmt->bind_param("s", $username_or_email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();
        $debug_info['user_found'] = true;
        $debug_info['user_data'] = [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'password_hash' => $user['password'],
            'hash_type' => (strpos($user['password'], '$2y$') === 0 || strpos($user['password'], '$2a$') === 0) ? 'bcrypt' : 'md5'
        ];
        
        // Test password verification
        $stored_hash = $user['password'];
        $verification_results = [];
        
        if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
            // bcrypt
            $verification_results['bcrypt_direct'] = password_verify($password, $stored_hash);
        } else {
            // md5
            $verification_results['md5_direct'] = ($stored_hash === md5($password));
        }
        
        // Check payment_temp for this user
        $temp_stmt = $conn->prepare("SELECT password FROM payment_temp WHERE (username = ? OR email = ?) ORDER BY id DESC LIMIT 1");
        $temp_stmt->bind_param("ss", $username_or_email, $username_or_email);
        $temp_stmt->execute();
        $temp_result = $temp_stmt->get_result();
        
        if ($temp_result->num_rows > 0) {
            $temp_data = $temp_result->fetch_assoc();
            $temp_password = $temp_data['password'];
            $debug_info['payment_temp_found'] = true;
            $debug_info['payment_temp_password'] = $temp_password;
            $debug_info['password_matches_temp'] = ($password === $temp_password);
            
            // Test if temp password verifies against stored hash
            if (strpos($stored_hash, '$2y$') === 0 || strpos($stored_hash, '$2a$') === 0) {
                $verification_results['bcrypt_temp'] = password_verify($temp_password, $stored_hash);
            } else {
                $verification_results['md5_temp'] = ($stored_hash === md5($temp_password));
            }
        } else {
            $debug_info['payment_temp_found'] = false;
        }
        
        $debug_info['verification_results'] = $verification_results;
        
    } else {
        $debug_info['user_found'] = false;
        
        // Check if user exists in payment_temp only
        $temp_stmt = $conn->prepare("SELECT * FROM payment_temp WHERE (username = ? OR email = ?) AND password = ? ORDER BY id DESC LIMIT 1");
        $temp_stmt->bind_param("sss", $username_or_email, $username_or_email, $password);
        $temp_stmt->execute();
        $temp_result = $temp_stmt->get_result();
        
        if ($temp_result->num_rows > 0) {
            $debug_info['temp_only'] = true;
            $debug_info['temp_data'] = $temp_result->fetch_assoc();
        } else {
            $debug_info['temp_only'] = false;
        }
    }
    
    echo json_encode(['success' => true, 'debug' => $debug_info]);
    exit();
}

// Handle manual user creation for testing
if (isset($_POST['action']) && $_POST['action'] === 'create_test_user') {
    $username = $_POST['username'] ?? '';
    $email = $_POST['email'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($email) || empty($password)) {
        echo json_encode(['success' => false, 'message' => 'All fields required']);
        exit();
    }
    
    try {
        // Create user with bcrypt password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $registration_time = date('Y-m-d H:i:s');
        
        $stmt = $conn->prepare("INSERT INTO user (username, email, password, registration_time, first_name, last_name) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $username, $email, $hashed_password, $registration_time, $username, '');
        $stmt->execute();
        $user_id = $conn->insert_id;
        
        // Also add to payment_temp
        $temp_stmt = $conn->prepare("INSERT INTO payment_temp (session_id, username, email, password) VALUES (?, ?, ?, ?)");
        $session_id = 'test_' . time();
        $temp_stmt->bind_param("ssss", $session_id, $username, $email, $password);
        $temp_stmt->execute();
        
        echo json_encode([
            'success' => true, 
            'message' => 'Test user created successfully',
            'user_id' => $user_id,
            'username' => $username,
            'email' => $email,
            'password' => $password
        ]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
    exit();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Sign-In Issue - HelloIT</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: Arial, sans-serif;
        padding: 20px;
    }

    .debug-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .debug-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .debug-result {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        padding: 15px;
        margin: 10px 0;
        font-family: monospace;
        font-size: 13px;
        white-space: pre-wrap;
    }

    .success-result {
        background: #d4edda;
        border-color: #c3e6cb;
        color: #155724;
    }

    .error-result {
        background: #f8d7da;
        border-color: #f5c6cb;
        color: #721c24;
    }
    </style>
</head>

<body>
    <div class="debug-container">
        <h1 class="text-center mb-4">Debug Sign-In Issue</h1>
        
        <div class="alert alert-warning">
            <h5><i class="fas fa-bug me-2"></i>Debug Tool</h5>
            <p class="mb-0">This tool helps debug sign-in issues by showing exactly what happens during authentication.</p>
        </div>

        <div class="debug-section">
            <h3>Test Sign-In Authentication</h3>
            <form id="debugForm">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username or Email</label>
                            <input type="text" class="form-control" id="username" placeholder="Enter username or email" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="text" class="form-control" id="password" placeholder="Enter password" required>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search me-2"></i>Debug Authentication
                </button>
            </form>
        </div>

        <div id="debugResults" class="debug-section" style="display: none;">
            <h3>Debug Results</h3>
            <div id="debugContent"></div>
        </div>

        <div class="debug-section">
            <h3>Create Test User</h3>
            <p>Create a test user to verify the authentication flow:</p>
            <form id="createUserForm">
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="testUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="testUsername" placeholder="testuser123" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="testEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="testEmail" placeholder="<EMAIL>" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="testPassword" class="form-label">Password</label>
                            <input type="text" class="form-control" id="testPassword" placeholder="testpass123" required>
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-success">
                    <i class="fas fa-user-plus me-2"></i>Create Test User
                </button>
            </form>
        </div>

        <div class="debug-section">
            <h3>Recent Webhook Data</h3>
            <p>Based on the latest webhook logs, the most recent user created was:</p>
            <div class="alert alert-info">
                <strong>User ID:</strong> 80<br>
                <strong>Username:</strong> user71085<br>
                <strong>Email:</strong> (from webhook session)<br>
                <strong>Session:</strong> cs_test_b10DARd1MoZZW4WkS1FVXjGvDkj78Zeue7gbkOI3Z8aHxxN8JslAbI5F2q
            </div>
            <p>Try debugging with username <code>user71085</code> and the password from payment_temp table.</p>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    document.getElementById('debugForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        fetch('debug-signin-issue.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=debug_signin&username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`
        })
        .then(response => response.json())
        .then(data => {
            displayDebugResults(data);
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred during debugging');
        });
    });

    document.getElementById('createUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('testUsername').value;
        const email = document.getElementById('testEmail').value;
        const password = document.getElementById('testPassword').value;
        
        fetch('debug-signin-issue.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=create_test_user&username=${encodeURIComponent(username)}&email=${encodeURIComponent(email)}&password=${encodeURIComponent(password)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Test user created successfully!\n\nUsername: ' + data.username + '\nEmail: ' + data.email + '\nPassword: ' + data.password);
                document.getElementById('createUserForm').reset();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while creating user');
        });
    });

    function displayDebugResults(data) {
        const resultsDiv = document.getElementById('debugResults');
        const contentDiv = document.getElementById('debugContent');
        
        if (data.success) {
            const debug = data.debug;
            let html = `<div class="debug-result">`;
            html += `<strong>DEBUG INFORMATION:</strong>\n\n`;
            html += `Input Data:\n`;
            html += `- Username/Email: ${debug.input.username_or_email}\n`;
            html += `- Password: ${debug.input.password}\n`;
            html += `- Is Email: ${debug.input.is_email}\n\n`;
            
            if (debug.user_found) {
                html += `User Found in Database:\n`;
                html += `- ID: ${debug.user_data.id}\n`;
                html += `- Username: ${debug.user_data.username}\n`;
                html += `- Email: ${debug.user_data.email}\n`;
                html += `- Hash Type: ${debug.user_data.hash_type}\n`;
                html += `- Password Hash: ${debug.user_data.password_hash.substring(0, 30)}...\n\n`;
                
                html += `Password Verification Results:\n`;
                Object.keys(debug.verification_results).forEach(key => {
                    const result = debug.verification_results[key];
                    html += `- ${key}: ${result ? '✅ PASS' : '❌ FAIL'}\n`;
                });
                html += `\n`;
                
                if (debug.payment_temp_found) {
                    html += `Payment Temp Data Found:\n`;
                    html += `- Temp Password: ${debug.payment_temp_password}\n`;
                    html += `- Password Matches Temp: ${debug.password_matches_temp ? '✅ YES' : '❌ NO'}\n\n`;
                } else {
                    html += `Payment Temp Data: ❌ NOT FOUND\n\n`;
                }
            } else {
                html += `User NOT Found in Main Database\n\n`;
                
                if (debug.temp_only) {
                    html += `Found in Payment Temp Only:\n`;
                    html += `- Session ID: ${debug.temp_data.session_id}\n`;
                    html += `- Username: ${debug.temp_data.username}\n`;
                    html += `- Email: ${debug.temp_data.email}\n`;
                    html += `- Password: ${debug.temp_data.password}\n`;
                    html += `\nThis means webhook hasn't processed yet!\n`;
                } else {
                    html += `Not found in Payment Temp either.\n`;
                }
            }
            
            html += `</div>`;
            contentDiv.innerHTML = html;
        } else {
            contentDiv.innerHTML = `<div class="debug-result error-result">Error: ${data.message}</div>`;
        }
        
        resultsDiv.style.display = 'block';
    }
    </script>
</body>
</html>
