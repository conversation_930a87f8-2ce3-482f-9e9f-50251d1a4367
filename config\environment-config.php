<?php
/**
 * SMART ENVIRONMENT CONFIGURATION
 * Automatically detects localhost vs production and provides appropriate settings
 */

class EnvironmentConfig {
    private static $environment = null;
    private static $config = null;
    
    /**
     * Detect if running on localhost (XAMPP) or production
     */
    public static function isLocalhost() {
        if (self::$environment !== null) {
            return self::$environment === 'localhost';
        }
        
        $localhost_indicators = [
            $_SERVER['HTTP_HOST'] === 'localhost',
            strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false,
            strpos($_SERVER['HTTP_HOST'], 'localhost:') !== false,
            $_SERVER['SERVER_NAME'] === 'localhost',
            isset($_SERVER['XAMPP_VERSION']), // XAMPP specific
            strpos($_SERVER['SERVER_SOFTWARE'], 'Apache') !== false && strpos($_SERVER['DOCUMENT_ROOT'], 'xampp') !== false
        ];
        
        $is_localhost = in_array(true, $localhost_indicators, true);
        self::$environment = $is_localhost ? 'localhost' : 'production';
        
        return $is_localhost;
    }
    
    /**
     * Get environment name
     */
    public static function getEnvironment() {
        self::isLocalhost(); // Ensure environment is detected
        return self::$environment;
    }
    
    /**
     * Get configuration for current environment
     */
    public static function getConfig() {
        if (self::$config !== null) {
            return self::$config;
        }
        
        if (self::isLocalhost()) {
            // LOCALHOST (XAMPP) CONFIGURATION
            self::$config = [
                'environment' => 'localhost',
                'stripe' => [
                    'api_key' => 'sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju',
                    'webhook_secret' => 'whsec_cbc364c427a670f4cfa20d246bed5586d004787966f1946a480afb76031bc8f1'
                ],
                'urls' => [
                    'base_url' => 'http://localhost/helloit',
                    'webhook_url' => 'http://localhost/helloit/front-end/stripe-webhook.php',
                    'rewrite_base' => '/helloit/'
                ],
                'database' => [
                    // Uses existing server.php configuration for localhost
                    'use_server_php' => true
                ]
            ];
        } else {
            // PRODUCTION (helloit.io) CONFIGURATION
            self::$config = [
                'environment' => 'production',
                'stripe' => [
                    'api_key' => 'sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju', // Update with live key when ready
                    'webhook_secret' => 'whsec_kGFRTVhhRpyyhBlLN2MqcKebEzgvu0X3'
                ],
                'urls' => [
                    'base_url' => 'https://helloit.io',
                    'webhook_url' => 'https://helloit.io/front-end/stripe-webhook.php',
                    'rewrite_base' => '/'
                ],
                'database' => [
                    // Uses existing server.php configuration for production
                    'use_server_php' => true
                ]
            ];
        }
        
        return self::$config;
    }
    
    /**
     * Get Stripe configuration
     */
    public static function getStripeConfig() {
        $config = self::getConfig();
        return $config['stripe'];
    }
    
    /**
     * Get URL configuration
     */
    public static function getUrlConfig() {
        $config = self::getConfig();
        return $config['urls'];
    }
    
    /**
     * Get base URL for the current environment
     */
    public static function getBaseUrl() {
        $config = self::getConfig();
        return $config['urls']['base_url'];
    }
    
    /**
     * Get webhook URL for the current environment
     */
    public static function getWebhookUrl() {
        $config = self::getConfig();
        return $config['urls']['webhook_url'];
    }
    
    /**
     * Log environment detection for debugging
     */
    public static function logEnvironmentInfo($log_file = null) {
        if (!$log_file) {
            $log_file = __DIR__ . '/../front-end/webhook.log';
        }
        
        $config = self::getConfig();
        $log_entry = date('c') . ' - Environment Detection:' . PHP_EOL;
        $log_entry .= '  Environment: ' . $config['environment'] . PHP_EOL;
        $log_entry .= '  HTTP_HOST: ' . ($_SERVER['HTTP_HOST'] ?? 'not set') . PHP_EOL;
        $log_entry .= '  SERVER_NAME: ' . ($_SERVER['SERVER_NAME'] ?? 'not set') . PHP_EOL;
        $log_entry .= '  Base URL: ' . $config['urls']['base_url'] . PHP_EOL;
        $log_entry .= '  Webhook URL: ' . $config['urls']['webhook_url'] . PHP_EOL;
        $log_entry .= '  Stripe Webhook Secret: ' . substr($config['stripe']['webhook_secret'], 0, 15) . '...' . PHP_EOL;
        
        file_put_contents($log_file, $log_entry, FILE_APPEND);
    }
}

// Helper functions for backward compatibility
function isLocalhost() {
    return EnvironmentConfig::isLocalhost();
}

function getEnvironmentConfig() {
    return EnvironmentConfig::getConfig();
}

function getStripeConfig() {
    return EnvironmentConfig::getStripeConfig();
}
?>
