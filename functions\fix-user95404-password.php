<?php
include('server.php');

echo "<h2>🔧 Fix user95404 Password Issue</h2>";

$username = 'user95404';

// Get user data
$user_query = "SELECT id, username, email, password FROM user WHERE username = ?";
$user_stmt = $conn->prepare($user_query);
$user_stmt->bind_param("s", $username);
$user_stmt->execute();
$user_result = $user_stmt->get_result();

if ($user_result->num_rows > 0) {
    $user_data = $user_result->fetch_assoc();
    $stored_hash = $user_data['password'];
    
    echo "<h3>Current Status:</h3>";
    echo "<p><strong>Username:</strong> " . $user_data['username'] . "</p>";
    echo "<p><strong>Email:</strong> " . $user_data['email'] . "</p>";
    echo "<p><strong>Stored Hash:</strong> " . substr($stored_hash, 0, 50) . "...</p>";
    
    // Get current payment_temp password
    $temp_query = "SELECT password FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1";
    $temp_stmt = $conn->prepare($temp_query);
    $temp_stmt->bind_param("s", $username);
    $temp_stmt->execute();
    $temp_result = $temp_stmt->get_result();
    
    if ($temp_result->num_rows > 0) {
        $temp_data = $temp_result->fetch_assoc();
        $current_temp_password = $temp_data['password'];
        
        echo "<p><strong>Current payment_temp password:</strong> '$current_temp_password'</p>";
        
        // Test if current password works
        $current_verify = password_verify($current_temp_password, $stored_hash);
        echo "<p><strong>Current password verification:</strong> " . ($current_verify ? '✅ WORKS' : '❌ FAILS') . "</p>";
        
        if (!$current_verify) {
            echo "<h3>🔧 Applying Fix:</h3>";
            
            // Strategy 1: Try to find a password that works with the hash
            echo "<h4>Strategy 1: Testing common passwords</h4>";
            $test_passwords = [
                $current_temp_password,
                'dc6fb0b1',
                bin2hex(random_bytes(4)),
                'password123',
                'test123'
            ];
            
            $working_password = null;
            foreach ($test_passwords as $test_pass) {
                $test_verify = password_verify($test_pass, $stored_hash);
                echo "<p>Testing '$test_pass': " . ($test_verify ? '✅ WORKS' : '❌ FAILS') . "</p>";
                if ($test_verify) {
                    $working_password = $test_pass;
                    break;
                }
            }
            
            if ($working_password) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Found Working Password: '$working_password'</h4>";
                echo "<p>Updating payment_temp with working password...</p>";
                
                // Update payment_temp with working password
                $update_stmt = $conn->prepare("UPDATE payment_temp SET password = ? WHERE username = ?");
                $update_stmt->bind_param("ss", $working_password, $username);
                
                if ($update_stmt->execute()) {
                    echo "<p>✅ Successfully updated payment_temp</p>";
                    echo "<p><strong>New credentials for sign-in:</strong></p>";
                    echo "<ul>";
                    echo "<li><strong>Username:</strong> $username</li>";
                    echo "<li><strong>Email:</strong> " . $user_data['email'] . "</li>";
                    echo "<li><strong>Password:</strong> $working_password</li>";
                    echo "</ul>";
                } else {
                    echo "<p>❌ Failed to update payment_temp: " . $conn->error . "</p>";
                }
                echo "</div>";
            } else {
                echo "<h4>Strategy 2: Generate new password and update hash</h4>";
                
                // Generate new password and update both hash and payment_temp
                $new_password = bin2hex(random_bytes(4));
                $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
                
                echo "<p><strong>Generated new password:</strong> '$new_password'</p>";
                echo "<p><strong>Generated new hash:</strong> " . substr($new_hash, 0, 50) . "...</p>";
                
                // Test new password with new hash
                $new_verify = password_verify($new_password, $new_hash);
                echo "<p><strong>New password verification:</strong> " . ($new_verify ? '✅ WORKS' : '❌ FAILS') . "</p>";
                
                if ($new_verify) {
                    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                    echo "<h4>⚠️ Option: Update Both Hash and Password</h4>";
                    echo "<p>This will update both the user table hash and payment_temp password</p>";
                    
                    if (isset($_POST['update_both'])) {
                        // Update user table hash
                        $update_user_stmt = $conn->prepare("UPDATE user SET password = ? WHERE username = ?");
                        $update_user_stmt->bind_param("ss", $new_hash, $username);
                        
                        // Update payment_temp password
                        $update_temp_stmt = $conn->prepare("UPDATE payment_temp SET password = ? WHERE username = ?");
                        $update_temp_stmt->bind_param("ss", $new_password, $username);
                        
                        if ($update_user_stmt->execute() && $update_temp_stmt->execute()) {
                            echo "<p>✅ Successfully updated both user and payment_temp</p>";
                            echo "<p><strong>New credentials for sign-in:</strong></p>";
                            echo "<ul>";
                            echo "<li><strong>Username:</strong> $username</li>";
                            echo "<li><strong>Email:</strong> " . $user_data['email'] . "</li>";
                            echo "<li><strong>Password:</strong> $new_password</li>";
                            echo "</ul>";
                        } else {
                            echo "<p>❌ Failed to update: " . $conn->error . "</p>";
                        }
                    } else {
                        echo "<form method='POST'>";
                        echo "<input type='hidden' name='update_both' value='1'>";
                        echo "<button type='submit' style='background: #ffc107; color: black; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Update Both Hash and Password</button>";
                        echo "</form>";
                    }
                    echo "</div>";
                }
            }
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>✅ Password Already Works!</h4>";
            echo "<p>The current password should work for sign-in. If it's not working, there might be an issue with the sign-in function.</p>";
            echo "</div>";
        }
    } else {
        echo "<p style='color: red;'>❌ No password found in payment_temp</p>";
        
        // Generate and save a working password
        echo "<h3>🔧 Creating New Password Entry</h3>";
        $new_password = bin2hex(random_bytes(4));
        
        // Test if this password would work with existing hash
        $test_verify = password_verify($new_password, $stored_hash);
        
        if (!$test_verify) {
            // Create a new hash that will work with a known password
            $known_password = 'temp123';
            $new_hash = password_hash($known_password, PASSWORD_DEFAULT);
            
            echo "<p>Creating new password entry with known working credentials...</p>";
            
            // Update user hash
            $update_user_stmt = $conn->prepare("UPDATE user SET password = ? WHERE username = ?");
            $update_user_stmt->bind_param("ss", $new_hash, $username);
            
            // Insert into payment_temp
            $session_id = 'fix_' . time();
            $insert_temp_stmt = $conn->prepare("INSERT INTO payment_temp (session_id, username, email, password) VALUES (?, ?, ?, ?)");
            $insert_temp_stmt->bind_param("ssss", $session_id, $username, $user_data['email'], $known_password);
            
            if ($update_user_stmt->execute() && $insert_temp_stmt->execute()) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h4>✅ Created Working Credentials</h4>";
                echo "<p><strong>New credentials for sign-in:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Username:</strong> $username</li>";
                echo "<li><strong>Email:</strong> " . $user_data['email'] . "</li>";
                echo "<li><strong>Password:</strong> $known_password</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                echo "<p>❌ Failed to create new credentials: " . $conn->error . "</p>";
            }
        }
    }
    
} else {
    echo "<p style='color: red;'>❌ User not found in database</p>";
}
?>

<h3>🧪 Test Sign-In</h3>
<p>After applying the fix, test sign-in with the updated credentials:</p>
<p><a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Try Sign-In</a></p>

<h3>🔍 Verify Fix</h3>
<p><a href="test-user95404-password.php" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Re-run Password Test</a></p>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4 { color: #333; }
</style>
