<?php
include('server.php');

echo "<h2>✅ Username Editing Feature Added</h2>";

echo "<h3>🎯 **New Feature Overview**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>What was added:</h4>";
echo "<ul>";
echo "<li>✅ <strong>Username field in profile display</strong></li>";
echo "<li>✅ <strong>Username field in edit profile modal</strong></li>";
echo "<li>✅ <strong>Frontend validation for username format</strong></li>";
echo "<li>✅ <strong>Backend validation and uniqueness check</strong></li>";
echo "<li>✅ <strong>Session update when username changes</strong></li>";
echo "<li>✅ <strong>Error handling for username conflicts</strong></li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔧 **Frontend Changes**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Profile Display (front-end/profile.php):</h4>";
echo "<ul>";
echo "<li>✅ Username field already displayed in profile view</li>";
echo "<li>✅ Added username input field to edit modal</li>";
echo "<li>✅ Added helpful text about username requirements</li>";
echo "</ul>";

echo "<h4>Form Validation:</h4>";
echo "<ul>";
echo "<li>✅ Username required validation</li>";
echo "<li>✅ Format validation (letters, numbers, underscores only)</li>";
echo "<li>✅ Length validation (3-50 characters)</li>";
echo "<li>✅ Real-time validation with custom alerts</li>";
echo "</ul>";

echo "<h4>Error Handling:</h4>";
echo "<ul>";
echo "<li>✅ New modal for username already exists error</li>";
echo "<li>✅ Focus on username field after error</li>";
echo "<li>✅ Proper error codes (error=3 for username conflicts)</li>";
echo "</ul>";
echo "</div>";

echo "<h3>⚙️ **Backend Changes**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Edit Profile Processing (functions/edit_profile.php):</h4>";
echo "<ul>";
echo "<li>✅ Added username parameter handling</li>";
echo "<li>✅ Server-side format validation</li>";
echo "<li>✅ Username uniqueness check</li>";
echo "<li>✅ Database update includes username field</li>";
echo "<li>✅ Session variable update when username changes</li>";
echo "</ul>";

echo "<h4>Validation Rules:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo "✅ Format: /^[a-zA-Z0-9_]+$/\n";
echo "✅ Length: 3-50 characters\n";
echo "✅ Uniqueness: Must not exist in database\n";
echo "✅ Required: Cannot be empty";
echo "</pre>";
echo "</div>";

echo "<h3>🎯 **How It Works**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>User Flow:</h4>";
echo "<ol>";
echo "<li><strong>User clicks 'Edit Profile'</strong> button</li>";
echo "<li><strong>Modal opens</strong> with current username pre-filled</li>";
echo "<li><strong>User can modify username</strong> along with other fields</li>";
echo "<li><strong>Frontend validation</strong> checks format and length</li>";
echo "<li><strong>Form submits</strong> to edit_profile.php</li>";
echo "<li><strong>Backend validation</strong> checks format and uniqueness</li>";
echo "<li><strong>Database update</strong> includes new username</li>";
echo "<li><strong>Session update</strong> if username changed</li>";
echo "<li><strong>Success/Error feedback</strong> to user</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔍 **Validation Details**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Frontend Validation (JavaScript):</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Username validation
if (username.length === 0) {
    showAlert(\'Username is required.\');
    return false;
}

if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    showAlert(\'Username must contain only letters, numbers, and underscores.\');
    return false;
}

if (username.length < 3) {
    showAlert(\'Username must be at least 3 characters long.\');
    return false;
}

if (username.length > 50) {
    showAlert(\'Username must be no more than 50 characters long.\');
    return false;
}
');
echo "</pre>";

echo "<h4>Backend Validation (PHP):</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
// Format validation
if (!preg_match(\'/^[a-zA-Z0-9_]+$/\', $new_username)) {
    $_SESSION[\'profile_update_error\'] = "Username must contain only letters, numbers, and underscores.";
    header(\'Location: ../front-end/profile.php?error=1\');
    exit();
}

// Length validation
if (strlen($new_username) < 3 || strlen($new_username) > 50) {
    $_SESSION[\'profile_update_error\'] = "Username must be between 3 and 50 characters long.";
    header(\'Location: ../front-end/profile.php?error=1\');
    exit();
}

// Uniqueness check
if ($new_username != $current_username) {
    $username_check_query = "SELECT * FROM user WHERE username = \'$new_username\'";
    $username_check_result = mysqli_query($conn, $username_check_query);
    
    if (mysqli_num_rows($username_check_result) > 0) {
        $_SESSION[\'profile_update_error\'] = "This username is already taken by another account.";
        header(\'Location: ../front-end/profile.php?error=3\');
        exit();
    }
}
');
echo "</pre>";
echo "</div>";

echo "<h3>🧪 **Testing Instructions**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Test Username Editing:</h4>";
echo "<ol>";
echo "<li><strong>Log in</strong> to your account</li>";
echo "<li><strong>Go to Profile page</strong></li>";
echo "<li><strong>Click 'Edit Profile'</strong> button</li>";
echo "<li><strong>Test Cases:</strong>";
echo "<ul>";
echo "<li><strong>Valid username:</strong> Try changing to 'newusername123' - should work</li>";
echo "<li><strong>Invalid format:</strong> Try 'user@name' - should show error</li>";
echo "<li><strong>Too short:</strong> Try 'ab' - should show error</li>";
echo "<li><strong>Too long:</strong> Try 51+ characters - should show error</li>";
echo "<li><strong>Existing username:</strong> Try another user's username - should show conflict error</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Verify:</strong> After successful change, check that username is updated everywhere</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🎉 **Expected Results**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #bee5eb;'>";
echo "<h4 style='color: #0c5460;'>✅ Success Indicators:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li>✅ <strong>Profile Display:</strong> Shows current username</li>";
echo "<li>✅ <strong>Edit Modal:</strong> Username field is editable</li>";
echo "<li>✅ <strong>Validation:</strong> Proper error messages for invalid usernames</li>";
echo "<li>✅ <strong>Uniqueness:</strong> Prevents duplicate usernames</li>";
echo "<li>✅ <strong>Session Update:</strong> Login session reflects new username</li>";
echo "<li>✅ <strong>Database:</strong> Username updated in user table</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🔗 **Quick Test Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/profile.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Go to Profile Page</a>";
echo "<a href='../front-end/sign-in.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Login Page</a>";
echo "</div>";

echo "<h3>📋 **Error Codes Reference**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background: #e9ecef;'>";
echo "<th style='padding: 10px;'>Error Code</th>";
echo "<th style='padding: 10px;'>Description</th>";
echo "<th style='padding: 10px;'>Modal Shown</th>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>error=1</strong></td>";
echo "<td style='padding: 10px;'>General validation error</td>";
echo "<td style='padding: 10px;'>errorModal</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>error=2</strong></td>";
echo "<td style='padding: 10px;'>Email already exists</td>";
echo "<td style='padding: 10px;'>emailExistsModal</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>error=3</strong></td>";
echo "<td style='padding: 10px;'>Username already exists</td>";
echo "<td style='padding: 10px;'>usernameExistsModal</td>";
echo "</tr>";

echo "<tr>";
echo "<td style='padding: 10px;'><strong>success=1</strong></td>";
echo "<td style='padding: 10px;'>Profile updated successfully</td>";
echo "<td style='padding: 10px;'>successModal</td>";
echo "</tr>";

echo "</table>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🚀 Username Editing Feature Complete!</h4>";
echo "<p style='color: #155724; margin: 0;'>Users can now edit their usernames through the profile page with proper validation, uniqueness checking, and error handling. The feature includes both frontend and backend validation to ensure data integrity and user experience.</p>";
echo "</div>";

// Show current user info if logged in
if (isset($_SESSION['username'])) {
    echo "<h3>🔍 **Current User Info**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    
    $username = $_SESSION['username'];
    $query = "SELECT username, email, first_name, last_name FROM user WHERE username = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result && $result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<p><strong>Current Username:</strong> " . htmlspecialchars($user['username']) . "</p>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</p>";
        echo "<p><strong>Email:</strong> " . htmlspecialchars($user['email']) . "</p>";
        echo "<p style='color: #28a745;'>✅ You can now edit your username through the profile page!</p>";
    } else {
        echo "<p style='color: #dc3545;'>❌ User not found in database</p>";
    }
    
    $stmt->close();
} else {
    echo "<h3>🔍 **User Status**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p style='color: #6c757d;'>Not logged in. Please log in to test the username editing feature.</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
