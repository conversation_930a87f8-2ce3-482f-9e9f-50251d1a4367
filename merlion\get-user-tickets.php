<?php
// Include database connection
include('../functions/server.php');

// Set headers for JSON response
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Check if admin is logged in
session_start();
if (!isset($_SESSION['admin_username'])) {
    echo json_encode(['success' => false, 'error' => 'Not logged in']);
    exit();
}

$admin_id = $_SESSION['admin_id'];
$admin_username = $_SESSION['admin_username'];

// Get user ID from request
$user_id = isset($_GET['user_id']) ? intval($_GET['user_id']) : null;

// Validate user ID
if (!$user_id) {
    echo json_encode(['success' => false, 'error' => 'Invalid user ID']);
    exit();
}

// Get user's tickets
$tickets_query = "SELECT id, subject, status, ticket_type, created_at 
                 FROM support_tickets 
                 WHERE user_id = ? 
                 ORDER BY 
                    CASE 
                        WHEN status = 'open' THEN 1
                        WHEN status = 'in_progress' THEN 2
                        WHEN status = 'resolved' THEN 3
                        WHEN status = 'closed' THEN 4
                        ELSE 5
                    END,
                    created_at DESC";

$stmt = mysqli_prepare($conn, $tickets_query);
mysqli_stmt_bind_param($stmt, 'i', $user_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

$tickets = [];
while ($ticket = mysqli_fetch_assoc($result)) {
    $tickets[] = $ticket;
}

mysqli_stmt_close($stmt);

// Return tickets as JSON
echo json_encode([
    'success' => true,
    'tickets' => $tickets
]);
?>
