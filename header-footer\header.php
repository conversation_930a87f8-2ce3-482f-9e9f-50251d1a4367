<!-- Localization APi: ip-api.com -->
<?php
//ip setting here
$use_manual_ip = false; // true = use manual ip, false = use user ip
$manual_ip     = '*******'; //insert manual ip here

// default
$country = '';
$city = '';

// check if page is index or sign-up this function will work
$current_page = basename($_SERVER['PHP_SELF']);
if ($current_page === 'index.php' || $current_page === 'sign-up.php') {

    // check what ip type should use
    if ($use_manual_ip) {
        $ip = $manual_ip;
    } else {
        //real user IP
        $ip = $_SERVER['REMOTE_ADDR'];

        // If localhost, use ipify for showing real ip
        if ($ip === '127.0.0.1' || $ip === '::1') {
            $ip = @file_get_contents("https://api.ipify.org");
        }
    }

    //get localtion from ip-api
    if ($ip) {
        $response = @file_get_contents("http://ip-api.com/json/{$ip}");
        if ($response) {
            $data = json_decode($response, true);
            if ($data && isset($data['country'])) {
                $country = $data['country'];
                $city    = $data['city'] ?? '';
            }
        }
    }
}
?>

<style>
/* Navbar Styles */
.floating-navbar-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    background-color: #473BF0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.floating-navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    max-width: 1200px;
    margin: 0 auto;
    height: 80px;
    position: relative;
}

/* Center logo in navbar */
.logo-center {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: right;
    justify-content: right;
    z-index: 2;
}

/* Logo responsive sizes */
/* Desktop (default) */
.logo img {
    height: 60px !important;
    width: 90px !important;
    transition: height 0.3s ease;
}

/* Tablet */
@media (max-width: 991px) {
    .logo img {
        height: 70px !important;
        width: 100px !important;
    }
}

/* Mobile */
@media (max-width: 767px) {
    .logo img {
        height: 40px !important;
        width: 60px !important;
    }

    .floating-navbar {
        height: 70px;
        /* Reduce navbar height on mobile */
    }

    .back-home-btn {
        font-size: 12px;
        padding: 7px 14px;
        letter-spacing: 0.2px;
        border-width: 1.5px;
    }
}

/* Small mobile */
@media (max-width: 480px) {
    .logo img {
        height: 40px;
    }

    .floating-navbar {
        height: 60px;
        /* Further reduce navbar height on small mobile */
        padding: 0 10px;
        /* Reduce horizontal padding */
    }

    .back-home-btn {
        font-size: 11px;
        padding: 6px 12px;
        letter-spacing: 0.1px;
        border-width: 1px;
        border-radius: 20px;
    }

    .home-button {
        margin-left: 8px;
    }
}

.nav-center {
    display: flex;
    justify-content: center;
    padding-top: 10px;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-links li a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    transition: color 0.3s;
}

.nav-links li a:hover {
    color: #f0f0f0;
}

.actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.login {
    color: white;
    text-decoration: none;
    font-weight: 500;
    margin-right: 15px;
}

.buynow.button {
    background-color: white;
    color: #473BF0;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    font-weight: 600;
    transition: background-color 0.3s, color 0.3s;
}

.buynow.button:hover {
    background-color: #f0f0f0;
}

.buynow.button.hidden {
    display: none;
}

.buynow.button.always-visible {
    display: inline-block;
}

/* Back to home page button styles */
.back-home-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: white;
    color: #473BF0;
    padding: 10px 20px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    border: 2px solid white;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.back-home-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background-color: transparent;
    transition: all 0.3s ease;
    z-index: -1;
}

.back-home-btn:hover {
    background-color: transparent;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.back-home-btn:hover::before {
    width: 100%;
}

.back-home-btn i {
    transition: transform 0.3s ease;
}

.back-home-btn:hover i {
    transform: translateX(-3px);
}

/* Dropdown styles */
.dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: white;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    min-width: 200px;
    display: none;
    z-index: 1001;
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    transition: background-color 0.3s;
}

.dropdown-item:hover {
    background-color: #f5f5f5;
}

/* Hamburger Menu Styles */
.hamburger-menu {
    display: none;
    cursor: pointer;
    z-index: 1001;
}

.hamburger-icon {
    width: 30px;
    height: 20px;
    position: relative;
}

.hamburger-icon span {
    display: block;
    position: absolute;
    height: 3px;
    width: 100%;
    background: white;
    border-radius: 3px;
    opacity: 1;
    left: 0;
    transform: rotate(0deg);
    transition: .25s ease-in-out;
}

.hamburger-icon span:nth-child(1) {
    top: 0px;
}

.hamburger-icon span:nth-child(2) {
    top: 8px;
}

.hamburger-icon span:nth-child(3) {
    top: 16px;
}

.hamburger-menu.active .hamburger-icon span:nth-child(1) {
    top: 8px;
    transform: rotate(135deg);
}

.hamburger-menu.active .hamburger-icon span:nth-child(2) {
    opacity: 0;
    left: -60px;
}

.hamburger-menu.active .hamburger-icon span:nth-child(3) {
    top: 8px;
    transform: rotate(-135deg);
}

/* Menu overlay */
.menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

.menu-overlay.active {
    display: block;
}

/* Mobile profile link (hidden by default) */
.mobile-profile-link {
    display: none;
}

/* Responsive styles */
@media (max-width: 991px) {
    .nav-links {
        gap: 15px;
    }
}

@media (max-width: 767px) {
    .hamburger-menu {
        display: block;
    }

    .buynow.button {
        display: none !important;
    }

    /* Show mobile profile link and hide desktop dropdown */
    .mobile-profile-link {
        display: block;
    }

    .desktop-dropdown,
    .desktop-login {
        display: none !important;
    }

    .nav-center {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100vh;
        background-color: rgba(71, 59, 240, 0.98);
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding-top: 0;
        z-index: 1000;
        overflow-y: auto;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.4s ease, visibility 0.4s ease;
    }

    .nav-center.active {
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        flex-direction: column;
        gap: 20px;
        width: 100%;
        text-align: center;
    }

    .nav-links li a {
        font-size: 18px;
        padding: 10px 0;
        display: block;
    }
}

/* make logo center */
/* nav.floating-navbar {
    align-items: center;
    display: flex;
    justify-content: center;
} */
</style>

<header class="floating-navbar-container">
    <nav class="floating-navbar">
        <div class="logo-container" style="display: flex; align-items: center;">
            <?php
            // Hide back button on customer panel pages
            $current_page = basename($_SERVER['PHP_SELF']);
            $customer_panel_pages = ['profile.php', 'user-panel.php', 'purchase-history.php', 'payment-methods.php', 'create-ticket.php', 'chat-support.php', 'open-tickets.php', 'my-ticket-log.php', 'my-ticket.php', 'ticket-detail.php'];
            $hide_back_button = in_array($current_page, $customer_panel_pages);
            ?>
            <?php if (!$hide_back_button): ?>
            <div class="home-button" style="margin-left: 15px;">
                <a href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'index.php' : '../index.php'; ?>"
                    class="back-home-btn">
                    <i class="fa fa-home" style="margin-right: 5px;"></i>Back
                </a>
            </div>
            <?php endif; ?>
        </div>
        <div class="logo-center">
            <a href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? 'index.php' : '../index.php'; ?>"
                class="logo">
                <?php
                $is_localhost = (strpos($_SERVER['HTTP_HOST'], 'localhost') !== false || strpos($_SERVER['HTTP_HOST'], '127.0.0.1') !== false);
                $logo_path = $is_localhost ? '/helloit/image/wp/HelloIT-new.png' : '/image/wp/HelloIT-new.png';
                ?>
                <img src="<?php echo $logo_path; ?>" alt="HelloIT Logo">
            </a>
        </div>
        <div style="width:90px;"></div> <!-- Placeholder for right side, adjust width as needed -->
        <!-- <div class="nav-right">
            <div class="actions">
                <a href="<?php echo (basename($_SERVER['PHP_SELF']) === 'index.php') ? './front-end/buy-now.php' : '../front-end/buy-now.php'; ?>"
                    class="buynow button <?php echo basename($_SERVER['PHP_SELF']) !== 'index.php' ? 'always-visible' : 'hidden'; ?>">Buy
                    Now</a>
                <div class="hamburger-menu">
                    <div class="hamburger-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                </div>
            </div>
        </div> -->
    </nav>
</header>

<!-- Overlay for mobile menu -->
<div class="menu-overlay"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const hamburgerMenu = document.querySelector('.hamburger-menu');
    const navCenter = document.querySelector('.nav-center');
    const menuOverlay = document.querySelector('.menu-overlay');
    const body = document.body;

    // Toggle mobile menu
    hamburgerMenu.addEventListener('click', function() {
        hamburgerMenu.classList.toggle('active');
        navCenter.classList.toggle('active');
        menuOverlay.classList.toggle('active');
        body.classList.toggle('menu-open');
    });

    // Close menu when clicking on overlay
    menuOverlay.addEventListener('click', function() {
        document.querySelector('.hamburger-menu').classList.remove('active');
        navCenter.classList.remove('active');
        menuOverlay.classList.remove('active');
        body.classList.remove('menu-open');
    });

    // Close menu when clicking on a link (for mobile), but exclude dropdown toggles
    const menuLinks = document.querySelectorAll('.nav-links a:not(.dropdown-toggle)');
    menuLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Don't close menu if this is a dropdown item
            if (this.classList.contains('dropdown-item') && window.innerWidth <= 767) {
                // Just let the click go through without closing the menu
                return;
            }

            if (window.innerWidth <= 767) {
                document.querySelector('.hamburger-menu').classList.remove('active');
                navCenter.classList.remove('active');
                menuOverlay.classList.remove('active');
                body.classList.remove('menu-open');
            }
        });
    });
});
</script>


<?php //include_once('./includes/tawk-widget.php'); ?>