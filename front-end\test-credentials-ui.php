<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Credentials UI Test - HelloIT</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
    body {
        background-color: #f8f9fa;
        font-family: 'Circular Std', sans-serif;
        padding: 20px;
    }

    .test-container {
        max-width: 600px;
        margin: 0 auto;
    }

    .credentials-section {
        background: #e3f2fd;
        border: 1px solid #2196f3;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
    }

    .copy-btn {
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }

    .input-group .btn {
        border-left: 0;
    }

    .input-group .btn:first-of-type {
        border-left: 1px solid #ced4da;
    }

    #passwordField::placeholder {
        color: #6c757d;
        font-style: italic;
    }

    .password-help-text {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .btn-success {
        animation: successPulse 0.3s ease-in-out;
    }

    @keyframes successPulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .demo-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    </style>
</head>

<body>
    <div class="test-container">
        <h1 class="text-center mb-4">Enhanced Credentials UI Test</h1>

        <div class="demo-section">
            <h2>Enhanced Payment Success Credentials</h2>
            <p class="text-muted">This demonstrates the improved credentials section with hidden password and enhanced copy functionality.</p>

            <!-- Simulated credentials section -->
            <div class="alert alert-info mt-4">
                <h5><i class="fas fa-user-plus me-2"></i>Your Account Credentials</h5>
                <p class="mb-3">Your account has been created! Please save these credentials:</p>

                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label fw-bold">Username:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="user94642" readonly>
                            <button class="btn btn-outline-secondary copy-btn" type="button"
                                onclick="copyToClipboard('user94642')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Email:</label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="<EMAIL>" readonly>
                            <button class="btn btn-outline-secondary copy-btn" type="button"
                                onclick="copyToClipboard('<EMAIL>')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-bold">Password:</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="passwordField"
                                value="a60530ba" readonly
                                placeholder="Click copy to copy password">
                            <button class="btn btn-outline-secondary" type="button" id="togglePasswordBtn"
                                onclick="togglePasswordVisibility()">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-secondary copy-btn" type="button"
                                onclick="copyPassword('a60530ba')">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                        <small class="text-muted">Click copy to copy password</small>
                    </div>
                </div>

                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Important:</strong> Please save these credentials in a safe place. You'll need them to
                    access your account and tickets.
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>Features Demonstrated:</h3>
            <ul class="list-group list-group-flush">
                <li class="list-group-item">
                    <i class="fas fa-eye-slash text-primary me-2"></i>
                    <strong>Hidden Password:</strong> Password field is masked by default for security
                </li>
                <li class="list-group-item">
                    <i class="fas fa-eye text-primary me-2"></i>
                    <strong>Toggle Visibility:</strong> Eye icon to show/hide password when needed
                </li>
                <li class="list-group-item">
                    <i class="fas fa-copy text-primary me-2"></i>
                    <strong>Enhanced Copy:</strong> Dedicated copy button for password with visual feedback
                </li>
                <li class="list-group-item">
                    <i class="fas fa-check text-success me-2"></i>
                    <strong>Visual Feedback:</strong> Buttons turn green and show checkmark when copied
                </li>
                <li class="list-group-item">
                    <i class="fas fa-info-circle text-info me-2"></i>
                    <strong>Helper Text:</strong> Clear instructions for users
                </li>
            </ul>
        </div>

        <div class="demo-section">
            <h3>Test Instructions:</h3>
            <div class="alert alert-light">
                <ol>
                    <li><strong>Copy Username:</strong> Click the copy button next to username field</li>
                    <li><strong>Copy Email:</strong> Click the copy button next to email field</li>
                    <li><strong>View Password:</strong> Click the eye icon to reveal/hide password</li>
                    <li><strong>Copy Password:</strong> Click the copy button to copy hidden password</li>
                    <li><strong>Visual Feedback:</strong> Notice the green checkmark and success animation</li>
                </ol>
            </div>
        </div>

        <div class="demo-section">
            <h3>Browser Compatibility:</h3>
            <div class="row">
                <div class="col-md-6">
                    <h6>✅ Supported Browsers:</h6>
                    <ul class="list-unstyled">
                        <li><i class="fab fa-chrome text-warning me-2"></i>Chrome 66+</li>
                        <li><i class="fab fa-firefox text-danger me-2"></i>Firefox 63+</li>
                        <li><i class="fab fa-safari text-primary me-2"></i>Safari 13.1+</li>
                        <li><i class="fab fa-edge text-info me-2"></i>Edge 79+</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>⚠️ Fallback for Older Browsers:</h6>
                    <p class="small text-muted">
                        If clipboard API is not supported, users will see an alert message and can manually copy the text.
                    </p>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="payment-success.php?session_id=test" class="btn btn-primary">
                <i class="fas fa-external-link-alt me-2"></i>View Actual Payment Success Page
            </a>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Enhanced clipboard function with fallbacks
    function copyToClipboardAdvanced(text, button) {
        // Try modern clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            return navigator.clipboard.writeText(text).then(function() {
                showCopySuccess(button);
                return true;
            }).catch(function(err) {
                console.warn('Clipboard API failed, trying fallback:', err);
                return fallbackCopyTextToClipboard(text, button);
            });
        } else {
            // Use fallback method
            return fallbackCopyTextToClipboard(text, button);
        }
    }

    // Fallback copy method for older browsers or non-HTTPS
    function fallbackCopyTextToClipboard(text, button) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        try {
            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                showCopySuccess(button);
                return Promise.resolve(true);
            } else {
                throw new Error('execCommand failed');
            }
        } catch (err) {
            document.body.removeChild(textArea);
            console.error('Fallback copy failed:', err);

            // Final fallback - show text in prompt for manual copy
            const userAgent = navigator.userAgent.toLowerCase();
            if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
                // Mobile devices - show in alert
                alert('Please copy this manually:\n\n' + text);
            } else {
                // Desktop - show in prompt
                prompt('Copy this text manually (Ctrl+C):', text);
            }
            return Promise.resolve(false);
        }
    }

    // Show visual success feedback
    function showCopySuccess(button, customMessage = null) {
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check"></i>';
        button.classList.add('btn-success');
        button.classList.remove('btn-outline-secondary');

        // Show custom message if provided (for password)
        if (customMessage) {
            const smallText = document.querySelector('.col-12:last-child .text-muted');
            if (smallText) {
                const originalText = smallText.textContent;
                smallText.textContent = customMessage;
                smallText.style.color = '#28a745';

                setTimeout(function() {
                    smallText.textContent = originalText;
                    smallText.style.color = '';
                }, 2000);
            }
        }

        setTimeout(function() {
            button.innerHTML = originalHTML;
            button.classList.remove('btn-success');
            button.classList.add('btn-outline-secondary');
        }, 2000);
    }

    function copyToClipboard(text) {
        const button = event.target.closest('button');
        copyToClipboardAdvanced(text, button);
    }

    function copyPassword(password) {
        const button = event.target.closest('button');
        copyToClipboardAdvanced(password, button).then(function(success) {
            if (success) {
                showCopySuccess(button, 'Password copied to clipboard!');
            }
        });
    }

    function togglePasswordVisibility() {
        const passwordField = document.getElementById('passwordField');
        const toggleBtn = document.getElementById('togglePasswordBtn');
        const icon = toggleBtn.querySelector('i');

        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }
    </script>
</body>
</html>
