<?php
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

echo "<h1>Payment System Fixes Test</h1>";

// Test 1: Check for users with saved payment methods
echo "<h2>Test 1: Users with Saved Payment Methods</h2>";
$users_query = "SELECT id, username, email, stripe_customer_id FROM user WHERE stripe_customer_id IS NOT NULL AND stripe_customer_id != '' LIMIT 5";
$users_result = mysqli_query($conn, $users_query);

if (mysqli_num_rows($users_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>User ID</th><th>Username</th><th>Email</th><th>Stripe Customer ID</th><th>Saved Cards</th></tr>";
    
    while ($user = mysqli_fetch_assoc($users_result)) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . $user['stripe_customer_id'] . "</td>";
        
        // Check for saved payment methods
        try {
            $payment_methods = \Stripe\PaymentMethod::all([
                'customer' => $user['stripe_customer_id'],
                'type' => 'card',
            ]);
            
            if (count($payment_methods->data) > 0) {
                echo "<td>";
                foreach ($payment_methods->data as $pm) {
                    echo ucfirst($pm->card->brand) . " •••• " . $pm->card->last4 . " (" . $pm->card->exp_month . "/" . $pm->card->exp_year . ")<br>";
                }
                echo "</td>";
            } else {
                echo "<td>No saved cards</td>";
            }
        } catch (Exception $e) {
            echo "<td>Error: " . $e->getMessage() . "</td>";
        }
        
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No users with Stripe customer IDs found.</p>";
}

// Test 2: Check recent webhook logs
echo "<h2>Test 2: Recent Webhook Activity</h2>";
$webhook_log_file = __DIR__ . '/webhook.log';
if (file_exists($webhook_log_file)) {
    $log_content = file_get_contents($webhook_log_file);
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -10); // Last 10 lines
    
    echo "<div style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; font-family: monospace; white-space: pre-wrap;'>";
    echo implode("\n", $recent_lines);
    echo "</div>";
} else {
    echo "<p>No webhook log file found.</p>";
}

// Test 3: Check cart functionality for logged-in users
echo "<h2>Test 3: Cart Payment Method Display Test</h2>";
if (isset($_SESSION['user_id'])) {
    $user_id = $_SESSION['user_id'];
    echo "<p><strong>Current logged-in user ID:</strong> " . $user_id . "</p>";
    
    // Get user's Stripe customer ID
    $stmt = $conn->prepare("SELECT stripe_customer_id, username FROM user WHERE id = ?");
    $stmt->bind_param("i", $user_id);
    $stmt->execute();
    $stmt->bind_result($stripe_customer_id, $username);
    $stmt->fetch();
    $stmt->close();
    
    echo "<p><strong>Username:</strong> " . $username . "</p>";
    echo "<p><strong>Stripe Customer ID:</strong> " . ($stripe_customer_id ?: 'None') . "</p>";
    
    if ($stripe_customer_id) {
        try {
            $payment_methods = \Stripe\PaymentMethod::all([
                'customer' => $stripe_customer_id,
                'type' => 'card',
            ]);
            
            echo "<p><strong>Saved Payment Methods:</strong></p>";
            if (count($payment_methods->data) > 0) {
                echo "<ul>";
                foreach ($payment_methods->data as $pm) {
                    echo "<li>" . ucfirst($pm->card->brand) . " •••• " . $pm->card->last4 . " (Expires " . $pm->card->exp_month . "/" . $pm->card->exp_year . ")</li>";
                }
                echo "</ul>";
                echo "<p style='color: green;'>✅ Cart page should show these saved payment methods</p>";
            } else {
                echo "<p>No saved payment methods found.</p>";
                echo "<p style='color: orange;'>⚠️ Cart page should only show 'Add New Card' option</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Error fetching payment methods: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ User has no Stripe customer ID - will be created on first purchase</p>";
    }
    
    echo "<p><a href='cart.php' style='background: #6754e2; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Cart Page</a></p>";
} else {
    echo "<p>Not logged in. <a href='sign-in.php'>Login</a> to test cart functionality.</p>";
}

// Test 4: Instructions
echo "<h2>Test 4: Testing Instructions</h2>";
echo "<div style='background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h3>How to Test the Fixes:</h3>";
echo "<ol>";
echo "<li><strong>Test Duplicate Customer Prevention:</strong>";
echo "<ul><li>Login as a user who already has saved payment methods</li>";
echo "<li>Add items to cart and complete a purchase</li>";
echo "<li>Check Stripe dashboard - should NOT create a new customer</li></ul></li>";

echo "<li><strong>Test Cart Payment Method Display:</strong>";
echo "<ul><li>Login as a user with saved payment methods</li>";
echo "<li>Add items to cart and go to cart page</li>";
echo "<li>Should see saved cards listed with 'Add New Card' option</li>";
echo "<li>Should be able to select any saved card and proceed</li></ul></li>";

echo "<li><strong>Test New User Flow:</strong>";
echo "<ul><li>Create a new user account</li>";
echo "<li>Add items to cart</li>";
echo "<li>Cart should only show 'Add New Card' option</li>";
echo "<li>Complete purchase and verify customer is created properly</li></ul></li>";
echo "</ol>";
echo "</div>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

h1, h2 {
    color: #333;
}

.success {
    color: green;
}

.error {
    color: red;
}

.warning {
    color: orange;
}
</style>
