<?php
include('server.php');

echo "<h2>🔧 Fix Missing Purchase Items</h2>";

// Check if this is a POST request to fix the issue
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['fix_transaction'])) {
    $transaction_id = $_POST['transaction_id'];
    $username = $_POST['username'];

    echo "<h3>🔧 Fixing Missing Items for Transaction: " . substr($transaction_id, 0, 30) . "...</h3>";

    // Based on your screenshot, the missing item is:
    // ULTIMATE S: $250.00 (10 tickets)

    $missing_items = [
        [
            'ticket_type' => 'ULTIMATE',
            'package_size' => 'S',
            'numbers_per_package' => 10,
            'dollar_price_per_package' => 250,
            'quantity' => 1
        ]
    ];

    try {
        $conn->begin_transaction();

        foreach ($missing_items as $item) {
            $ticket_type = $item['ticket_type'];
            $package_size = $item['package_size'];
            $numbers_per_package = $item['numbers_per_package'];
            $dollar_price_per_package = $item['dollar_price_per_package'];
            $quantity = $item['quantity'];
            $total_tickets = $numbers_per_package * $quantity;

            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>Adding Missing Item:</h4>";
            echo "<ul>";
            echo "<li><strong>Type:</strong> $ticket_type</li>";
            echo "<li><strong>Package Size:</strong> $package_size</li>";
            echo "<li><strong>Numbers per Package:</strong> $numbers_per_package</li>";
            echo "<li><strong>Price:</strong> $$dollar_price_per_package</li>";
            echo "<li><strong>Total Tickets:</strong> $total_tickets</li>";
            echo "</ul>";

            // Update user's ticket count
            $column = '';
            if (stripos($ticket_type, 'starter') !== false) $column = 'starter_tickets';
            elseif (stripos($ticket_type, 'premium') !== false || stripos($ticket_type, 'business') !== false) $column = 'premium_tickets';
            elseif (stripos($ticket_type, 'ultimate') !== false) $column = 'ultimate_tickets';
            else $column = 'premium_tickets'; // default

            // Get user ID
            $user_query = "SELECT id FROM user WHERE username = ?";
            $user_stmt = $conn->prepare($user_query);
            $user_stmt->bind_param("s", $username);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();
            $user_data = $user_result->fetch_assoc();
            $user_id = $user_data['id'];

            // Update user tickets
            $update_query = "UPDATE user SET $column = $column + ? WHERE id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("ii", $total_tickets, $user_id);
            if (!$update_stmt->execute()) {
                throw new Exception("Failed to update user tickets: " . $update_stmt->error);
            }

            echo "<p style='color: green;'>✅ Updated $column for user $username with $total_tickets tickets</p>";

            // Insert into purchasetickets with UTC timestamp
            $purchase_time_utc = getCurrentUTC(); // Get UTC time for consistent storage
            $insert = $conn->prepare("INSERT INTO purchasetickets (username, ticket_type, package_size, numbers_per_package, dollar_price_per_package, purchase_time, transactionid, remaining_tickets) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
            $insert->bind_param("sssiiisi", $username, $ticket_type, $package_size, $numbers_per_package, $dollar_price_per_package, $purchase_time_utc, $transaction_id, $total_tickets);
            if (!$insert->execute()) {
                throw new Exception("Failed to insert purchase history: " . $insert->error);
            }

            echo "<p style='color: green;'>✅ Added purchase history record</p>";
            echo "</div>";
        }

        $conn->commit();
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
        echo "<h4 style='color: #155724;'>🎉 Success!</h4>";
        echo "<p style='color: #155724;'>Missing items have been successfully added to your account.</p>";
        echo "<p><a href='../front-end/my-ticket.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Check My Tickets</a></p>";
        echo "</div>";

    } catch (Exception $e) {
        $conn->rollback();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #f5c6cb;'>";
        echo "<h4 style='color: #721c24;'>❌ Error</h4>";
        echo "<p style='color: #721c24;'>Failed to fix missing items: " . $e->getMessage() . "</p>";
        echo "</div>";
    }

    exit();
}

// Find recent large purchases that might have missing items
$recent_purchase_query = "
    SELECT transactionid, username, COUNT(*) as item_count, SUM(dollar_price_per_package) as total_amount,
           MAX(purchase_time) as purchase_time
    FROM purchasetickets
    WHERE purchase_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY transactionid
    HAVING item_count >= 3 AND total_amount >= 500
    ORDER BY purchase_time DESC
    LIMIT 10
";

$recent_result = mysqli_query($conn, $recent_purchase_query);

echo "<h3>📋 Recent Large Purchases (Potential Missing Items):</h3>";

if ($recent_result && mysqli_num_rows($recent_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 10px;'>Transaction ID</th>";
    echo "<th style='padding: 10px;'>Username</th>";
    echo "<th style='padding: 10px;'>Items Count</th>";
    echo "<th style='padding: 10px;'>Total Amount</th>";
    echo "<th style='padding: 10px;'>Purchase Time</th>";
    echo "<th style='padding: 10px;'>Action</th>";
    echo "</tr>";

    while ($row = mysqli_fetch_assoc($recent_result)) {
        $is_problematic = ($row['item_count'] < 6 && $row['total_amount'] >= 600); // Likely missing items
        $row_style = $is_problematic ? "background: #fff3cd;" : "";

        echo "<tr style='$row_style'>";
        echo "<td style='padding: 10px;'>" . substr($row['transactionid'], 0, 20) . "...</td>";
        echo "<td style='padding: 10px;'>" . $row['username'] . "</td>";
        echo "<td style='padding: 10px;'>" . $row['item_count'] . ($is_problematic ? " ⚠️" : "") . "</td>";
        echo "<td style='padding: 10px;'>$" . $row['total_amount'] . "</td>";
        echo "<td style='padding: 10px;'>" . $row['purchase_time'] . "</td>";
        echo "<td style='padding: 10px;'>";

        if ($is_problematic) {
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='transaction_id' value='" . $row['transactionid'] . "'>";
            echo "<input type='hidden' name='username' value='" . $row['username'] . "'>";
            echo "<button type='submit' name='fix_transaction' style='background: #dc3545; color: white; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>Fix Missing Items</button>";
            echo "</form>";
        } else {
            echo "<span style='color: #28a745;'>✅ Complete</span>";
        }

        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No recent large purchases found.</p>";
}

// Manual fix form
echo "<h3>🔧 Manual Fix Form</h3>";
echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;'>";
echo "<p>If you know the specific transaction that has missing items, you can manually fix it here:</p>";
echo "<form method='POST'>";
echo "<div style='margin: 10px 0;'>";
echo "<label><strong>Transaction ID:</strong></label><br>";
echo "<input type='text' name='transaction_id' placeholder='cs_test_...' style='width: 400px; padding: 8px; margin: 5px 0;' required>";
echo "</div>";
echo "<div style='margin: 10px 0;'>";
echo "<label><strong>Username:</strong></label><br>";
echo "<input type='text' name='username' placeholder='Enter username' style='width: 200px; padding: 8px; margin: 5px 0;' required>";
echo "</div>";
echo "<div style='margin: 20px 0;'>";
echo "<button type='submit' name='fix_transaction' style='background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fix Missing ULTIMATE S Package</button>";
echo "</div>";
echo "</form>";
echo "<p><small><strong>Note:</strong> This will add the missing ULTIMATE S package (10 tickets, $250) based on your screenshot.</small></p>";
echo "</div>";

echo "<h3>🔗 Quick Actions</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='debug-cart-purchase-issue.php' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Debug Tool</a>";
echo "<a href='../front-end/webhook.log' target='_blank' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Webhook Logs</a>";
echo "<a href='../front-end/my-ticket.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Check My Tickets</a>";
echo "<a href='../front-end/payment-history.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Payment History</a>";
echo "</div>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
button:hover { opacity: 0.9; }
input[type="text"] { border: 1px solid #ddd; border-radius: 3px; }
</style>
