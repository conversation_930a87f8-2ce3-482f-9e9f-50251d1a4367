<?php
/**
 * GraphQL Ticket API Test - Using Guzzle to connect to Appika GraphQL API
 *
 * This example demonstrates how to use GraphQL mutations and queries with the API endpoint:
 * https://dev-sgsg-tktapi.appika.com/graphiql
 * for creating and editing tickets.
 */

// Include Composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../functions/server.php';

// Start session to persist data across requests
session_start();

// Load centralized API configuration
require_once '../config/api-config.php';

// GraphQL API Configuration
$graphqlConfig = getGraphqlApiConfig();
$graphqlEndpoint = $graphqlConfig['endpoint'];
$graphiqlEndpoint = 'https://dev-sgsg-tktapi.appika.com/graphiql';

// Store API key in session for persistence
$tokenMessage = '';
$tokenMessageType = '';
if (isset($_POST['api_token']) && !empty($_POST['api_token'])) {
    $_SESSION['graphqlApiKey'] = $_POST['api_token'];
    $tokenMessage = 'GraphQL API token updated successfully.';
    $tokenMessageType = 'success';
}

if (isset($_SESSION['graphqlApiKey']) && !empty($_SESSION['graphqlApiKey'])) {
    $apiKey = $_SESSION['graphqlApiKey'];
} else {
    // Use centralized API key as default
    $apiKey = $graphqlConfig['key'];
}

// Create a Guzzle HTTP client
$client = new \GuzzleHttp\Client([
    'base_uri' => $graphqlEndpoint,
    'timeout' => 30,
    'http_errors' => false, // Don't throw exceptions for 4xx/5xx responses
]);

// Function to display results in a readable format
function displayResults($title, $data) {
    echo "<h2>{$title}</h2>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
}

// Function to make GraphQL requests
function makeGraphQLRequest($client, $query, $variables = [], $title = '') {
    global $apiKey;

    try {
        // Display request for debugging
        echo "<h3>GraphQL Query (Debug):</h3>";
        echo "<pre>{$query}</pre>";

        if (!empty($variables)) {
            echo "<h3>Variables (Debug):</h3>";
            echo "<pre>";
            print_r($variables);
            echo "</pre>";
        }

        // Prepare GraphQL request body
        $requestBody = [
            'query' => $query
        ];

        if (!empty($variables)) {
            $requestBody['variables'] = $variables;
        }

        // Send the request
        $response = $client->request('POST', '', [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => $requestBody
        ]);

        // Get status code
        $statusCode = $response->getStatusCode();
        echo "<p>Status Code: {$statusCode}</p>";

        // Get response body
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        displayResults($title . ' Response:', $data);

        return [
            'status' => $statusCode,
            'data' => $data
        ];
    } catch (\Exception $e) {
        echo "<h2>Error Occurred</h2>";
        echo "<p>Error Message: " . $e->getMessage() . "</p>";
        return [
            'status' => 500,
            'error' => $e->getMessage()
        ];
    }
}

// Function to get sample tickets from local database
function getSampleTicketsFromDB($conn) {
    $tickets = [];
    $sql = "SELECT st.*, u.email as req_email, u.username
            FROM support_tickets st
            JOIN user u ON st.user_id = u.id
            ORDER BY st.created_at DESC
            LIMIT 5";

    $result = mysqli_query($conn, $sql);
    if ($result && mysqli_num_rows($result) > 0) {
        while ($row = mysqli_fetch_assoc($result)) {
            // Map local database fields to essential Appika GraphQL format
            $tickets[] = [
                'id' => (int)$row['id'],
                'ticket_no' => 'HT-' . str_pad($row['id'], 6, '0', STR_PAD_LEFT),
                'contact_id' => (int)$row['user_id'],
                'agent_id' => $row['assigned_admin_id'] ? (int)$row['assigned_admin_id'] : null,
                'req_email' => $row['req_email'],
                'subject' => $row['subject'],
                'type_name' => $row['ticket_type'],
                'type' => $row['ticket_type'] === 'starter' ? 1 : ($row['ticket_type'] === 'premium' ? 2 : 3),
                'priority' => $row['priority'],
                'status' => $row['status'],
                'created' => $row['created_at'],
                'updated' => $row['updated_at'],
                'description' => $row['description'],
                'severity' => $row['severity'],
                'problem_type' => $row['problem_type']
            ];
        }
    }
    return $tickets;
}

// Process form submission
$message = '';
$messageType = '';
$ticketId = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        switch ($action) {
            case 'query_tickets':
                // Query tickets using GraphQL with essential fields only
                $query = '
                query {
                  ticket1: getTicket(id: 1) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                  ticket3: getTicket(id: 3) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                }';

                $result = makeGraphQLRequest($client, $query, [], 'Query Tickets');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Tickets queried successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to query tickets. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'create_ticket':
                // Create a new ticket using createTicketByAgent mutation (supports type and type_name)
                $email = !empty($_POST['email']) ? $_POST['email'] : '<EMAIL>';
                $subject = !empty($_POST['subject']) ? $_POST['subject'] : 'Test GraphQL Ticket';
                $replyMsg = !empty($_POST['reply_msg']) ? $_POST['reply_msg'] : 'Initial ticket creation message';

                // Handle ticket type
                $typeName = !empty($_POST['type_name']) ? $_POST['type_name'] : 'starter';
                $typeMapping = [
                    'starter' => 1,
                    'premium' => 2,
                    'ultimate' => 3
                ];
                $type = $typeMapping[$typeName] ?? 1;

                // Handle priority
                $priorityInput = !empty($_POST['priority']) ? $_POST['priority'] : 'low';
                $priorityMapping = [
                    'low' => 'LOW',
                    'medium' => 'MEDIUM',
                    'high' => 'HIGH',
                    'urgent' => 'URGENT'
                ];
                $priority = $priorityMapping[$priorityInput] ?? 'LOW';

                $mutation = '
                mutation CreateTicketByAgent(
                  $subject: String!,
                  $reply_msg: String!,
                  $req_email: String,
                  $type: Int!,
                  $type_name: String,
                  $priority: String!,
                  $status: String!,
                  $reply_type: String!,
                  $tags: String
                ) {
                  createTicketByAgent(
                    subject: $subject,
                    reply_msg: $reply_msg,
                    req_email: $req_email,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    reply_type: $reply_type,
                    tags: $tags
                  ) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                }';

                $variables = [
                    'subject' => $subject,
                    'reply_msg' => $replyMsg,
                    'req_email' => $email,
                    'type' => $type,
                    'type_name' => $typeName,
                    'priority' => $priority,
                    'status' => 'OPEN',
                    'reply_type' => 'note',
                    'tags' => ''
                ];

                $result = makeGraphQLRequest($client, $mutation, $variables, 'Create Ticket using createTicketByAgent');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Ticket created successfully! Type: ' . $typeName . ', Priority: ' . $priorityInput . ' → ' . $priority;
                    $messageType = 'success';
                    $ticketId = $result['data']['data']['createTicketByAgent']['id'] ?? '';
                } else {
                    $message = 'Failed to create ticket. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'search_ticket_for_update':
                // Search for a ticket to load its data for editing
                if (empty($_POST['search_ticket_id'])) {
                    $message = 'Ticket ID is required for search.';
                    $messageType = 'danger';
                    break;
                }

                $searchTicketId = $_POST['search_ticket_id'];

                $query = '
                query GetTicket($id: Int!) {
                  getTicket(id: $id) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                }';

                $variables = [
                    'id' => (int)$searchTicketId
                ];

                $result = makeGraphQLRequest($client, $query, $variables, 'Search Ticket for Update');

                if ($result['status'] >= 200 && $result['status'] < 300 && isset($result['data']['data']['getTicket'])) {
                    $ticketData = $result['data']['data']['getTicket'];
                    $message = 'Ticket found! Data loaded for editing.';
                    $messageType = 'success';

                    // Store ticket data in session for form population
                    $_SESSION['update_ticket_data'] = $ticketData;
                } else {
                    $message = 'Ticket not found or failed to retrieve ticket data.';
                    $messageType = 'danger';
                    unset($_SESSION['update_ticket_data']);
                }
                break;

            case 'update_ticket':
                // Update an existing ticket using actual updateTicket mutation (compatible with database)
                if (empty($_POST['ticketId'])) {
                    $message = 'Ticket ID is required for update.';
                    $messageType = 'danger';
                    break;
                }

                $ticketId = $_POST['ticketId'];
                $contactId = !empty($_POST['contact_id']) ? (int)$_POST['contact_id'] : null;
                $agentId = !empty($_POST['agent_id']) ? (int)$_POST['agent_id'] : null;
                $subject = !empty($_POST['subject']) ? $_POST['subject'] : 'Updated Test Ticket';

                // Map your enum values to API type IDs
                $typeName = !empty($_POST['type_name']) ? $_POST['type_name'] : 'starter';
                $typeMapping = [
                    'starter' => 1,
                    'premium' => 2,
                    'ultimate' => 3
                ];
                $type = $typeMapping[$typeName] ?? 1;

                // Map priority values to ALL CAPS as expected by server
                $priorityInput = !empty($_POST['priority']) ? $_POST['priority'] : 'low';
                $priorityMapping = [
                    'low' => 'LOW',
                    'medium' => 'MEDIUM',
                    'high' => 'HIGH',
                    'urgent' => 'URGENT'
                ];
                $priority = $priorityMapping[$priorityInput] ?? 'LOW';

                // Map status values to ALL CAPS as expected by server
                $statusInput = !empty($_POST['status']) ? $_POST['status'] : 'in_progress';
                $statusMapping = [
                    'open' => 'OPEN',
                    'in_progress' => 'WIP',
                    'resolved' => 'CLOSED',  // Map resolved to CLOSED since RESOLVED doesn't exist
                    'closed' => 'CLOSED'
                ];
                $status = $statusMapping[$statusInput] ?? 'OPEN';
                $reqEmail = !empty($_POST['req_email']) ? $_POST['req_email'] : null;
                $timeTrack = !empty($_POST['time_track']) ? $_POST['time_track'] : '00:00:00';
                $replyMsg = !empty($_POST['reply_msg']) ? $_POST['reply_msg'] : 'Ticket updated via GraphQL API';

                // Additional fields for complete updateTicket mutation
                $agentGroup = !empty($_POST['agent_group']) ? (int)$_POST['agent_group'] : null;
                $assign2 = !empty($_POST['assign2']) ? (int)$_POST['assign2'] : null;
                $followers = !empty($_POST['followers']) ? $_POST['followers'] : '';
                $tags = !empty($_POST['tags']) ? $_POST['tags'] : '';
                $replyType = !empty($_POST['reply_type']) ? $_POST['reply_type'] : 'note';
                $cc = !empty($_POST['cc']) ? $_POST['cc'] : '';
                $customFields = !empty($_POST['custom_fields']) ? $_POST['custom_fields'] : '';
                $customLogText = !empty($_POST['custom_log_text']) ? $_POST['custom_log_text'] : '';

                $mutation = '
                mutation UpdateTicket(
                  $id: Int!,
                  $contact_id: Int,
                  $agent_id: Int,
                  $subject: String!,
                  $type: Int!,
                  $type_name: String,
                  $priority: String!,
                  $status: String!,
                  $req_email: String,
                  $time_track: String!,
                  $reply_msg: String,
                  $tags: String
                ) {
                  updateTicket(
                    id: $id,
                    contact_id: $contact_id,
                    agent_id: $agent_id,
                    subject: $subject,
                    type: $type,
                    type_name: $type_name,
                    priority: $priority,
                    status: $status,
                    req_email: $req_email,
                    time_track: $time_track,
                    reply_msg: $reply_msg,
                    tags: $tags
                  ) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                }';

                $variables = [
                    'id' => (int)$ticketId,
                    'contact_id' => $contactId,
                    'agent_id' => $agentId,
                    'subject' => $subject,
                    'type' => $type,
                    'type_name' => $typeName,
                    'priority' => $priority,
                    'status' => $status,
                    'req_email' => $reqEmail,
                    'time_track' => $timeTrack,
                    'reply_msg' => $replyMsg,
                    'tags' => ''
                ];

                $result = makeGraphQLRequest($client, $mutation, $variables, 'Update Ticket');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Ticket updated successfully! Type: ' . $typeName . ' (ID: ' . $type . '), Priority: ' . $priorityInput . ' → ' . $priority;
                    $messageType = 'success';
                    // Clear the stored ticket data after successful update
                    unset($_SESSION['update_ticket_data']);
                } else {
                    $message = 'Failed to update ticket. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'get_ticket':
                // Get a specific ticket using GraphQL query with essential fields
                if (empty($_POST['ticketId'])) {
                    $message = 'Ticket ID is required to get details.';
                    $messageType = 'danger';
                    break;
                }

                $ticketId = $_POST['ticketId'];

                $query = '
                query GetTicket($id: Int!) {
                  getTicket(id: $id) {
                    id
                    ticket_no
                    contact_id
                    agent_id
                    req_email
                    subject
                    type
                    type_name
                    priority
                    status
                    created
                    updated
                  }
                }';

                $variables = [
                    'id' => (int)$ticketId
                ];

                $result = makeGraphQLRequest($client, $query, $variables, 'Get Ticket');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Ticket details retrieved successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get ticket details. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;

            case 'list_tickets':
                // List all tickets using GraphQL query with correct parameters
                $query = '
                query GetTickets($contact_id: Int, $agent_id: Int, $query_filter: String) {
                  getTickets(contact_id: $contact_id, agent_id: $agent_id, query_filter: $query_filter) {
                    data {
                      id
                      ticket_no
                      contact_id
                      agent_id
                      req_email
                      subject
                      type
                      type_name
                      priority
                      status
                      created
                      updated
                    }
                  }
                }';

                $variables = [
                    'contact_id' => null,
                    'agent_id' => null,
                    'query_filter' => null
                ];

                $result = makeGraphQLRequest($client, $query, $variables, 'Get Tickets');

                if ($result['status'] >= 200 && $result['status'] < 300) {
                    $message = 'Tickets retrieved successfully!';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to get tickets. ' . ($result['error'] ?? '');
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// Get sample tickets from local database for reference
$sampleTickets = getSampleTicketsFromDB($conn);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphQL Ticket API Test</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .card {
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .btn-action {
        margin-right: 10px;
    }

    pre {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
        max-height: 400px;
    }

    .nav-tabs {
        margin-bottom: 20px;
    }

    .tab-content {
        padding: 20px;
        border: 1px solid #dee2e6;
        border-top: none;
        border-radius: 0 0 5px 5px;
    }

    .sample-tickets {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }

    .graphql-endpoint {
        background-color: #e7f3ff;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">GraphQL Ticket API Test</h1>

        <!-- GraphQL Endpoint Info -->
        <div class="graphql-endpoint">
            <h5>GraphQL Endpoints:</h5>
            <p><strong>API:</strong> <?php echo $graphqlEndpoint; ?></p>
            <p><strong>GraphiQL:</strong> <a href="<?php echo $graphiqlEndpoint; ?>"
                    target="_blank"><?php echo $graphiqlEndpoint; ?></a></p>
        </div>

        <!-- Show token validation message -->
        <?php if (!empty($tokenMessage)): ?>
        <div class="alert alert-<?php echo $tokenMessageType; ?>" role="alert">
            <?php echo $tokenMessage; ?>
        </div>
        <?php endif; ?>

        <!-- Add API Token Input Form -->
        <form method="post" action="" class="mb-4">
            <div class="form-group">
                <label for="api_token"><strong>GraphQL API Token</strong></label>
                <input type="text" class="form-control" id="api_token" name="api_token"
                    value="<?php echo htmlspecialchars($apiKey); ?>" required>
                <small class="form-text text-muted">Paste your GraphQL API token here. This will be used for all GraphQL
                    requests below.</small>
            </div>
            <button type="submit" class="btn btn-secondary mb-2">Set Token</button>
        </form>

        <?php if (!empty($message)): ?>
        <div class="alert alert-<?php echo $messageType; ?>" role="alert">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>



        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="graphqlTabs" role="tablist">
                    <li class="nav-item">
                        <a class="nav-link active" id="query-tab" data-toggle="tab" href="#query" role="tab">Query
                            Tickets</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="create-tab" data-toggle="tab" href="#create" role="tab">Create
                            Ticket</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="update-tab" data-toggle="tab" href="#update" role="tab">Update
                            Ticket</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="get-tab" data-toggle="tab" href="#get" role="tab">Get Ticket</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" id="list-tab" data-toggle="tab" href="#list" role="tab">Get Tickets</a>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="graphqlTabsContent">
                    <!-- Query Tickets Tab -->
                    <div class="tab-pane fade show active" id="query" role="tabpanel">
                        <h3>Query Specific Tickets (Example from your request)</h3>
                        <p>This demonstrates the exact GraphQL query you provided, cleaned up with essential fields that
                            match your database:
                        </p>
                        <pre>query {
  ticket1: getTicket(id: 1) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
  }
  ticket3: getTicket(id: 3) {
    id
    ticket_no
    contact_id
    agent_id
    req_email
    subject
    type
    type_name
    priority
    status
    created
    updated
  }
}</pre>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="query_tickets">
                            <button type="submit" class="btn btn-primary">Execute Query</button>
                        </form>
                    </div>

                    <!-- Create Ticket Tab -->
                    <div class="tab-pane fade" id="create" role="tabpanel">
                        <h3>Create New Ticket (using createTicketByAgent)</h3>
                        <div class="alert alert-success">
                            <strong>✅ Now supports type and type_name!</strong> Using <code>createTicketByAgent</code>
                            allows you to specify ticket type during creation.
                        </div>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="create_ticket">

                            <div class="form-group">
                                <label for="email">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required
                                    value="<EMAIL>">
                                <small class="form-text text-muted">Email address of the ticket creator</small>
                            </div>

                            <div class="form-group">
                                <label for="subject">Subject *</label>
                                <input type="text" class="form-control" id="subject" name="subject" required
                                    value="Test GraphQL Ticket">
                                <small class="form-text text-muted">Brief description of the issue</small>
                            </div>

                            <div class="form-group">
                                <label for="type_name">Ticket Type *</label>
                                <select class="form-control" id="type_name" name="type_name" required>
                                    <option value="starter" selected>Starter</option>
                                    <option value="premium">Premium</option>
                                    <option value="ultimate">Ultimate</option>
                                </select>
                                <small class="form-text text-muted">Maps to your ticket_type enum (starter→1, premium→2,
                                    ultimate→3)</small>
                            </div>

                            <div class="form-group">
                                <label for="priority">Priority *</label>
                                <select class="form-control" id="priority" name="priority" required>
                                    <option value="low" selected>Low</option>
                                    <option value="medium">Medium</option>
                                    <option value="high">High</option>
                                    <option value="urgent">Urgent</option>
                                </select>
                                <small class="form-text text-muted">Priority level (all mapped to 'LOW' by API)</small>
                            </div>

                            <div class="form-group">
                                <label for="reply_msg">Description *</label>
                                <textarea class="form-control" id="reply_msg" name="reply_msg" rows="4" required
                                    placeholder="Describe the issue in detail">Initial ticket creation message</textarea>
                                <small class="form-text text-muted">Detailed description of the issue or request</small>
                            </div>

                            <button type="submit" class="btn btn-primary">Create Ticket</button>

                            <div class="mt-3">
                                <p><strong>createTicketByAgent Benefits:</strong></p>
                                <ul>
                                    <li>✅ Supports <code>type</code> and <code>type_name</code> fields</li>
                                    <li>✅ Maps to your database ticket_type enum</li>
                                    <li>✅ Uses proper API field formats (ALL CAPS for priority/status)</li>
                                    <li>✅ Returns complete ticket data with type information</li>
                                </ul>
                                <p><strong>Default Values:</strong> Priority: LOW, Status: OPEN, Reply Type: note</p>
                            </div>
                        </form>
                    </div>



                    <!-- Update Ticket Tab -->
                    <div class="tab-pane fade" id="update" role="tabpanel">
                        <h3>Update Existing Ticket (with Search & Load)</h3>
                        <div class="alert alert-info">
                            <strong>Step 1:</strong> Search for a ticket by ID to load its data, then edit and update.
                        </div>

                        <!-- Search Form -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5>🔍 Search Ticket to Edit</h5>
                            </div>
                            <div class="card-body">
                                <form method="post" action="">
                                    <input type="hidden" name="action" value="search_ticket_for_update">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="form-group">
                                                <label for="searchTicketId">Ticket ID *</label>
                                                <input type="number" class="form-control" id="searchTicketId"
                                                    name="search_ticket_id" required
                                                    placeholder="Enter ticket ID to search">
                                                <small class="form-text text-muted">Enter the ID of the ticket you want
                                                    to edit</small>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>&nbsp;</label>
                                                <button type="submit" class="btn btn-primary btn-block">🔍 Search &
                                                    Load</button>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <?php
                        // Get ticket data from session if available
                        $ticketData = $_SESSION['update_ticket_data'] ?? null;
                        ?>

                        <!-- Update Form -->
                        <form method="post" action="">
                            <input type="hidden" name="action" value="update_ticket">

                            <div class="form-group">
                                <label for="updateTicketId">Ticket ID *</label>
                                <input type="number" class="form-control" id="updateTicketId" name="ticketId" required
                                    value="<?php echo $ticketData ? htmlspecialchars($ticketData['id']) : ''; ?>"
                                    placeholder="Enter ticket ID to update">
                                <small class="form-text text-muted">ID of the ticket to update</small>
                            </div>

                            <div class="form-group">
                                <label for="updateContactId">Contact ID</label>
                                <input type="number" class="form-control" id="updateContactId" name="contact_id"
                                    value="<?php echo $ticketData ? htmlspecialchars($ticketData['contact_id']) : ''; ?>"
                                    placeholder="Contact/User ID">
                                <small class="form-text text-muted">Maps to your user_id field</small>
                            </div>

                            <div class="form-group">
                                <label for="updateAgentId">Agent ID</label>
                                <input type="number" class="form-control" id="updateAgentId" name="agent_id"
                                    value="<?php echo $ticketData ? htmlspecialchars($ticketData['agent_id']) : ''; ?>"
                                    placeholder="Assigned agent ID">
                                <small class="form-text text-muted">Maps to your assigned_admin_id field</small>
                            </div>

                            <div class="form-group">
                                <label for="updateReqEmail">Requester Email</label>
                                <input type="email" class="form-control" id="updateReqEmail" name="req_email"
                                    value="<?php echo $ticketData ? htmlspecialchars($ticketData['req_email']) : ''; ?>"
                                    placeholder="Requester email address">
                                <small class="form-text text-muted">Email of the ticket requester</small>
                            </div>

                            <div class="form-group">
                                <label for="updateSubject">Subject *</label>
                                <input type="text" class="form-control" id="updateSubject" name="subject" required
                                    value="<?php echo $ticketData ? htmlspecialchars($ticketData['subject']) : 'Updated GraphQL Ticket'; ?>">
                                <small class="form-text text-muted">Brief description of the issue</small>
                            </div>

                            <div class="form-group">
                                <label for="updateTypeName">Ticket Type *</label>
                                <select class="form-control" id="updateTypeName" name="type_name" required>
                                    <?php
                                    $currentType = $ticketData ? $ticketData['type_name'] : 'starter';
                                    $typeOptions = ['starter' => 'Starter', 'premium' => 'Premium', 'ultimate' => 'Ultimate'];
                                    foreach ($typeOptions as $value => $label):
                                    ?>
                                    <option value="<?php echo $value; ?>"
                                        <?php echo $currentType === $value ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Maps to your ticket_type enum</small>
                            </div>

                            <div class="form-group">
                                <label for="updatePriority">Priority *</label>
                                <select class="form-control" id="updatePriority" name="priority" required>
                                    <?php
                                    $currentPriority = $ticketData ? strtolower($ticketData['priority']) : 'low';
                                    $priorityOptions = ['low' => 'Low', 'medium' => 'Medium', 'high' => 'High', 'urgent' => 'Urgent'];
                                    foreach ($priorityOptions as $value => $label):
                                    ?>
                                    <option value="<?php echo $value; ?>"
                                        <?php echo $currentPriority === $value ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Priority level (API limitation: all mapped to
                                    'low')</small>
                            </div>

                            <div class="form-group">
                                <label for="updateStatus">Status *</label>
                                <select class="form-control" id="updateStatus" name="status" required>
                                    <?php
                                    $currentStatus = $ticketData ? strtolower($ticketData['status']) : 'open';
                                    $statusOptions = ['open' => 'Open', 'in_progress' => 'In Progress', 'resolved' => 'Resolved', 'closed' => 'Closed'];
                                    foreach ($statusOptions as $value => $label):
                                    ?>
                                    <option value="<?php echo $value; ?>"
                                        <?php echo $currentStatus === $value ? 'selected' : ''; ?>>
                                        <?php echo $label; ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                                <small class="form-text text-muted">Current status of the ticket</small>
                            </div>

                            <div class="form-group">
                                <label for="updateReplyMsg">Update Description *</label>
                                <textarea class="form-control" id="updateReplyMsg" name="reply_msg" rows="4" required
                                    placeholder="Describe the update or changes made">Ticket updated via GraphQL API</textarea>
                                <small class="form-text text-muted">Message describing the update (required by
                                    API)</small>
                            </div>

                            <button type="submit" class="btn btn-warning">Update Ticket</button>

                            <div class="mt-3">
                                <p><strong>getTicket Fields (Editable):</strong></p>
                                <ul>
                                    <li>Only fields returned by getTicket response are editable</li>
                                    <li>Type mapping: starter→1, premium→2, ultimate→3</li>
                                    <li>Priority values: LOW, MEDIUM, HIGH, URGENT (ALL CAPS)</li>
                                    <li>Status values: OPEN, WIP, CLOSED (resolved→CLOSED)</li>
                                    <li>Time track format: HH:MM:SS (e.g., 00:00:00)</li>
                                    <li>Read-only: id, ticket_no, created, updated</li>
                                </ul>
                            </div>
                        </form>
                    </div>

                    <!-- Get Ticket Tab -->
                    <div class="tab-pane fade" id="get" role="tabpanel">
                        <h3>Get Ticket Details</h3>
                        <div class="alert alert-warning">
                            <strong>Note:</strong> If you get "No query results" error, the ticket ID doesn't
                            exist.
                            Try using "Get Tickets" first to see available ticket IDs.
                        </div>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="get_ticket">

                            <div class="form-group">
                                <label for="getTicketId">Ticket ID *</label>
                                <input type="number" class="form-control" id="getTicketId" name="ticketId" required
                                    placeholder="Enter ticket ID" value="1">
                                <small class="form-text text-muted">ID of the ticket to retrieve (try 1, 2, 3,
                                    etc.)</small>
                            </div>

                            <button type="submit" class="btn btn-info">Get Ticket</button>
                        </form>

                        <div class="mt-3">
                            <p><strong>Common ticket IDs to try:</strong></p>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="document.getElementById('getTicketId').value='1'">ID: 1</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="document.getElementById('getTicketId').value='2'">ID: 2</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="document.getElementById('getTicketId').value='3'">ID: 3</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="document.getElementById('getTicketId').value='5'">ID: 5</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm"
                                    onclick="document.getElementById('getTicketId').value='10'">ID: 10</button>
                            </div>
                        </div>
                    </div>

                    <!-- Get Tickets Tab -->
                    <div class="tab-pane fade" id="list" role="tabpanel">
                        <h3>Get All Tickets</h3>
                        <p>This will retrieve a list of all tickets using the <code>getTickets</code> query with
                            pagination support.</p>
                        <form method="post" action="">
                            <input type="hidden" name="action" value="list_tickets">
                            <button type="submit" class="btn btn-success">Get Tickets</button>
                        </form>
                        <div class="mt-3">
                            <p><strong>Note:</strong> The API uses <code>getTickets</code> instead of
                                <code>listTickets</code>.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>