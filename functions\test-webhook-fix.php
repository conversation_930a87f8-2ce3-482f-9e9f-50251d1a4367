<?php
include('server.php');

echo "<h2>🔧 Webhook Payment Method Saving Fix</h2>";

echo "<h3>🎯 **Issue Identified**</h3>";
echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #f5c6cb;'>";
echo "<h4>Root Cause:</h4>";
echo "<ul>";
echo "<li>❌ <strong>Wrong Webhook Endpoint:</strong> Stripe events were going to <code>/helloit/support-ticket/webhook</code></li>";
echo "<li>❌ <strong>Missing Logic:</strong> Main webhook.php didn't have payment method saving logic</li>";
echo "<li>❌ <strong>Guest Processing:</strong> processGuestPurchase() function was empty</li>";
echo "<li>❌ <strong>No Payment Method Attachment:</strong> Cards weren't being attached to Stripe customers</li>";
echo "</ul>";
echo "</div>";

echo "<h3>✅ **Solution Implemented**</h3>";
echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #c3e6cb;'>";
echo "<h4>What was fixed:</h4>";
echo "<ol>";
echo "<li><strong>Enhanced Main Webhook (webhook.php):</strong>";
echo "<ul>";
echo "<li>✅ Added <code>savePaymentMethodIfRequested()</code> function</li>";
echo "<li>✅ Integrated payment method saving for logged-in users</li>";
echo "<li>✅ Enhanced guest purchase processing</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>Payment Method Saving Logic:</strong>";
echo "<ul>";
echo "<li>✅ Retrieves payment method from payment intent</li>";
echo "<li>✅ Attaches payment method to Stripe customer</li>";
echo "<li>✅ Saves payment method details to database</li>";
echo "<li>✅ Respects user's checkbox preference</li>";
echo "<li>✅ Sets first payment method as default</li>";
echo "</ul>";
echo "</li>";

echo "<li><strong>Error Handling & Logging:</strong>";
echo "<ul>";
echo "<li>✅ Comprehensive logging for debugging</li>";
echo "<li>✅ Graceful handling of already-attached payment methods</li>";
echo "<li>✅ Prevents duplicate database entries</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔧 **Technical Details**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Files Modified:</h4>";
echo "<ul>";
echo "<li><strong>webhook.php:</strong> Added payment method saving logic</li>";
echo "<li><strong>front-end/create-checkout-session.php:</strong> Added save_payment_method to metadata</li>";
echo "<li><strong>functions/create-cart-checkout-session.php:</strong> Added save_payment_method to metadata</li>";
echo "</ul>";

echo "<h4>Key Function Added:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo htmlspecialchars('
function savePaymentMethodIfRequested($session, $metadata, $username, $conn) {
    // Check if user wanted to save payment method
    $save_payment_method = true; // Default to save
    if (isset($metadata[\'save_payment_method\'])) {
        $save_payment_method = $metadata[\'save_payment_method\'] === \'true\' || $metadata[\'save_payment_method\'] === \'1\';
    }
    
    if ($save_payment_method) {
        // Get payment method from payment intent
        $payment_intent = \Stripe\PaymentIntent::retrieve($session->payment_intent);
        $payment_method = \Stripe\PaymentMethod::retrieve($payment_intent->payment_method);
        
        // Attach to customer and save to database
        $payment_method->attach([\'customer\' => $stripe_customer_id]);
        // ... database saving logic
    }
}
');
echo "</pre>";
echo "</div>";

echo "<h3>🎯 **How It Works Now**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>Updated Webhook Flow:</h4>";
echo "<ol>";
echo "<li><strong>Stripe Event:</strong> checkout.session.completed sent to /helloit/support-ticket/webhook</li>";
echo "<li><strong>Main Webhook:</strong> webhook.php processes the event</li>";
echo "<li><strong>User Type Detection:</strong>";
echo "<ul>";
echo "<li>If <code>metadata['username']</code> exists → <code>processLoggedInUserPurchase()</code></li>";
echo "<li>If no username → <code>processGuestPurchase()</code> (calls enhanced webhook)</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Payment Method Saving:</strong> <code>savePaymentMethodIfRequested()</code> called after purchase processing</li>";
echo "<li><strong>Result:</strong> Payment method attached to Stripe customer and saved to database</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🧪 **Testing Instructions**</h3>";
echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #bbdefb;'>";
echo "<h4>Test Logged-in User:</h4>";
echo "<ol>";
echo "<li><strong>Log in</strong> to your account</li>";
echo "<li><strong>Add items to cart</strong></li>";
echo "<li><strong>Keep checkbox checked</strong> (Save my payment method)</li>";
echo "<li><strong>Complete purchase</strong></li>";
echo "<li><strong>Check webhook.log</strong> for these entries:</li>";
echo "<ul>";
echo "<li><code>Processing logged-in user purchase</code></li>";
echo "<li><code>Attempting to save payment method for user: [username]</code></li>";
echo "<li><code>Payment method attached to customer: cus_xxx</code></li>";
echo "<li><code>Payment method saved to database for user: [user_id]</code></li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Check Stripe Dashboard:</strong> Payment method should be attached to customer</li>";
echo "</ol>";

echo "<h4>Test Guest User:</h4>";
echo "<ol>";
echo "<li><strong>Log out</strong> (or use incognito mode)</li>";
echo "<li><strong>Add items to cart</strong></li>";
echo "<li><strong>Keep checkbox checked</strong></li>";
echo "<li><strong>Complete purchase</strong></li>";
echo "<li><strong>Check webhook.log</strong> for:</li>";
echo "<ul>";
echo "<li><code>Processing guest purchase (creating new user)</code></li>";
echo "<li><code>Calling enhanced webhook for guest purchase processing</code></li>";
echo "<li>Enhanced webhook logs from front-end/stripe-webhook.php</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<h3>🔍 **Debugging Tools**</h3>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>Check Webhook Logs:</h4>";
echo "<p>Monitor <code>webhook.log</code> in the root directory for these key messages:</p>";
echo "<ul>";
echo "<li><code>✅ Processing checkout.session.completed</code></li>";
echo "<li><code>✅ Attempting to save payment method for user: [username]</code></li>";
echo "<li><code>✅ Payment method attached to customer: cus_xxx</code></li>";
echo "<li><code>✅ Payment method saved to database for user: [user_id]</code></li>";
echo "</ul>";

echo "<h4>Check Database:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 12px;'>";
echo "-- Check recent payment methods\n";
echo "SELECT pm.*, u.username, u.email \n";
echo "FROM payment_methods pm \n";
echo "JOIN user u ON pm.user_id = u.id \n";
echo "WHERE pm.created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)\n";
echo "ORDER BY pm.created_at DESC;";
echo "</pre>";

echo "<h4>Check Stripe Dashboard:</h4>";
echo "<ul>";
echo "<li>Go to <strong>Customers</strong> section</li>";
echo "<li>Find the customer by email</li>";
echo "<li>Check <strong>Payment methods</strong> tab</li>";
echo "<li>Verify payment method is attached</li>";
echo "</ul>";
echo "</div>";

echo "<h3>📋 **Expected Results**</h3>";
echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #bee5eb;'>";
echo "<h4 style='color: #0c5460;'>✅ Success Indicators:</h4>";
echo "<ul style='color: #0c5460;'>";
echo "<li>✅ <strong>Webhook Logs:</strong> Show payment method saving process</li>";
echo "<li>✅ <strong>Stripe Dashboard:</strong> Payment methods attached to customers</li>";
echo "<li>✅ <strong>Database:</strong> payment_methods table populated</li>";
echo "<li>✅ <strong>User Experience:</strong> Saved cards appear in Payment Methods page</li>";
echo "<li>✅ <strong>Future Purchases:</strong> Users can select saved payment methods</li>";
echo "</ul>";
echo "</div>";

echo "<h3>🚨 **Troubleshooting**</h3>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0; border: 1px solid #ffeaa7;'>";
echo "<h4>If payment methods still aren't saving:</h4>";
echo "<ol>";
echo "<li><strong>Check webhook.log</strong> for error messages</li>";
echo "<li><strong>Verify Stripe webhook endpoint</strong> is pointing to correct URL</li>";
echo "<li><strong>Check metadata</strong> is being passed correctly</li>";
echo "<li><strong>Verify setup_future_usage</strong> is being added to checkout sessions</li>";
echo "<li><strong>Test with different card types</strong> (some cards may not support saving)</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; border: 1px solid #c3e6cb;'>";
echo "<h4 style='color: #155724;'>🎉 Fix Implemented!</h4>";
echo "<p style='color: #155724; margin: 0;'>The main webhook now has payment method saving logic for both logged-in and guest users. Credit cards should now be properly saved to Stripe when the checkbox is checked.</p>";
echo "</div>";

echo "<h3>🔗 **Quick Test Links**</h3>";
echo "<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>";
echo "<a href='../front-end/cart.php' style='background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Cart Purchase</a>";
echo "<a href='../front-end/payment-methods.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Payment Methods</a>";
echo "<a href='../webhook.log' style='background: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;' target='_blank'>View Webhook Logs</a>";
echo "</div>";

// Check if webhook.log exists and show recent entries
if (file_exists(__DIR__ . '/../webhook.log')) {
    echo "<h3>📄 **Recent Webhook Log Entries**</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    $log_content = file_get_contents(__DIR__ . '/../webhook.log');
    $log_lines = explode("\n", $log_content);
    $recent_lines = array_slice($log_lines, -20); // Last 20 lines
    
    echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; font-size: 11px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recent_lines as $line) {
        if (!empty(trim($line))) {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
    echo "</div>";
} else {
    echo "<h3>📄 **Webhook Log**</h3>";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<p>No webhook.log file found yet. Make a test purchase to generate webhook events.</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
code { background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
</style>
