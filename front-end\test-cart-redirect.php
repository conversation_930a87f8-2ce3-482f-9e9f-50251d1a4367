<?php
session_start();
include('../functions/server.php');

echo "<h1>Cart Redirect Fix Test</h1>";

echo "<h2>Test Results:</h2>";

// Test 1: Check if support-ticket/cart.php redirects properly
echo "<h3>1. Support-ticket Cart Redirect Test</h3>";
echo "<p><strong>Expected:</strong> support-ticket/cart should redirect to front-end/cart</p>";
echo "<p><strong>Test:</strong> <a href='/helloit/support-ticket/cart' target='_blank'>Click here to test support-ticket/cart redirect</a></p>";
echo "<p><em>Should automatically redirect to the main cart page</em></p>";

// Test 2: Check buy-now popup cart link
echo "<h3>2. Buy-now Popup Cart Link Test</h3>";
echo "<p><strong>Expected:</strong> 'Go to Cart' button in popup should go to /helloit/front-end/cart</p>";
echo "<p><strong>Test:</strong> <a href='/helloit/support-ticket/buy-now' target='_blank'>Go to buy-now page</a></p>";
echo "<p><em>Add item to cart and check if 'Go to Cart' button works correctly</em></p>";

// Test 3: Direct cart access
echo "<h3>3. Direct Cart Access Test</h3>";
echo "<p><strong>Main Cart Page:</strong> <a href='/helloit/front-end/cart' target='_blank'>front-end/cart</a></p>";
echo "<p><em>Should show the full cart functionality with payment methods</em></p>";

// Test 4: URL mapping verification
echo "<h3>4. URL Mapping Verification</h3>";
echo "<div style='background: #f5f5f5; padding: 15px; border: 1px solid #ddd; font-family: monospace;'>";
echo "<strong>URL Mappings:</strong><br>";
echo "• /helloit/support-ticket/buy-now → front-end/buy-now.php<br>";
echo "• /helloit/support-ticket/cart → support-ticket/cart.php → redirects to front-end/cart<br>";
echo "• /helloit/front-end/cart → front-end/cart.php (main cart)<br>";
echo "</div>";

// Test 5: Check current session
echo "<h3>5. Current Session Info</h3>";
if (isset($_SESSION['user_id'])) {
    echo "<p><strong>Logged in as User ID:</strong> " . $_SESSION['user_id'] . "</p>";
    echo "<p><strong>Username:</strong> " . ($_SESSION['username'] ?? 'Not set') . "</p>";
    echo "<p style='color: green;'>✅ You can test the full cart functionality</p>";
} else {
    echo "<p style='color: orange;'>⚠️ Not logged in - cart will work but with limited functionality</p>";
    echo "<p><a href='sign-in.php'>Login</a> to test full cart features</p>";
}

// Test 6: Instructions
echo "<h3>6. Testing Instructions</h3>";
echo "<div style='background: #e8f4fd; padding: 15px; border-left: 4px solid #2196F3;'>";
echo "<h4>How to Test the Fix:</h4>";
echo "<ol>";
echo "<li><strong>Test Cart Popup Redirect:</strong>";
echo "<ul>";
echo "<li>Go to <a href='/helloit/support-ticket/buy-now' target='_blank'>support-ticket/buy-now</a></li>";
echo "<li>Click 'Add to Cart' on any package</li>";
echo "<li>When popup appears, click 'Go to Cart'</li>";
echo "<li>Should redirect to main cart page (not support-ticket/cart.php)</li>";
echo "</ul></li>";

echo "<li><strong>Test Direct Support-ticket Cart Access:</strong>";
echo "<ul>";
echo "<li>Go to <a href='/helloit/support-ticket/cart' target='_blank'>support-ticket/cart</a></li>";
echo "<li>Should automatically redirect to main cart page</li>";
echo "</ul></li>";

echo "<li><strong>Verify Main Cart Functionality:</strong>";
echo "<ul>";
echo "<li>Ensure cart shows items correctly</li>";
echo "<li>Verify payment methods display properly</li>";
echo "<li>Test checkout process</li>";
echo "</ul></li>";
echo "</ol>";
echo "</div>";

// Test 7: File status check
echo "<h3>7. File Status Check</h3>";
$files_to_check = [
    '/helloit/front-end/buy-now.php' => 'Main buy-now page',
    '/helloit/support-ticket/cart.php' => 'Support-ticket cart redirect',
    '/helloit/front-end/cart.php' => 'Main cart page'
];

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>File</th><th>Description</th><th>Status</th></tr>";

foreach ($files_to_check as $file => $description) {
    $file_path = $_SERVER['DOCUMENT_ROOT'] . $file;
    $exists = file_exists($file_path);
    $status = $exists ? '✅ Exists' : '❌ Missing';
    $color = $exists ? 'green' : 'red';
    
    echo "<tr>";
    echo "<td><code>$file</code></td>";
    echo "<td>$description</td>";
    echo "<td style='color: $color;'>$status</td>";
    echo "</tr>";
}

echo "</table>";

?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
}

a {
    color: #6754e2;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

code {
    background: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
}

.success {
    color: green;
}

.error {
    color: red;
}

.warning {
    color: orange;
}
</style>
