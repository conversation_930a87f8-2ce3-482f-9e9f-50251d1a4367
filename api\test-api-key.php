<?php
require_once '../vendor/autoload.php';

// Test both API keys to see which one works
$graphqlEndpoint = 'https://dev-sgsg-tktapi.appika.com/graphql';

$apiKeys = [
    'old_key' => 'YmFiMmJlOGM0ZDBkZGJkNGY4NzA0YWQzMTdjMTZiYTMxZmM3Zjc5ZDgzZjliOTY4NDBkY2EzNzgyMTdhMjdlNQ',
    'new_key' => 'NWViNWRhZmQ4ZWMxZmMzODA4MGIzZmQzNGE2ZmRhY2U4MDI2NWJjMWExMTFhNzFiMDM3M2I5OTQ1OTk5NDVlYg'
];

function testApiKey($endpoint, $apiKey, $keyName) {
    $client = new \GuzzleHttp\Client([
        'timeout' => 30,
        'http_errors' => false,
    ]);

    // Simple query to test authentication
    $query = '
    query {
      getTicket(id: 1) {
        id
        ticket_no
        subject
      }
    }';

    try {
        $response = $client->post($endpoint, [
            'headers' => [
                'Authorization' => "Bearer {$apiKey}",
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ],
            'json' => [
                'query' => $query
            ]
        ]);

        $statusCode = $response->getStatusCode();
        $body = $response->getBody()->getContents();
        $data = json_decode($body, true);

        echo "<h3>Testing {$keyName}:</h3>";
        echo "<p><strong>Status Code:</strong> {$statusCode}</p>";
        echo "<p><strong>API Key:</strong> " . substr($apiKey, 0, 20) . "...</p>";
        
        if ($statusCode == 401) {
            echo "<p style='color: red;'><strong>❌ UNAUTHORIZED</strong> - API key is invalid</p>";
        } elseif ($statusCode >= 200 && $statusCode < 300) {
            echo "<p style='color: green;'><strong>✅ SUCCESS</strong> - API key is valid</p>";
        } else {
            echo "<p style='color: orange;'><strong>⚠️ OTHER ERROR</strong> - Status: {$statusCode}</p>";
        }
        
        echo "<pre>";
        print_r($data);
        echo "</pre>";
        echo "<hr>";

        return $statusCode;
    } catch (\Exception $e) {
        echo "<h3>Testing {$keyName}:</h3>";
        echo "<p style='color: red;'><strong>❌ EXCEPTION:</strong> " . $e->getMessage() . "</p>";
        echo "<hr>";
        return 500;
    }
}

echo "<h1>API Key Authentication Test</h1>";
echo "<p>Testing both API keys to determine which one is valid...</p>";

foreach ($apiKeys as $keyName => $apiKey) {
    testApiKey($graphqlEndpoint, $apiKey, $keyName);
}

echo "<h2>Conclusion:</h2>";
echo "<p>Use the API key that shows <strong style='color: green;'>✅ SUCCESS</strong> in your create-ticket files.</p>";
?>
