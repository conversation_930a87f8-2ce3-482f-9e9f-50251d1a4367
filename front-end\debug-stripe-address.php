<?php
/**
 * Debug Stripe Address Structure
 * This will help us understand how Stripe provides address data
 */
session_start();
require_once '../vendor/autoload.php';
include('../functions/server.php');

// Set Stripe API key
\Stripe\Stripe::setApiKey('sk_test_51ROUofEJRyUMDOj5Op3iqq0gpje1mQqwhINGCYVwVRvmOixzZh1IUmfwArcDt7FtJkN9aszopPP6dyxVWyOZeC8F00gwZtB0ju');

echo "<h2>Debug Stripe Address Structure</h2>";

// Check if session_id is provided
if (isset($_GET['session_id'])) {
    $session_id = $_GET['session_id'];
    
    try {
        // Retrieve the checkout session
        $checkout_session = \Stripe\Checkout\Session::retrieve($session_id);
        
        echo "<h3>Stripe Checkout Session Address Data:</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        
        if (isset($checkout_session->customer_details->address)) {
            $address = $checkout_session->customer_details->address;
            
            echo "<h4>Raw Address Object:</h4>";
            echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px;'>";
            echo json_encode($address, JSON_PRETTY_PRINT);
            echo "</pre>";
            
            echo "<h4>Individual Address Fields:</h4>";
            echo "<ul>";
            echo "<li><strong>line1:</strong> " . ($address->line1 ?? 'null') . "</li>";
            echo "<li><strong>line2:</strong> " . ($address->line2 ?? 'null') . "</li>";
            echo "<li><strong>city:</strong> " . ($address->city ?? 'null') . "</li>";
            echo "<li><strong>state:</strong> " . ($address->state ?? 'null') . "</li>";
            echo "<li><strong>postal_code:</strong> " . ($address->postal_code ?? 'null') . "</li>";
            echo "<li><strong>country:</strong> " . ($address->country ?? 'null') . "</li>";
            echo "</ul>";
            
            // Check for additional fields that might contain suburb
            echo "<h4>Checking for Suburb/District Fields:</h4>";
            echo "<ul>";
            
            $address_array = json_decode(json_encode($address), true);
            foreach ($address_array as $key => $value) {
                if (stripos($key, 'suburb') !== false || stripos($key, 'district') !== false) {
                    echo "<li><strong>$key:</strong> $value</li>";
                }
            }
            
            // Check if suburb is embedded in line2
            if (!empty($address->line2)) {
                echo "<li><strong>Analysis:</strong> line2 contains: '" . $address->line2 . "'</li>";
                
                // Try to detect if suburb is appended to line2
                if (strpos($address->line2, ',') !== false) {
                    $parts = explode(',', $address->line2);
                    echo "<li><strong>line2 parts:</strong> " . implode(' | ', array_map('trim', $parts)) . "</li>";
                }
            }
            echo "</ul>";
            
        } else {
            echo "<p>No address data found in checkout session.</p>";
        }
        
        echo "</div>";
        
        // Show customer details
        echo "<h3>Full Customer Details:</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 3px; max-height: 400px; overflow-y: auto;'>";
        echo json_encode($checkout_session->customer_details, JSON_PRETTY_PRINT);
        echo "</pre>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Error:</h4>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
} else {
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>How to Use This Debug Tool:</h3>";
    echo "<ol>";
    echo "<li>Complete a test purchase on localhost</li>";
    echo "<li>On the payment success page, copy the session_id from the URL</li>";
    echo "<li>Visit this page with: <code>debug-stripe-address.php?session_id=YOUR_SESSION_ID</code></li>";
    echo "<li>This will show you exactly how Stripe provides the address data</li>";
    echo "</ol>";
    
    echo "<h3>Example:</h3>";
    echo "<code>debug-stripe-address.php?session_id=cs_test_a1...</code>";
    echo "</div>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>What We're Looking For:</h3>";
    echo "<ul>";
    echo "<li><strong>How Stripe provides suburb data</strong> - Is it in a separate field or combined with line2?</li>";
    echo "<li><strong>Address structure</strong> - What fields are actually available?</li>";
    echo "<li><strong>Data format</strong> - How is the suburb information formatted?</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<h3>Common Stripe Address Behavior:</h3>";

echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🔍 What Usually Happens:</h4>";
echo "<ol>";
echo "<li><strong>User fills form:</strong>";
echo "<ul>";
echo "<li>Address line 1: '123 Road Prakanong bangkok'</li>";
echo "<li>Address line 2: '321 Road prakanong bangkok'</li>";
echo "<li>Suburb: 'Prakanong'</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Stripe processes:</strong>";
echo "<ul>";
echo "<li>line1: '123 Road Prakanong bangkok'</li>";
echo "<li>line2: '321 Road prakanong bangkok, Prakanong' (suburb appended)</li>";
echo "<li>No separate suburb field</li>";
echo "</ul>";
echo "</li>";
echo "<li><strong>Solution needed:</strong>";
echo "<ul>";
echo "<li>Parse line2 to extract suburb</li>";
echo "<li>Clean line2 and save suburb to district</li>";
echo "</ul>";
echo "</li>";
echo "</ol>";
echo "</div>";

echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h4>🛠️ Potential Fix Strategy:</h4>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 5px;'>";
echo "// If line2 contains comma, suburb might be appended\n";
echo "if (strpos(\$address2, ',') !== false) {\n";
echo "    \$parts = explode(',', \$address2);\n";
echo "    \$address2 = trim(\$parts[0]); // Clean address2\n";
echo "    \$district = trim(\$parts[1]); // Extract suburb\n";
echo "} else {\n";
echo "    \$district = ''; // No suburb found\n";
echo "}";
echo "</pre>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Use this debug tool to understand exactly how Stripe provides your address data, then we can implement the correct parsing logic.</strong></p>";
?>
