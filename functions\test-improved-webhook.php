<?php
include('server.php');

echo "<h2>🧪 Test Improved Webhook System</h2>";

echo "<h3>📋 Current System Status:</h3>";

// Check payment_temp table structure
echo "<h4>1. Payment Temp Table Structure:</h4>";
$structure_query = "DESCRIBE payment_temp";
$structure_result = mysqli_query($conn, $structure_query);

$has_user_created = false;
$has_processed_at = false;

echo "<ul>";
while ($row = mysqli_fetch_assoc($structure_result)) {
    echo "<li><strong>" . $row['Field'] . ":</strong> " . $row['Type'];
    if ($row['Field'] === 'user_created') $has_user_created = true;
    if ($row['Field'] === 'processed_at') $has_processed_at = true;
    echo "</li>";
}
echo "</ul>";

if ($has_user_created && $has_processed_at) {
    echo "<p style='color: green;'>✅ Table structure is updated with new columns</p>";
} else {
    echo "<p style='color: red;'>❌ Table structure needs updating</p>";
    echo "<p><a href='update-payment-temp-table.php' style='background: #ffc107; color: black; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>Update Table Structure</a></p>";
}

// Check recent payment_temp entries
echo "<h4>2. Recent Payment Temp Entries:</h4>";
$recent_query = "SELECT id, session_id, username, email, password, user_created, processed_at, created_at FROM payment_temp ORDER BY id DESC LIMIT 5";
$recent_result = mysqli_query($conn, $recent_query);

if ($recent_result && mysqli_num_rows($recent_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>ID</th>";
    echo "<th style='padding: 8px;'>Session ID</th>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Password</th>";
    echo "<th style='padding: 8px;'>User Created</th>";
    echo "<th style='padding: 8px;'>Processed At</th>";
    echo "<th style='padding: 8px;'>Created At</th>";
    echo "</tr>";
    
    while ($row = mysqli_fetch_assoc($recent_result)) {
        echo "<tr>";
        echo "<td style='padding: 8px;'>" . $row['id'] . "</td>";
        echo "<td style='padding: 8px;'>" . substr($row['session_id'], 0, 15) . "...</td>";
        echo "<td style='padding: 8px;'>" . $row['username'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['email'] . "</td>";
        echo "<td style='padding: 8px;'>" . $row['password'] . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['user_created'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['processed_at'] ?? 'NULL') . "</td>";
        echo "<td style='padding: 8px;'>" . ($row['created_at'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No payment_temp entries found.</p>";
}

// Check for password mismatches
echo "<h4>3. Password Verification Check:</h4>";
$mismatch_query = "
    SELECT u.username, u.email, u.password as user_hash, pt.password as temp_password
    FROM user u
    JOIN payment_temp pt ON u.username = pt.username
    WHERE u.username LIKE 'user%'
    ORDER BY u.id DESC
    LIMIT 10
";

$mismatch_result = mysqli_query($conn, $mismatch_query);

if ($mismatch_result && mysqli_num_rows($mismatch_result) > 0) {
    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
    echo "<tr style='background: #f8f9fa;'>";
    echo "<th style='padding: 8px;'>Username</th>";
    echo "<th style='padding: 8px;'>Email</th>";
    echo "<th style='padding: 8px;'>Temp Password</th>";
    echo "<th style='padding: 8px;'>Hash Type</th>";
    echo "<th style='padding: 8px;'>Verification</th>";
    echo "<th style='padding: 8px;'>Status</th>";
    echo "</tr>";
    
    $working_count = 0;
    $broken_count = 0;
    
    while ($row = mysqli_fetch_assoc($mismatch_result)) {
        $username = $row['username'];
        $user_hash = $row['user_hash'];
        $temp_password = $row['temp_password'];
        
        // Test password verification
        if (strpos($user_hash, '$2y$') === 0) {
            $verify_result = password_verify($temp_password, $user_hash);
            $hash_type = 'bcrypt';
        } else {
            $verify_result = ($user_hash === md5($temp_password));
            $hash_type = 'md5';
        }
        
        if ($verify_result) {
            $working_count++;
            $status = '✅ Working';
            $row_style = 'background: #d4edda;';
        } else {
            $broken_count++;
            $status = '❌ Broken';
            $row_style = 'background: #f8d7da;';
        }
        
        echo "<tr style='$row_style'>";
        echo "<td style='padding: 8px;'>" . $username . "</td>";
        echo "<td style='padding: 8px;'>" . $row['email'] . "</td>";
        echo "<td style='padding: 8px;'>" . $temp_password . "</td>";
        echo "<td style='padding: 8px;'>" . $hash_type . "</td>";
        echo "<td style='padding: 8px;'>" . ($verify_result ? 'PASS' : 'FAIL') . "</td>";
        echo "<td style='padding: 8px;'>" . $status . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>📊 Verification Summary:</h5>";
    echo "<ul>";
    echo "<li><strong>Working credentials:</strong> $working_count</li>";
    echo "<li><strong>Broken credentials:</strong> $broken_count</li>";
    echo "<li><strong>Total checked:</strong> " . ($working_count + $broken_count) . "</li>";
    echo "</ul>";
    
    if ($broken_count > 0) {
        echo "<p style='color: red;'><strong>⚠️ $broken_count users have password mismatches!</strong></p>";
        echo "<p><a href='comprehensive-password-fix.php' style='background: #dc3545; color: white; padding: 8px 15px; text-decoration: none; border-radius: 3px;'>Fix All Password Mismatches</a></p>";
    } else {
        echo "<p style='color: green;'><strong>✅ All credentials are working correctly!</strong></p>";
    }
    echo "</div>";
    
} else {
    echo "<p>No users found to check.</p>";
}

// Test specific user if provided
if (isset($_GET['test_user'])) {
    $test_user = $_GET['test_user'];
    echo "<h4>4. Testing User: $test_user</h4>";
    
    // Get user data
    $user_query = "SELECT id, username, email, password FROM user WHERE username = ?";
    $user_stmt = $conn->prepare($user_query);
    $user_stmt->bind_param("s", $test_user);
    $user_stmt->execute();
    $user_result = $user_stmt->get_result();
    
    // Get payment_temp data
    $temp_query = "SELECT password, user_created, processed_at FROM payment_temp WHERE username = ? ORDER BY id DESC LIMIT 1";
    $temp_stmt = $conn->prepare($temp_query);
    $temp_stmt->bind_param("s", $test_user);
    $temp_stmt->execute();
    $temp_result = $temp_stmt->get_result();
    
    if ($user_result->num_rows > 0 && $temp_result->num_rows > 0) {
        $user_data = $user_result->fetch_assoc();
        $temp_data = $temp_result->fetch_assoc();
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h5>User Details:</h5>";
        echo "<p><strong>Username:</strong> " . $user_data['username'] . "</p>";
        echo "<p><strong>Email:</strong> " . $user_data['email'] . "</p>";
        echo "<p><strong>User Hash:</strong> " . substr($user_data['password'], 0, 30) . "...</p>";
        echo "<p><strong>Temp Password:</strong> " . $temp_data['password'] . "</p>";
        echo "<p><strong>User Created:</strong> " . ($temp_data['user_created'] ?? 'NULL') . "</p>";
        echo "<p><strong>Processed At:</strong> " . ($temp_data['processed_at'] ?? 'NULL') . "</p>";
        
        // Test verification
        $stored_hash = $user_data['password'];
        $temp_password = $temp_data['password'];
        
        if (strpos($stored_hash, '$2y$') === 0) {
            $verify_result = password_verify($temp_password, $stored_hash);
            $hash_type = 'bcrypt';
        } else {
            $verify_result = ($stored_hash === md5($temp_password));
            $hash_type = 'md5';
        }
        
        echo "<p><strong>Hash Type:</strong> $hash_type</p>";
        echo "<p><strong>Verification:</strong> " . ($verify_result ? '✅ PASS' : '❌ FAIL') . "</p>";
        
        if ($verify_result) {
            echo "<div style='background: #d4edda; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
            echo "<p style='color: green;'><strong>✅ This user can sign in with these credentials!</strong></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 3px; margin: 10px 0;'>";
            echo "<p style='color: red;'><strong>❌ Password mismatch - needs fixing!</strong></p>";
            echo "</div>";
        }
        echo "</div>";
    } else {
        echo "<p style='color: red;'>❌ User or payment_temp data not found for: $test_user</p>";
    }
}
?>

<h3>🧪 Test Specific User</h3>
<form method="GET">
    <label>Username to test:</label>
    <input type="text" name="test_user" placeholder="user21041" value="<?php echo $_GET['test_user'] ?? ''; ?>">
    <input type="submit" value="Test User">
</form>

<h3>🔧 Available Actions</h3>
<div style='display: flex; gap: 10px; flex-wrap: wrap; margin: 20px 0;'>
    <a href="update-payment-temp-table.php" style="background: #ffc107; color: black; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Update Table Structure</a>
    <a href="comprehensive-password-fix.php" style="background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Fix Password Mismatches</a>
    <a href="../front-end/sign-in.php" style="background: #007cba; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Test Sign-In</a>
    <a href="../front-end/payment-success.php" style="background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;">Payment Success Page</a>
</div>

<h3>📝 Improvements Made</h3>
<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;'>
    <h4>Webhook Improvements:</h4>
    <ul>
        <li>✅ <strong>Consistent password storage:</strong> Same password saved to both tables</li>
        <li>✅ <strong>Better tracking:</strong> user_created and processed_at fields</li>
        <li>✅ <strong>Automatic cleanup:</strong> Mark payment_temp as processed</li>
        <li>✅ <strong>Improved logging:</strong> Better debugging information</li>
    </ul>
    
    <h4>Payment Success Page Improvements:</h4>
    <ul>
        <li>✅ <strong>Smart data source:</strong> Uses appropriate table based on user status</li>
        <li>✅ <strong>Password verification:</strong> Ensures credentials will work</li>
        <li>✅ <strong>Automatic fixing:</strong> Fixes password mismatches on the fly</li>
        <li>✅ <strong>Better user experience:</strong> Shows pending vs completed states</li>
    </ul>
    
    <h4>Expected Results:</h4>
    <ul>
        <li>🎯 <strong>No more password mismatches</strong> between payment_temp and user tables</li>
        <li>🎯 <strong>Sign-in always works</strong> with credentials from payment success page</li>
        <li>🎯 <strong>Better data management</strong> with proper cleanup and tracking</li>
        <li>🎯 <strong>Improved debugging</strong> with better logging and status tracking</li>
    </ul>
</div>

<h3>🚀 Next Steps</h3>
<ol>
    <li><strong>Update table structure</strong> if needed (click button above)</li>
    <li><strong>Fix existing password mismatches</strong> (click button above)</li>
    <li><strong>Test new purchase</strong> with non-login user</li>
    <li><strong>Verify sign-in works</strong> with displayed credentials</li>
    <li><strong>Monitor webhook logs</strong> for improved processing</li>
</ol>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h2, h3, h4, h5 { color: #333; }
table { margin: 20px 0; }
th { background: #f8f9fa; }
form { margin: 20px 0; }
input[type="text"] { padding: 5px; margin: 5px; }
input[type="submit"] { padding: 8px 15px; background: #007cba; color: white; border: none; cursor: pointer; }
</style>
